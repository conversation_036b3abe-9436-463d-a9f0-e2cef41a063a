<template>
    <CommonAccessDenied v-if="error?.statusCode === 403" :error="error" />
    <CommonError
        v-else-if="error?.statusCode !== HTTP_CODE.NOT_FOUND"
        :title="`Error: ${error?.statusCode}`"
        :message="error?.message"
    />
</template>

<script setup lang="ts">
import { HTTP_CODE } from '~/constants/api-status'
const { error } = defineProps({
    error: Object,
})

if (error?.statusCode === HTTP_CODE.NOT_FOUND) {
    clearError({ redirect: '/404' })
}
</script>
