<template>
    <div class="hero-banner">
        <div class="hero-banner__bg relative">
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/cho-tet/banner.png`"
                :srcMb="`${staticUrl}/cho-tet/banner-mb.png`"
                alt="banner"
            />
            <div class="hero-banner__time animate-blink">
                <div v-if="!isAfterEndDateHCM">
                    {{ $t('cho_tet.from') }}
                    {{ dayjs(TET_EVENT_START_DAY).format('DD/M') }}
                    {{ $t('cho_tet.to') }}
                    {{ dayjs(TET_EVENT_END_DAY).format('DD/M') }}
                </div>
                <div v-else>{{ $t('cho_tet.end') }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import {useCountdown} from '~/composables/common/use-countdown'

const staticUrl = useRuntimeConfig().public.staticUrl
const { TET_EVENT_START_DAY, TET_EVENT_END_DAY } = useRuntimeConfig().public

const { isAfterEndDateHCM } = useCountdown(
    TET_EVENT_START_DAY,
    TET_EVENT_END_DAY
)
</script>
<style lang="scss" scoped>
.hero-banner {
    &__bg {
        @media (max-width: 1850px) {
            @apply pb-[1vw];
        }
        @media (max-width: 1600px) {
            @apply pb-[3vw];
        }
        @media (max-width: 1400px) {
            @apply pb-[5vw];
        }
        @media (max-width: 1300px) {
            @apply pb-[5.5vw];
        }
        @media (max-width: 1200px) {
            @apply pb-[7vw];
        }
        @media (max-width: 992px) {
            @apply pb-[4vw];
        }
    }
    &__time {
        background: url('/assets/images/cho-tet/banner-title.png') center
            no-repeat;
        background-size: 100%;
        @apply absolute bottom-[3%] left-1/2 mx-auto flex h-[183px] w-[858px] -translate-x-1/2 items-end justify-center pb-[43px] text-center text-[28px] font-bold italic leading-[33px];
        @media (max-width: 992px) {
            background: url('/assets/images/cho-tet/banner-title-mb.png') center
                no-repeat;
            background-size: 100%;
            @apply h-[20vw] w-full max-w-full pb-[2.6vw] text-[3.2vw] leading-[3.75vw];
        }
    }
}
</style>
