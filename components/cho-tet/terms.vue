<template>
    <div class="terms container mt-[40px] max-w-[1462px] lg:mt-[112px]">
        <div @click="openGame(CHO_TET)" class="terms__game cursor-pointer">
            <img
                :src="`${staticUrl}/cho-tet/thumb.png`"
                alt="event"
                class="hidden lg:block"
            />
            <img
                :src="`${staticUrl}/cho-tet/thumb-mb.png`"
                alt="event"
                class="block lg:hidden"
            />
        </div>
        <div class="terms__txt">
            <div>
                <div class="mb-[2.7vw] lg:mb-5">
                    {{ $t('cho_tet.from_event') }}
                    <span>
                        {{
                            dayjs(TET_EVENT_START_DAY).format(
                                'HH:mm DD/MM/YYYY'
                            )
                        }}
                        {{ $t('cho_tet.to_event') }}
                        {{
                            dayjs(TET_EVENT_END_DAY).format('HH:mm DD/MM/YYYY')
                        }}
                    </span>
                    - {{ $t('cho_tet.opportunity') }}
                    <span>{{ $t('cho_tet.get_free') }}</span>
                    {{ $t('cho_tet.lucky') }}
                </div>
                <ul class="mb-[2.7vw] lg:mb-5">
                    <li>{{ $t('cho_tet.des_1') }}</li>
                    <li>
                        {{ $t('cho_tet.des_2_1') }}
                        <span>{{ $t('cho_tet.des_2_2') }}</span>
                    </li>
                    <li>{{ $t('cho_tet.des_3') }}</li>
                    <li>{{ $t('cho_tet.des_4') }}</li>
                </ul>
                <div class="mb-[5.4vw] lg:mb-6">{{ $t('cho_tet.noted') }}</div>
                <div
                    class="mx-auto max-w-[48vw] cursor-pointer transition-[0.3s] lg:mx-0 lg:max-w-[240px] lg:hover:opacity-80 xl:max-w-[288px]"
                    @click="openGame(CHO_TET)"
                >
                    <img :src="`${staticUrl}/cho-tet/btn.png`" alt="play" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { CHO_TET } from '~/constants/game'

const staticUrl = useRuntimeConfig().public.staticUrl
const { TET_EVENT_START_DAY, TET_EVENT_END_DAY } = useRuntimeConfig().public
const { openGame } = usePlayGame()
</script>
<style lang="scss" scoped>
.terms {
    @apply flex flex-col gap-3 lg:flex-row lg:gap-8;
    &__game {
        @apply transition-[0.3s] lg:w-[490px] lg:hover:opacity-80 xl:w-[578px];
    }
    &__txt {
        background: url('/assets/images/cho-tet/bg-note.png') center no-repeat;
        background-size: 100% 100%;
        @apply flex flex-1 items-center p-8 text-[3.2vw] font-normal leading-[3.8vw] lg:text-lg lg:leading-6;
        span {
            @apply font-bold;
        }
        ul {
            @apply list-disc pl-[20px];
            li {
                &:not(:last-child) {
                    @apply mb-1;
                }
            }
        }
        @media (max-width: 992px) {
            background: url('/assets/images/cho-tet/bg-note-mb.png') center
                no-repeat;
            background-size: 100% 100%;
            @apply px-[4vw] py-[4.9vw];
        }
    }
}
</style>
