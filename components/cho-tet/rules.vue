<template>
    <div class="rules container max-w-[1328px]">
        <div class="flex flex-col lg:flex-row lg:gap-20">
            <div class="rules__item">
                <img
                    :src="`${staticUrl}/cho-tet/award.png`"
                    alt="award"
                    class="hidden lg:block"
                />
                <img
                    :src="`${staticUrl}/cho-tet/award-mb.png`"
                    alt="award"
                    class="block lg:hidden"
                />
                <div class="rules__txt">
                    {{ $t('cho_tet.award') }}
                </div>
            </div>
            <div class="rules__item">
                <img
                    :src="`${staticUrl}/cho-tet/condition.png`"
                    alt="condition"
                    class="hidden lg:block"
                />
                <img
                    :src="`${staticUrl}/cho-tet/condition-mb.png`"
                    alt="condition"
                    class="block lg:hidden"
                />
                <div class="rules__txt">
                    {{ $t('cho_tet.condition', { brandName }) }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const staticUrl = useRuntimeConfig().public.staticUrl
const { BRAND_NAME: brandName } = useRuntimeConfig().public
</script>
<style lang="scss" scoped>
.rules {
    @media (max-width: 992px) {
        @apply px-0;
    }
    &__item {
        @apply relative flex items-center text-left text-[3.2vw] font-semibold italic leading-[4vw] lg:text-xl lg:leading-[26px];
    }
    &__txt {
        background: linear-gradient(
            90deg,
            rgba(157, 15, 14, 0) 0%,
            rgba(157, 15, 14, 0.6) 50%,
            rgba(157, 15, 14, 0) 100%
        );
        @apply absolute bottom-[5vw] right-[13.5vw] flex h-[18.3vw] max-w-[50vw] items-center px-2 lg:bottom-[60px] lg:right-[40px] lg:h-[102px] lg:max-w-[56.3%];
        @media (max-width: 1350px) and (min-width: 993px) {
            @apply bottom-[4vw];
        }
        @media (max-width: 1100px) and (min-width: 993px) {
            @apply bottom-[3.5vw];
        }
    }
}
</style>
