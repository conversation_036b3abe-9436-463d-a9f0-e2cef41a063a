<template>
    <div class="rule mb-[61px] mt-10 lg:mb-0 lg:mt-[5rem]">
        <div class="title-rule">
            <GoldenStarWarriosCommonTitle
                :title="t('top_event.rule.title')"
                :suggestion="t('top_event.rule.suggestion')"
            />
        </div>
        <div
            class="content-rule relative -ml-4 -mr-4 mt-3 flex w-[100vw] flex-col lg:mx-0 lg:mt-10 lg:w-auto lg:flex-row lg:[background:transparent]"
        >
            <div
                class="rules-box relative z-[2] order-2 mx-4 mb-[13px] mt-6 bg-[#111] px-4 py-6 lg:order-1 lg:mx-0 lg:mb-0 lg:mb-5 lg:mt-0 lg:h-[51.908125rem] lg:w-[58.1875rem] lg:pb-5 lg:pl-[1.75rem] lg:pr-[1rem] lg:pt-[1.75rem]"
            >
                <GoldenStarWarriosCommonRuleItem />
            </div>
            <div
                class="prizes order-1 flex lg:order-2 lg:mt-10 lg:h-[49.375rem] lg:w-[77.9375rem] lg:justify-end"
            >
                <div class="data-prizes w-full lg:w-[29.8125rem]">
                    <div class="prizes-content mt-5 lg:mt-[3.25rem]">
                        <div
                            class="prizes-title flex h-[5.5rem] w-full items-center gap-x-0 bg-[url('/assets/images/golden-star-warrios/rules/bg-title-mb.png')] pl-[1.5625rem] pr-6 lg:h-[7rem] lg:gap-x-4 lg:bg-[url('/assets/images/golden-star-warrios/rules/bg-title.png')] lg:px-[1.75rem]"
                        >
                            <img
                                :src="`${staticUrl}/golden-star-warrios/rules/icon.png`"
                                alt="icon"
                                class="size-[4.5rem] lg:size-[5.75rem]"
                            />
                            <div class="prizes-title-content">
                                <p
                                    class="title-reward mb-1 text-right text-xs font-medium uppercase lg:text-sm"
                                >
                                    {{ t('top_event.rule.prizes.title') }}
                                </p>
                                <p class="prizes-title-content__amount"></p>
                            </div>
                        </div>
                        <div class="prizes-table">
                            <div class="prizes-table-header">
                                <p class="prizes-table-header__item row--left">
                                    {{
                                        t(
                                            'top_event.rule.prizes.position_title'
                                        )
                                    }}
                                </p>
                                <p class="prizes-table-header__item row--right">
                                    {{
                                        t('top_event.rule.prizes.amount_title')
                                    }}
                                </p>
                            </div>
                            <div class="prizes-table-row">
                                <p class="prizes-table-row__left row--left">
                                    1
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    100,000,000 VND
                                </p>
                            </div>
                            <div class="prizes-table-row row--odd">
                                <p class="prizes-table-row__left row--left">
                                    2
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    50,000,000 VND
                                </p>
                            </div>
                            <div class="prizes-table-row">
                                <p class="prizes-table-row__left row--left">
                                    3
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    25,000,000 VND
                                </p>
                            </div>
                            <div class="prizes-table-row row--odd">
                                <p class="prizes-table-row__left row--left">
                                    4-6
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    10,000,000 VND
                                </p>
                            </div>
                            <div class="prizes-table-row">
                                <p class="prizes-table-row__left row--left">
                                    7-10
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    5,000,000 VND
                                </p>
                            </div>
                            <div class="prizes-table-row row--odd">
                                <p class="prizes-table-row__left row--left">
                                    11-20
                                </p>
                                <p class="prizes-table-row__right row--right">
                                    1,500,000 VND
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const staticUrl = useRuntimeConfig().public.staticUrl
</script>

<style scoped lang="scss">
.rules-box {
    position: relative;
    border-radius: 16px 16px 0 16px;
    background-color: #111;
    overflow: hidden;
    &::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: linear-gradient(
            127.3deg,
            #ffd55b -0.26%,
            rgba(255, 213, 91, 0) 66.84%
        );
        border-radius: 20px 20px 0 20px;
        z-index: -1;
    }
    &::after {
        content: '';
        position: absolute;
        top: 3px;
        left: 3px;
        right: 3px;
        bottom: 3px;
        background-color: #111;
        border-radius: 13px 13px 0 13px;
        z-index: -1;
    }
    @include mba() {
        border-radius: 16px;
        border: 1.5px solid #ccb46d;
        &::before,
        &::after {
            content: none;
        }
    }
}
.prizes {
    @apply bg-center bg-no-repeat lg:absolute lg:bottom-0 lg:right-0 lg:z-[1] lg:rounded-3xl lg:bg-[url('/assets/images/golden-star-warrios/rules/bg.jpg')];
    background-size: 100% 100%;

    .prizes-title {
        @apply flex items-center;

        &__cup {
            @apply ml-4 h-[140px] w-[64px];
        }

        .prizes-title-content {
            @apply flex h-full flex-col justify-center;
            &__amount {
                @apply aspect-[253/27] w-[15.8125rem] bg-[url('/assets/images/golden-star-warrios/rules/amount-mb.png')] bg-contain bg-right bg-no-repeat lg:aspect-[312/37] lg:w-[19.5rem] lg:bg-[url('/assets/images/golden-star-warrios/rules/amount-pc.png')];
            }
        }
    }

    .prizes-table {
        @apply mx-4 mt-3 overflow-hidden rounded-lg lg:mx-[1.75rem] lg:mt-8;

        .prizes-table-row {
            @apply flex h-[3.125rem] items-center [background:rgba(255,255,255,0.13)];

            &.row--odd {
                @apply [background:rgba(255,255,255,0.3)];
            }
        }

        .row--left,
        .row--right {
            @apply flex items-center justify-center text-base font-semibold text-white;
        }

        .row--left {
            @apply relative w-[40.65%];

            &::before {
                @apply absolute right-[0] top-[-10px] h-[40.25px] w-[1px] content-[''] [background:rgba(255,255,255,0.2)];
            }
        }
        .row--right {
            @apply w-[59.35%];
        }

        .prizes-table-header {
            @apply flex h-[3.125rem] items-center [background:rgba(255,255,255,0.7)];

            &__item {
                @apply flex items-center justify-center text-sm font-semibold text-black lg:text-base;
            }
            .row--left,
            .row--right {
                @apply text-black;
            }
        }
    }
}
.title-reward {
    background: linear-gradient(180deg, #ffffff 0%, #e0dfdf 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}
.content-rule {
    @include mba() {
        background: linear-gradient(
            220.8deg,
            #4d2704 23.06%,
            #7d550f 34.06%,
            #220305 66.12%
        );
    }
}
</style>
