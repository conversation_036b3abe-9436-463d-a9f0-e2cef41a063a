<template>
    <div class="top-number">
        <div class="top-number__contain">
            <template v-if="top >= 1 && top <= 3">
                <img
                    class="top-number__top"
                    :src="`${staticUrl}/golden-star-warrios/common/rankings/top_${top}.png`"
                    alt="Top"
                />
            </template>
            <template v-else>
                <div class="top-number__stt">
                    <div class="top-number__text">{{ top }}</div>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
const staticUrl = useRuntimeConfig().public.staticUrl
defineProps({
    top: {
        type: Number,
        default: 1,
    },
})
</script>
<style lang="scss" scoped>
.top-number {
    &__top {
        @apply flex size-[40px] items-center justify-center object-contain [background-size:100%];
        @media (min-width: 700px) {
            @apply h-[40px] w-[40px];
        }
        @media (max-width: 374px) {
            @apply h-[24px] w-[24px];
        }
    }
    &__stt {
        @apply flex size-[29.57px] items-center justify-center bg-contain bg-right bg-no-repeat text-sm leading-[20px];
        background-image: url(/assets/images/golden-star-warrios/common/rankings/top_bg.svg);
        @media (min-width: 700px) {
            @apply h-[30px] w-[30px];
        }
        @media (max-width: 374px) {
            @apply h-[24px] w-[24px];
        }
    }
    &__text {
        @apply flex-[1] text-center font-['Montserrat'] text-sm font-extrabold italic leading-[20px] text-[#f2faff] [text-shadow:0px_0.836365px_0.836365px_rgba(0,_0,_0,_0.25)];
        @media (max-width: 374px) {
            @apply text-[9px];
        }
    }
}
</style>
