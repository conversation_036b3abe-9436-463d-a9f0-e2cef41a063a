<template>
    <div class="table-item" v-if="!isMobile">
        <div :class="['table-item__contain', { active: isActivated }]">
            <div class="table-item__inner">
                <GoldenStarWarriosCommonRankingsTopNumber :top="rankNumber" />
            </div>
            <div class="table-item__inner">{{ user.username }}</div>
            <div class="table-item__inner">{{ user.game }}</div>
            <div class="table-item__inner" v-if="isShowMoney">
                {{
                    NumberUtils.formatMoneyWithAmount(
                        Number(
                            user?.stake &&
                                !isNaN(user?.stake)
                                ? user?.stake?.toFixed()
                                : 0
                        ),
                        1,
                        CURRENCY.D
                    )
                }}
            </div>
            <div class="table-item__inner" v-else>*********** D</div>
            <div class="table-item__inner">
                <span>{{
                    user?.amount
                        ? NumberUtils.formatMoneyWithAmount(
                              Number(user.amount),
                              1,
                              CURRENCY.VND
                          )
                        : `${prizesFiltered} VND`
                }}</span>
            </div>
        </div>
    </div>
    <div class="table-item table-item-mb" v-else>
        <div :class="['table-item-mb__contain', { active: isActivated }]">
            <div class="flex items-center">
                <div class="table-item-mb__inner">
                    <GoldenStarWarriosCommonRankingsTopNumber :top="rankNumber" />
                </div>
                <div class="flex-grow-1 ml-2 flex flex-col items-start">
                    <div class="table-item-mb__inner table-item-mb__username">
                        {{ user.username }}
                    </div>
                    <div class="table-item-mb__inner" v-if="!isShowMoney">
                        *********** D
                    </div>
                    <div class="table-item-mb__inner" v-else-if="user?.is_null">
                        {{ user?.winlost }}
                    </div>
                    <div class="table-item-mb__inner" v-else>
                        {{
                            NumberUtils.formatMoneyWithAmount(
                                Number(
                                    user?.stake &&
                                        !isNaN(user?.stake)
                                        ? user?.stake?.toFixed()
                                        : 0
                                ),
                                1,
                                CURRENCY.D
                            )
                        }}
                    </div>

                </div>
            </div>
            <div class="flex flex-col justify-end items-end">
                <div class="table-item-mb__inner">
                    <span
                        >{{
                            user?.amount
                                ? NumberUtils.formatMoneyWithAmount(
                                      Number(user.amount),
                                      1,
                                      CURRENCY.VND
                                  )
                                : `${prizesFiltered} VND`
                        }}
                    </span>
                </div>
                <!-- <div class="table-item-mb__inner table-item-mb__game">
                    {{ user.game }}
                </div> -->
            </div>
            <div v-if="rankNumber <= 3" class="absolute right-0 top-0 w-[53px] h-[23px]">
                <img
                    class="w-[53px] h-[23px] object-contain"
                    :src="`${staticUrl}/golden-star-warrios/ranking/top${rankNumber}.png`"
                    alt="Top"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NumberUtils } from '~/utils'
import type { UserRanking } from '~/composables/user/interface'
import { rankingPrizes } from '~/resources/olympic-2024/ranking-prizes'
import { CURRENCY } from '~/constants/common'

const staticUrl = useRuntimeConfig().public.staticUrl

const props = defineProps({
    user: {
        type: Object as PropType<UserRanking>,
        default: () => {},
    },
    rankNumber: {
        type: Number,
        default: 0,
    },
    isActivated: {
        type: Boolean,
        default: false,
    },
    isShowMoney: {
        type: Boolean,
        default: false,
    },
    isMobile: {
        type: Boolean,
        default: false,
    },
})

const groupedPrizeById = computed(() => {
    return Object.assign(
        {},
        ...rankingPrizes.map((prize) => ({ [prize.id]: prize.value }))
    )
})

const prizesFiltered = computed(() => {
    if (props.rankNumber >= 1 && props.rankNumber <= 10) {
        return groupedPrizeById.value[props.rankNumber]
    }
    return rankingPrizes[11].value
})
</script>

<style lang="scss" scoped>
.table-item {
    @apply relative rounded-lg overflow-hidden bg-[rgba(255,_255,_255,_0.08)];

    &__icon {
        @apply absolute -left-[3px] top-2/4 h-[46px] w-[16px] -translate-y-1/2 transform;
    }

    &__contain {
        @apply grid grid-cols-[130px_repeat(4,_1fr)] px-[0] py-[6px] lg:h-[3.25rem] lg:text-center;
        &.active {
            @apply rounded-lg border-[2px] border-solid border-[#FFD55B] bg-[rgba(0,_0,_0,_0.12)];
        }
    }

    &__inner {
        @apply flex items-center justify-center p-0 text-base font-bold leading-[20px] text-white;

        span {
            @apply text-base font-bold not-italic leading-[calc(24/16)] text-[#FFD55B];
        }

        &:not(:first-child) {
            @apply [border-left:1px_solid_rgba(255,_255,_255,_0.2)];
        }
    }
}

.table-item-mb {
    &__contain {
        @apply relative grid h-[4rem] grid-cols-[repeat(2,_1fr)] p-3 text-center;
        &.active {
            @apply rounded-lg border-[2px] border-solid border-[#BB87FF] bg-[rgba(0,_0,_0,_0.12)];
        }
    }
    &__inner {
        @apply flex min-w-[38px] items-center justify-center px-[0] text-xs leading-[19px] text-white;
        @media (max-width: 374px) {
            @apply min-w-[24px];
        }
        span {
            @apply text-sm font-bold not-italic leading-[19px] text-[#FFD55B];
        }
        &:not(:first-child):not(:last-child) {
            @apply [border-left:1px_solid_rgba(255,_255,_255,_0.2)] [border-right:1px_solid_rgba(255,_255,_255,_0.2)];
        }
    }
    &__game,
    &__username {
        @apply text-xs leading-[15px] font-semibold;
    }
    &__game {
        @apply text-right;
    }
}
</style>
