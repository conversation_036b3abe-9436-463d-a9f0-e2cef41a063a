<template>
    <section class="sports">
        <div :class="['slider', isClient ? 'client' : 'server']">
            <Swiper
                effect="fade"
                :modules="[EffectCards, EffectCreative, FreeMode]"
                slidesPerView="auto"
                :slidesPerGroup="1"
                :spaceBetween="6"
                :breakpoints="{
                    1024: {
                        spaceBetween: 12,
                    },
                }"
            >
                <SwiperSlide
                    v-for="sport in SPORT_LIST_EVENTS"
                    :key="sport.type"
                    @click="handleRedirectSport(sport)"
                >
                    <CommonImage
                        :src="isDeskTop ? sport.imgPc : sport.imgMb"
                        :alt="`${t(sport.title)}`"
                        class="h-[11.625rem] w-[10.25rem] object-cover lg:h-[10rem] lg:w-[19.4375rem]"
                    />
                </SwiperSlide>
            </Swiper>
        </div>
    </section>
</template>

<script setup lang="ts">
import { EffectCreative, FreeMode, EffectCards } from 'swiper/modules'
import { SPORT_LIST_EVENTS } from '~/resources/sport'
import type { ISport } from '~/interfaces/sport'

const { openSport } = usePlayGame()
const { isDeskTop } = useWindowSize()
const { t } = useI18n()
const isClient = ref(false)

const handleRedirectSport = async (sport: ISport) => {
    try {
        openSport({
            loginRequired: sport?.loginRequired || false,
            type: sport.type.toString(),
            newTab: !isDeskTop,
            link: sport.url,
            apiUrl: sport.apiUrl,
            isMainApi: true,
        })
    } catch (error) {
        alert(t('error.maintain'))
        return
    }
}

onMounted(() => {
    nextTick(() => {
        if (import.meta.client) {
            isClient.value = true
        }
    })
})
</script>

<style lang="scss" scoped>
.sports {
    .slider {
        :deep(.swiper) {
            @apply -mr-4 lg:m-0;
        }
        :deep(.swiper-slide) {
            @apply mr-1.5 lg:mr-3 h-[11.625rem] w-[10.25rem]  cursor-pointer lg:h-[10rem] lg:w-[19.4375rem];
            &:hover {
                @apply brightness-125;
            }
            &:last-child {
                @include mba() {
                    margin-right: 16px !important;
                }
            }
        }
        &.client {
            :deep(.swiper-slide) {
                @apply mr-0;
            }
        }
    }
}
</style>
