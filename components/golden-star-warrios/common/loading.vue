<template>
    <div class="lds-roller">
        <div v-for="n in 8" :key="n"></div>
    </div>
</template>
<style lang="scss" scoped>
@keyframes lds-roller {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.lds-roller {
    @apply relative inline-block h-20 w-20;

    div {
        animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
        transform-origin: 40px 40px;
        &:after {
            @apply absolute block h-[7px] w-[7px] rounded-full bg-white content-[''];
        }

        &:nth-child(1) {
            @apply [animation-delay:-0.036s];

            &:after {
                @apply left-[63px] top-[63px];
            }
        }

        &:nth-child(2) {
            @apply [animation-delay:-0.072s];

            &:after {
                @apply left-[56px] top-[68px];
            }
        }

        &:nth-child(3) {
            @apply [animation-delay:-0.108s];

            &:after {
                @apply left-[48px] top-[71px];
            }
        }

        &:nth-child(4) {
            @apply [animation-delay:-0.144s];

            &:after {
                @apply left-[40px] top-[72px];
            }
        }

        &:nth-child(5) {
            @apply [animation-delay:-0.18s];

            &:after {
                @apply left-[32px] top-[71px];
            }
        }

        &:nth-child(6) {
            @apply [animation-delay:-0.216s];

            &:after {
                @apply left-[24px] top-[68px];
            }
        }

        &:nth-child(7) {
            @apply [animation-delay:-0.252s];

            &:after {
                @apply left-[17px] top-[63px];
            }
        }

        &:nth-child(8) {
            @apply [animation-delay:-0.288s];

            &:after {
                @apply left-[12px] top-[56px];
            }
        }
    }
}
</style>
