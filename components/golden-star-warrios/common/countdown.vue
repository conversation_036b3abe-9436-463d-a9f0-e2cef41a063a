<template>
    <div :class="className">
        <div
            v-if="!isAfterEndDate && !isFinish"
            :class="[
                'countdown-top',
                isAfterStartDate ? 'is-start' : '',
                isAfterEndDate ? 'is-end' : '',
            ]"
        >
            <div
                :class="[
                    'countdown-top__content',
                    isAfterStartDate ? 'remaining' : '',
                ]"
            >
                <div
                    class="countdown-title text-[9px] font-medium text-white lg:text-base"
                >
                    <p>
                        {{
                            !isAfterStartDate
                                ? `${t('common.countdown.start_at')}:`
                                : `${t('common.countdown.remaining')}:`
                        }}
                    </p>
                </div>
                <div
                    class="countdown-list flex items-center gap-x-1 text-white lg:min-w-[13.9375rem] lg:gap-x-1"
                >
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cDays) }}</p>
                        <p class="text">{{ t('common.countdown.date') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cHours) }}</p>
                        <p class="text">{{ t('common.countdown.hour') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cMinutes) }}</p>
                        <p class="text">{{ t('common.countdown.minute') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div
            v-else
            class="is-finish absolute flex items-center justify-center lg:right-[17%] lg:top-[33.8%] lg:w-[28.125%]"
        >
            <div class="countdown">
                {{ t('common.end_event') }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useCountdown } from '~/composables/common/use-countdown'

const { t } = useI18n()

defineProps({
    className: {
        type: String,
        default: '',
    },
})
const {
    GOLDEN_STAR_WARRIORS_EVENT_START_DAY,
    GOLDEN_STAR_WARRIORS_EVENT_END_DAY,
} = useRuntimeConfig().public
const {
    isFinish,
    isAfterEndDate,
    isAfterStartDate,
    cDays,
    cHours,
    cMinutes,
    formatTimer,
} = useCountdown(
    GOLDEN_STAR_WARRIORS_EVENT_START_DAY,
    GOLDEN_STAR_WARRIORS_EVENT_END_DAY
)
</script>

<style scoped lang="scss" rel="preload">
.countdown-top {
    @apply absolute left-1 top-[58%] flex items-center justify-center xs:left-[5%] sm:left-[10%] lg:left-auto lg:right-[20.4%] lg:top-[60%] lg:w-[21.875%];
    &.is-start {
        @apply left-[15px] xs:left-[4%] sm:left-[10%] lg:left-auto;
    }
    &.is-end {
        @apply left-[43px] xs:left-[3%] sm:left-[12%] lg:left-auto;
    }
    &__content {
        @apply flex h-[24.75px] min-w-[178.2px] items-center justify-center gap-x-[5.94px] rounded border-[0.83px] border-solid border-[#FFFFFF33] bg-[#EDA0A066] pl-2 pr-1 lg:right-[17%] lg:top-[24.8%] lg:h-[3.125rem] lg:min-w-[21.875rem] lg:gap-x-2.5 lg:rounded-lg;
        backdrop-filter: blur(10px);
        &.remaining {
            @apply w-[11.375rem] lg:w-[23.9rem];
        }
    }
}
.countdown {
    @apply h-[2.25rem] w-[12.25rem] rounded-[6px] border border-solid border-[#FFFFFF33] bg-[#EDA0A066] text-sm font-bold text-white backdrop-blur-md lg:h-[3.25rem] lg:w-[16.1875rem] lg:rounded-[8.57px] lg:text-lg;
}
.countdown-item {
    @apply flex items-center gap-x-[1.98px] lg:gap-x-1;
    .time {
        @apply flex size-5 items-center justify-center rounded bg-[#00000080] text-[10px] font-bold shadow-[0px_0px_5.71px_0px_#00000040] lg:size-[1.98rem] lg:rounded-[0.356875rem] lg:text-base;
    }
    .text {
        @apply text-[8px] font-semibold lg:text-base;
    }
}
.is-finish {
    @include mba() {
        @apply bottom-4 left-2/4 -translate-x-2/4;
    }
}
</style>
