<template>
    <div v-show="!isLoading" class="start-event">
        <div class="start-event__container">
            <img
                :src="`${staticUrl}/golden-star-warrios/ranking/no-data.png`"
                alt="olympic-2024"
                class="start-event__img"
            />
            <div
                :class="`start-event__title ${
                    isAfterRoundEvent ? 'no-data' : ''
                }`"
            >
                {{ isAfterRoundEvent ? 'Chưa có dữ liệu.' : 'Sắp bắt đầu...' }}
            </div>
            <div
                v-if="!isAfterRoundEvent && !isFinish"
                class="start-event__countdown"
            >
                {{ formattedDays }}
                <span> {{ t('common.countdown.date') }} </span>
                {{ formattedHours }}
                <span> {{ t('common.countdown.hour') }} </span>
                {{ formattedMinutes }}
                <span> {{ t('common.countdown.minute') }} </span>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { useCountdown } from '~/composables/common/use-countdown'

const { t } = useI18n()
const {
    staticUrl,
    GOLDEN_STAR_WARRIORS_EVENT_START_DAY,
    GOLDEN_STAR_WARRIORS_EVENT_END_DAY,
} = useRuntimeConfig().public

const props = defineProps({
    empty: {
        type: Boolean,
        default: false,
    },
    startTimeEvent: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['finish-time'])

const isAfterRoundEvent = ref(false)

const isLoading = ref(true)
const cDays = ref(0)
const cHours = ref(0)
const cMinutes = ref(0)
const cSeconds = ref(0)
const isFinish = ref(false)

const formattedDays = computed(() => cDays.value.toString().padStart(2, '0'))
const formattedHours = computed(() => cHours.value.toString().padStart(2, '0'))
const formattedMinutes = computed(() =>
    cMinutes.value.toString().padStart(2, '0')
)

const count = ref()

const {getServerTime, serverTime} = useCountdown(
    GOLDEN_STAR_WARRIORS_EVENT_START_DAY,
    GOLDEN_STAR_WARRIORS_EVENT_END_DAY
)

const countdownStartTime = (dateEnd: number) => {
    count.value = setInterval(() => {
        const now = getServerTime().valueOf()
        const distance = dateEnd - now
        convertTimeCountdown(distance)
        if (distance === 0) {
            isAfterRoundEvent.value = dayjs(getServerTime()).isAfter(
                props.startTimeEvent,
                'second'
            )
            clearInterval(count.value)
            isFinish.value = true
        }
    }, 1000)
}

watch(
    () => isFinish.value,
    (finished) => {
        if (finished) {
            clearInterval(count.value)
            emit('finish-time')
        }
    }
)

const convertTimeCountdown = (distance: number) => {
    cDays.value = Math.max(0, Math.floor(distance / (1000 * 60 * 60 * 24)))
    cHours.value = Math.max(
        0,
        Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    )
    cMinutes.value = Math.max(
        0,
        Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    )
    cSeconds.value = Math.max(0, Math.floor((distance % (1000 * 60)) / 1000))
}

watch(
    () => props.startTimeEvent,
    (time) => {
        if (!isAfterRoundEvent.value && import.meta.client) {
            clearInterval(count.value)
            countdownStartTime(new Date(time).getTime())
        }
        setTimeout(() => (isLoading.value = false), 500)
    },
    { immediate: true }
)

watch(() => serverTime, () => {
    isAfterRoundEvent.value = dayjs(getServerTime()).isAfter(props.startTimeEvent, 'second')
})
</script>

<style lang="scss" scoped>
.start-event {
    &__container {
        @apply flex flex-col items-center;
    }

    &__img {
        @apply mb-2 h-[8.75rem] w-[8.75rem];
    }

    &__title {
        @apply text-sm font-semibold text-white opacity-70 lg:text-base;

        &.no-data {
            @apply text-sm font-semibold lg:text-base;
        }
    }

    &__countdown {
        @apply mx-auto my-6 text-base font-bold text-white lg:text-xl;

        span {
            @apply text-base font-semibold lg:text-lg;
        }
    }
}
</style>
