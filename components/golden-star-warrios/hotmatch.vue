<template>
    <div v-if="hotMatch?.data?.length" class="hotmatch-list mt-10 lg:mt-[5rem]">
        <TopEventCommonTitle
            :title="$t('top_event.hotmatch.title')"
            :suggestion="$t('top_event.hotmatch.suggestion')"
        />
        <div :class="['slider mt-6 lg:mt-10', isClient ? 'client' : 'server']">
            <Swiper
                effect="fade"
                :modules="[EffectCards, EffectCreative, FreeMode]"
                slidesPerView="auto"
                :slidesPerGroup="1"
                :allowTouchMove="true"
                :spaceBetween="8"
                :breakpoints="{
                    992: {
                        spaceBetween: 12,
                    },
                }"
            >
                <SwiperSlide v-for="item in hotMatch?.data" :key="item.type">
                    <HomeHotMatchItem :item="item" />
                </SwiperSlide>
            </Swiper>
        </div>
    </div>
</template>

<script setup lang="ts">
import { EffectCreative, EffectCards, FreeMode } from 'swiper/modules'
import { useHotMatchStore } from '~/composables/home/<USER>'
const useHotMatchInstance = useHotMatchStore()
const { hotMatch } = storeToRefs(useHotMatchInstance)

const isClient = ref(false)

onMounted(() => {
    nextTick(() => {
        if (import.meta.client) {
            isClient.value = true
        }
    })
})
</script>

<style scoped lang="scss">
:deep(.swiper) {
    @apply -mr-4 lg:m-0;
}
:deep(.swiper-slide) {
    @apply h-[6.875rem] w-[21.4375rem] lg:mr-3 lg:h-[7.6875rem] lg:w-[27rem];
}
.client {
    :deep(.swiper-slide) {
        @apply mr-0;
    }
}
</style>
