<template>
    <div class="rankings">
        <div class="rankings__container">
            <div class="rankings-head">
                <div
                    v-if="selectedFilterWeekend.isEnd"
                    class="rankings-head-end"
                >
                    {{ t('event.status.finished') }}
                </div>
                <div class="rankings-head-charts">
                    <img
                        :src="`${staticUrl}/golden-star-warrios/pc/rank-text.png`"
                        alt="Bang xep hang"
                        class="rankings-head-charts__top"
                    />
                    <p>{{ t('event.total_amount') }} {{ week }}</p>
                    <div class="rankings-head-charts__amount">
                        <div
                            class="amount flex gap-x-1"
                            v-if="!isBeforeStartEvent && !!user && topStake > 0"
                        >
                            <CommonAnimatedNumber
                                className="!text-white"
                                :number="topStake"
                            />
                            <span>{{ t('event.unit') }}</span>
                        </div>
                        <div class="amount no-data" v-else>
                            <span>***********</span>
                            <span>{{ t('event.unit') }}</span>
                        </div>
                    </div>
                </div>
                <div class="rankings-head-filter ranking-golden-star-warrios">
                    <CommonDropdownFilterWeek
                        :options="filteredWeekendList"
                        :title="selectedFilterWeekend?.title || ''"
                        :defaultActive="activeFilter"
                        @click-filter="handleFilterWeekend"
                    />
                </div>
            </div>
            <div
                :class="[
                    'rankings__body',
                    isBeforeStartEvent || !fillUsersWinLost.length
                        ? 'no-footer bg-[#20096C]'
                        : '',
                ]"
            >
                <div v-if="isBeforeStartEvent" class="rankings__body-inner">
                    <GoldenStarWarriosCommonStartEvent
                        @finishTime="handleFinish"
                        :startTimeEvent="startTimeEvent"
                    />
                </div>
                <template v-else>
                    <div v-if="isLoadingWinLost" class="rankings__body-inner">
                        <GoldenStarWarriosCommonLoading />
                    </div>
                    <div
                        v-else-if="
                            !isLoadingWinLost && fillUsersWinLost.length !== 0
                        "
                        class="rankings__list"
                    >
                        <GoldenStarWarriosCommonRankingsTable
                            :users="fillUsersWinLost"
                            :isShowMoney="!!user"
                            :position="
                                userPosition?.position
                                    ? Number(userPosition?.position)
                                    : 0
                            "
                        />
                    </div>
                    <div class="rankings__body-inner" v-else>
                        <GoldenStarWarriosCommonStartEvent
                            :startTimeEvent="startTimeEvent"
                            @finish-time="handleFinish"
                        />
                    </div>
                </template>
                <div
                    class="rankings__footer"
                    v-if="
                        !isBeforeStartEvent &&
                        !isLoadingWinLost &&
                        fillUsersWinLost.length
                    "
                >
                    <GoldenStarWarriosCommonCurrentTop
                        :username="user?.username"
                        :userPosition="userPosition"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useGoldenStarWarrios } from '~/composables/use-golden-star-warrios'

const { t } = useI18n()
const {
    week,
    selectedFilterWeekend,
    startTimeEvent,
    filteredWeekendList,
    activeFilter,
    isBetweenDate,
    topStake,
    userPosition,
    isLoadingWinLost,
    isBeforeStartEvent,
    fillUsersWinLost,
    handleFilterWeekend,
    handleFinish,
} = useGoldenStarWarrios()
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)
const staticUrl = useRuntimeConfig().public.staticUrl
</script>

<style lang="scss" scoped>
.rankings {
    @apply mb-[2.5rem];
    &__container {
        @apply relative rounded-xl bg-[linear-gradient(180deg,_#3C0800_0%,_#220305_26.93%)];

        .amount {
            @apply whitespace-nowrap bg-clip-text text-3xl font-bold capitalize leading-none text-white;

            span {
                &:first-child {
                    @apply font-montserrat text-3xl;
                }

                &:last-child {
                    @apply mt-1 font-montserrat text-sm text-white;
                }
            }

            &.no-data {
                @apply flex items-center gap-x-[4px];

                span {
                    &:first-child {
                        @apply mt-[10px] flex items-center text-3xl;
                    }

                    &:last-child {
                        @apply leading-[24px];
                    }
                }
            }
        }
    }

    .rankings-head {
        @apply relative h-[8.0625rem];

        .rankings-head-charts {
            background-image: url(/assets/images/golden-star-warrios/ranking/top-ranking-pc.png);
            background-position: center;
            @apply absolute -top-[12px] left-2/4 h-[8.75rem] w-[43.5625rem] -translate-x-1/2 transform bg-contain bg-no-repeat text-center;

            &__amount {
                @apply absolute bottom-[2.625rem] left-2/4 h-[35px] -translate-x-1/2 transform overflow-hidden;

                p {
                    @apply flex min-w-[300px] items-center whitespace-nowrap text-right text-3xl font-bold capitalize leading-[30px] text-white;

                    span {
                        @apply flex items-center text-sm font-bold capitalize leading-[20px] text-white;
                        text-shadow: 0px 1px 4px 0px #00000040;
                    }
                }
            }

            img {
                @apply relative -top-[20px] mx-[auto] my-[0] w-[382px];
            }

            p {
                &:nth-child(2) {
                    @apply absolute bottom-[5rem] left-2/4 -translate-x-1/2 transform text-center text-base font-semibold leading-[20px] text-white;
                }
            }
        }
    }

    .rankings-head-end {
        @apply absolute left-[1.25vw] top-[1.35vw] text-xl font-bold leading-[24px];
    }

    .rankings-head-filter {
        @apply absolute right-[24px] top-[19px];

        @media (max-width: 1230px) {
            @apply right-[15px] top-[50px];
        }
        &.ranking-golden-star-warrios {
            :deep(.dropdown-simple) {
                .dropdown-body {
                    .dropdown-header,
                    .dropdown-menu {
                        @apply rounded-md bg-[#461B15];

                        .dropdown-item {
                            @apply border-0 text-sm font-medium leading-[20px] text-[#FFFFFF];
                            &:first-child {
                                @apply rounded-t-md;
                            }
                            &:last-child {
                                @apply rounded-b-md border-b-0;
                            }
                            &.active,
                            &:hover {
                                @apply border-b border-b-[#FFFFFF1A] bg-[#FFFFFF1A] text-sm font-medium leading-[20px] text-[#FFFFFF];
                            }
                        }
                    }
                }
            }
        }
    }

    &__body {
        @apply relative min-h-[520px] rounded-bl-2xl rounded-br-2xl rounded-tl-lg rounded-tr-lg bg-[linear-gradient(180deg,_#573003_0%,_rgba(87,_48,_3,_0.2)_89.53%)] pb-6 pl-[10px] pr-[3px];

        &:before {
            @apply absolute left-[0] top-[0] h-[31px] w-full bg-[linear-gradient(180deg,_rgba(255,_255,_255,_0.12)_0%,_rgba(0,_0,_0,_0.0264)_110.2%)] content-[''];
        }

        &.no-footer {
            @apply h-[42.25rem] bg-transparent;

            &:before {
                @apply content-[none];
            }
        }

        @media (min-width: 1024px) {
            @apply pb-6 pl-6 pr-[3px] pt-[0];
        }
    }
    &__list {
        :deep(.rank-table__container) {
            .rank-table__body {
                @apply pr-[1.0625rem];
            }
        }
    }

    &__body-inner {
        @apply grid place-content-center [min-height:inherit];
    }

    &__footer {
        @apply rounded-bl-[16px] rounded-br-[16px] rounded-tl-[0] rounded-tr-[0] px-2 pb-[0] pr-6 pt-3;
    }

    &__head-bg {
        @apply absolute left-2/4 top-[0] h-[6.15vw] w-full -translate-x-1/2 transform;
    }
}
</style>
