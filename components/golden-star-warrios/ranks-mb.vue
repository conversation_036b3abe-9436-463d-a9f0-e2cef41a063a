<template>
    <div class="rankings">
        <div
            :class="[
                'rankings-container',
                {
                    'has-data':
                        !isBeforeStartEvent && !!fillUsersWinLost.length,
                },
            ]"
        >
            <div class="rankings-head">
                <div class="rankings-head-charts">
                    <img
                        :src="`${staticUrl}/golden-star-warrios/sp/rank-text.png`"
                        alt="Bang xep hang"
                        class="rankings-head-charts__top"
                    />
                    <p
                        class="text-xs font-semibold leading-[19.6px] lg:text-base"
                    >
                        {{ t('event.total_amount') }} {{ week }}
                    </p>
                    <div class="rankings-head-charts__amount">
                        <div class="amount" v-if="!!user && isBetweenDate">
                            <CommonAnimatedNumber
                                :number="topStake"
                                className="!text-white"
                            />
                            <span>{{ t('event.unit') }}</span>
                        </div>
                        <div class="amount no-data" v-else>
                            <span>***********</span>
                            <span>{{ t('event.unit') }}</span>
                        </div>
                    </div>
                </div>
                <div class="rankings-filter">
                    <div
                        v-if="selectedFilterWeekend?.isEnd"
                        class="rankings-head-end"
                    >
                        {{ t('event.status.finished') }}
                    </div>
                    <div v-else class="rankings-filter__unit">
                        <p>{{ t('event.unit_convert') }}</p>
                    </div>
                    <div
                        class="rankings-filter__date ranking-golden-star-warrios"
                    >
                        <CommonDropdownFilterWeek
                            :options="filteredWeekendList"
                            :title="selectedFilterWeekend?.title || ''"
                            :defaultActive="activeFilter"
                            @click-filter="handleFilterWeekend"
                        />
                    </div>
                </div>
            </div>
            <div
                :class="[
                    'rankings__body',
                    isBeforeStartEvent || !fillUsersWinLost.length
                        ? 'no-footer'
                        : '',
                ]"
            >
                <div
                    v-if="isBeforeStartEvent"
                    class="rankings__body-inner !flex !items-start !justify-center"
                >
                    <GoldenStarWarriosCommonStartEvent
                        @finishTime="handleFinish"
                        :startTimeEvent="startTimeEvent"
                    />
                </div>
                <template v-else>
                    <div v-if="isLoadingWinLost" class="rankings__body-inner">
                        <GoldenStarWarriosCommonLoading />
                    </div>
                    <div
                        v-else-if="
                            !isLoadingWinLost && fillUsersWinLost?.length !== 0
                        "
                        class="rankings__list"
                    >
                        <!-- In Event -->
                        <GoldenStarWarriosCommonRankingsTable
                            :users="fillUsersWinLost"
                            :isShowMoney="!!user"
                            :position="
                                userPosition?.position
                                    ? Number(userPosition?.position)
                                    : 0
                            "
                        />
                    </div>
                    <div class="rankings__body-inner" v-else>
                        <!-- UI Empty -->
                        <GoldenStarWarriosCommonStartEvent
                            :startTimeEvent="startTimeEvent"
                            @finishTime="handleFinish"
                        />
                    </div>
                </template>
                <div
                    class="rankings__footer"
                    v-if="
                        !isBeforeStartEvent &&
                        !isLoadingWinLost &&
                        fillUsersWinLost.length
                    "
                >
                    <GoldenStarWarriosCommonCurrentTop
                        :username="user?.username"
                        :userPosition="userPosition"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useGoldenStarWarrios } from '~/composables/use-golden-star-warrios'

const { t } = useI18n()
const {
    week,
    selectedFilterWeekend,
    filteredWeekendList,
    activeFilter,
    isBetweenDate,
    topStake,
    userPosition,
    startTimeEvent,
    isLoadingWinLost,
    isBeforeStartEvent,
    fillUsersWinLost,
    handleFilterWeekend,
    handleFinish,
} = useGoldenStarWarrios()
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)
const staticUrl = useRuntimeConfig().public.staticUrl
</script>

<style lang="scss" scoped>
.rankings {
    @apply mb-3 mt-[3.6rem];
    .rankings-container {
        @apply relative mx-[auto] my-[0] rounded-xl bg-[#3c0800];
        &.has-data {
            background: linear-gradient(
                355.53deg,
                rgba(34, 3, 5, 0.9) 5.18%,
                rgba(60, 8, 0, 0.9) 74.05%
            );
        }
    }
    .rankings-head {
        @apply relative h-[8.875rem];

        .rankings-head-charts {
            background: url(/assets/images/golden-star-warrios/ranking/top-ranking-mb.png);
            background-size: 100% 100%;
            @apply absolute -top-[10%] left-2/4 h-[6.25rem] w-[107%] -translate-x-1/2 transform rounded-tl-[0.75rem] rounded-tr-[0.75rem] bg-center md:w-[70%];
            &__top {
                @apply absolute -top-[1.25rem] left-2/4 h-[2.3125rem] w-[16.9375rem] -translate-x-1/2 object-cover;
            }

            &__amount {
                @apply absolute left-2/4 top-[44.5%] h-[9.33vw] -translate-x-1/2 transform overflow-hidden;
                .amount {
                    @apply flex min-w-[50vw] items-center justify-center whitespace-nowrap text-sm font-bold capitalize leading-[30px] text-white;
                    span {
                        &:first-child {
                            @apply mr-1 font-montserrat text-base font-bold;
                        }
                        &:last-child {
                            @apply font-montserrat text-sm font-bold;
                        }
                    }
                    &.no-data {
                        @apply flex items-center gap-x-[4px];
                        span {
                            &:first-child {
                                @apply mt-[6px];
                            }
                        }
                    }
                }
            }

            p {
                &:nth-child(2) {
                    @apply absolute left-2/4 top-[25%] mb-1 -translate-x-1/2 transform whitespace-nowrap text-center text-xs font-semibold leading-[19.6px] text-[#fff] lg:text-base;
                }
            }
        }
    }
    .rankings-head-end {
        @apply text-xs;
    }
    .rankings-filter {
        @apply absolute left-[0] top-[60%] flex w-full items-center justify-between px-3 py-[0];

        &__unit {
            @apply flex items-center text-white;
            p {
                @apply mb-0 text-xs font-semibold opacity-70 lg:text-sm;
            }
        }

        &__date {
            .filter-weekend {
                @apply h-full;
            }
            &.ranking-golden-star-warrios {
                :deep(.dropdown-simple) {
                    .dropdown-body {
                        .dropdown-header,
                        .dropdown-menu {
                            @apply rounded-md bg-[#461B15];

                            .dropdown-item {
                                @apply border-0 text-sm font-medium leading-[20px] text-[#FFFFFF];
                                &:first-child {
                                    @apply rounded-t-md;
                                }
                                &:last-child {
                                    @apply rounded-b-md border-b-0;
                                }
                                &.active,
                                &:hover {
                                    @apply border-b border-b-[#FFFFFF1A] bg-[#FFFFFF1A] text-sm font-medium leading-[20px] text-[#FFFFFF];
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .rankings-head-filter {
        @apply absolute right-[1.25vw] top-[1vw] h-[2.6vw] w-[11.51vw];
    }
    &__body {
        @apply rounded-xl bg-top px-[12px] py-[0];
        background: linear-gradient(
            180deg,
            #573003 0%,
            rgba(87, 48, 3, 0) 100%
        );

        &.no-footer {
            @apply h-[324px] rounded-[0_0_0.75rem_0.75rem];
            background: #3c0800;
        }
    }
    &__body-inner {
        @apply grid h-full place-content-center [min-height:inherit];
    }
    &__footer {
        @apply w-full px-[1.25vw] pb-[18px] pt-[20px];
    }
    &__head-bg {
        @apply absolute left-2/4 top-[0] h-[6.15vw] w-full -translate-x-1/2 transform;
    }
    .rank-table__head--mb {
        @apply opacity-70;
    }
}
</style>
