<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import type { LuckyDrawData, LuckyDrawDataInfo } from '~/interfaces/event'
import {
    Autoplay,
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
} from 'swiper/modules'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(timezone)
dayjs.extend(utc)

const router = useRouter()
const staticUrl = useRuntimeConfig().public.staticUrl
const { isMobile } = useDevice()
const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { isLogged } = storeToRefs(useUserStoreInstance)
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const useEventDanhDeStoreInstance = useEventDanhDeStore()

const {
    getCountdown,
    checkIsAfterTarget,
    splitResult,
    registerSwiperInstance,
    handleDayClick,
    initializeSelectedDate,
    updateSevenDays
} = useEventDanhDeStoreInstance

const { 
    serverTimestamp, 
    luckyDrawResultsMB, 
    isLoading,
    selectedDayIndex,
    selectedDate,
    sevenDays
} = storeToRefs(useEventDanhDeStoreInstance)

const countdownTime = ref({
    hours: '00',
    minutes: '00',
    seconds: '00'
})

const swiperRef = ref<any>(null)

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
    luckyDrawInfo: {
        type: Object as PropType<LuckyDrawDataInfo>,
        required: true,
    },
    isLoadingLuckyDraw: {
        type: Boolean,
        default: false
    }
})

const onSwiper = (swiper: any) => {
    swiperRef.value = swiper
    registerSwiperInstance('banner', swiper)
    nextTick(() => {
        if (selectedDayIndex.value !== undefined) {
            swiper.slideTo(selectedDayIndex.value)
        }
    })
}

let timer: NodeJS.Timeout | null = null

const updateCountdown = () => {
    if (serverTimestamp.value) {
        serverTimestamp.value += 1
        const result = getCountdown(serverTimestamp.value)
        countdownTime.value = {
            hours: result.hours,
            minutes: result.minutes,
            seconds: result.seconds
        }
        updateSevenDays(serverTimestamp.value)
    }
}

const handleDeposit = () => {
    if (isLogged.value) {
        router.push('/user/deposit?type=codepay')
        return
    }
    showLoginModal.value = true
}

const totalAmount = computed(() => {
    const info = props.luckyDrawInfo

    if (info.lucky_number_1_txt && info.lucky_number_2_txt) {
        const n1 = Number(info.lucky_number_1_txt.replace(/,/g, ''))
        const n2 = Number(info.lucky_number_2_txt.replace(/,/g, ''))
        return NumberUtils.formatNumberWithoutDecimal(n1 + n2)
    }

    return NumberUtils.formatNumberWithoutDecimal(
        (info.lucky_number_1 || 0) + (info.lucky_number_2 || 0)
    )
})

const totalPlayCount = computed(() => {
    if (!isLogged.value) return 0
    const { lucky_number_1: l1, lucky_number_2: l2 } = props.luckyDraw || {}
    return (l1 ? 1 : 0) + (l2 ? 1 : 0)
})

const firstLuckyNumber1 = computed((): string[] => {
    const value = props.luckyDraw?.lucky_number_1
    if (typeof value !== 'string') return []
    return value?.split(',').map((num: string) => num.trim()).filter(Boolean)
})

const firstLuckyNumber2 = computed((): string[] => {
    const value = props.luckyDraw?.lucky_number_2
    if (typeof value !== 'string') return []
    return value?.split(',').map((num: string) => num.trim()).filter(Boolean)
})

onMounted(() => {
    nextTick(async () => {
        await initializeSelectedDate()
        updateCountdown()

        setTimeout(() => {
            if (swiperRef.value && selectedDayIndex.value !== undefined) {
                swiperRef.value.slideTo(selectedDayIndex.value)
            }
        }, 100)
        
        timer = setInterval(updateCountdown, 1000)
    })
})

onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>
<template>
    <div class="banner">
        <CommonImage
            class="size-full"
            :max="`(max-width: 991px)`"
            :src="`${staticUrl}/danh-de-mien-phi/banner/pc.jpg`"
            :srcMb="`${staticUrl}/danh-de-mien-phi/banner/mb.png`"
            alt="banner"
            loading="eager"
            :width="isMobile ? 375 : 1920"
            :height="isMobile ? 264 : 420"
        />
        <div class="banner__container">
                <div class="w-full h-full flex flex-col lg:flex-row justify-center items-center lg:max-h-[406px]">
                    <div class="banner__container--left">
                        <CommonImage
                            :src="`${staticUrl}/danh-de-mien-phi/banner/${isMobile ? 'text-mb.png':'text.png'}`"
                            alt="banner"
                            class="w-full h-full aspect-[319/80] lg:max-w-[34.4rem] lg:h-[10.8rem] lg:aspect-[550/172] object-contain lg:object-contain" />
                    </div>
                    <div class="banner__container--right">
                        <div class="banner__container--table">
                            <div class="size-full pt-4 px-5 lg:pt-[17px] lg:px-[30px]">
                                <div
                                    class="flex items-center justify-center font-montserrat font-semibold text-base tracking-normal text-center align-middle capitalize text-z-red-500"
                                    :class="[!isLoadingLuckyDraw && isLogged ? 'opacity-0' : '',
                                        (typeof luckyDraw?.lucky_number_1 !== 'string') && (typeof luckyDraw?.lucky_number_2 !== 'string') ? 'gap-x-2.5 lg:gap-x-3 lg:pl-0':'pl-2.5 gap-1'
                                    ]"
                                >
                                    <span class="banner__arrow"></span>
                                    <div
                                        v-if="(typeof luckyDraw?.lucky_number_1 !== 'string') && (typeof luckyDraw?.lucky_number_2 !== 'string')"
                                    >
                                        <div v-if="totalPlayCount > 0">Số lượt chơi còn lại: {{ totalPlayCount }}</div>
                                        <div v-else :class="totalPlayCount != 0 ? 'opacity-0' : ''">Nhận lượt chơi</div>
                                    </div>
                                    <div v-else class="banner__container--number">
                                        Số đã chọn
                                        <div class="flex items-center gap-x-0.5">
                                            <template v-if="firstLuckyNumber1.length > 0">
                                                <span v-for="number in firstLuckyNumber1" :key="number">{{ number }}</span>
                                            </template>
                                            <template v-if="firstLuckyNumber2.length > 0">
                                                <span v-for="number in firstLuckyNumber2" :key="number">{{ number }}</span>
                                            </template>
                                        </div>
                                    </div>
                                    <span class="banner__arrow banner__arrow--right"></span>
                                </div>
                                <div class="banner__date">
                                    <div class=" font-montserrat lg:font-semibold font-bold text-base lg:tracking-normal text-[#292929] text-center lg:text-left lg:py-0">Kết quả XSMB</div>
                                    <div class="relative bg-white w-full h-8 lg:h-[30px] lg:w-[245px] py-1 px-1.5 lg:p-x[3.44px] rounded-[3px] lg:mr-[5px]">
                                        <LazySwiper
                                            :modules="[
                                                Autoplay,
                                                Pagination,
                                                EffectCards,
                                                EffectCreative,
                                                FreeMode,
                                                Navigation,
                                            ]"
                                            :spaceBetween="4"
                                            :slidesPerView="3"
                                            :loop="false"
                                            :allowTouchMove="true"
                                            :watchSlidesProgress="true"
                                            :centeredSlidesBounds="true"
                                            :centeredSlides="true"
                                            :navigation="{
                                                nextEl: '.btn-next',
                                                prevEl: '.btn-prev',
                                            }"
                                            :breakpoints="{
                                                992: {
                                                    slidesPerView: 3,
                                                    spaceBetween: 2.25,
                                                },
                                            }"
                                            @swiper="onSwiper"
                                        >
                                            <LazySwiperSlide 
                                                v-for="(day, index) in sevenDays" 
                                                :key="`${index}_${day.date}`"
                                                class="banner__slide--item"
                                                :class="{ 'active': selectedDayIndex === index }"
                                                @click="handleDayClick(index, day.date)"
                                            >
                                                <div class="font-montserrat lg:font-normal text-z-chroma-black lg:text-xs font-normal text-sm text-center px-4 py-0.5 lg:my-auto lg:px-[14.97px] lg:py-[3.25px] rounded-[4px] lg:rounded-[3.44px]">{{ day.date }}</div>
                                            </LazySwiperSlide>
                                        </LazySwiper>
                                        <div
                                            class="btn-slide btn-next absolute -right-4 top-1/2 -translate-y-1/2 z-[2] size-8 lg:size-5 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-[0px_1px_2px_0px_#00000040]"
                                        >
                                            <NuxtIcon class="text-base lg:text-[11px] text-[#838589]" name="chevron-right"></NuxtIcon>
                                        </div>
                                        <div
                                            class="btn-slide btn-prev absolute -left-4 top-1/2 -translate-y-1/2 z-[2] size-8 lg:size-5 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-[0px_-1px_2px_0px_#00000040]"
                                        >
                                            <NuxtIcon class="text-base lg:text-[11px] text-[#838589]" name="chevron-left"></NuxtIcon>
                                        </div>
                                    </div>
                                </div>
                                <div class="banner__result">
                                    <div class="font-montserrat font-bold lg:text-lg text-base leading-[1.5625rem] lg:leading-[0%] text-z-red-500 text-center lg:text-left mb-2 lg:mb-0">GIẢI ĐẶC BIỆT</div>
                                    <div class="flex flex-1 items-center justify-center gap-x-[7px]">
                                        <template v-if="!checkIsAfterTarget(selectedDate)">
                                            <template v-if="!isLoading">
                                                <div
                                                    v-for="idx in 5"
                                                    :key="`loading-${idx}`"
                                                    class="banner__result--item loading-lucky"
                                                ></div>
                                            </template>
                                            <template v-else>
                                                <div class="banner__result--item"><span>{{ countdownTime.hours }}</span><small>h</small></div>
                                                <div class="font-bold text-[13.2px] leading-[17.6px] text-[#1C1C1C]">:</div>
                                                <div class="banner__result--item"><span>{{ countdownTime.minutes }}</span><small>m</small></div>
                                                <div class="font-bold text-[13.2px] leading-[17.6px] text-[#1C1C1C]">:</div>
                                                <div class="banner__result--item"><span>{{ countdownTime.seconds }}</span><small>s</small></div>
                                            </template>
                                        </template>
                                        <template v-else>
                                            <template v-if="!isLoading">
                                                <div
                                                    v-for="idx in 5"
                                                    :key="`loading-${idx}`"
                                                    class="banner__result--item loading-lucky"
                                                ></div>
                                            </template>
                                            <template v-else>
                                                <div v-for="(item, index) in splitResult(luckyDrawResultsMB)" :key="index" class="banner__result--item layered-background">
                                                    <span>{{ item }}</span>
                                                </div>
                                            </template>
                                        </template>
                                    </div>
                                </div>
                                <div class="banner__money" :class="totalAmount == 0 ? 'opacity-0' : ''">
                                    <div class="font-montserrat lg:font-bold lg:text-sm font-semibold text-xs leading-4 tracking-[0%] text-center text-z-red-600 lg:text-white">TỔNG<br> THƯỞNG</div>
                                    <div class="banner__money--number" :class="totalAmount.length > 10 ? 'max-lg:!text-2xl': ''">{{ totalAmount }} VND</div>
                                </div>
                                <template v-if="(luckyDraw?.deposit <= luckyDraw?.deposit_required && isLogged) || !isLogged">
                                    <div @click="handleDeposit" class="banner__btn"><span>NẠP NGAY</span></div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.banner {
    @apply relative lg:h-[420px] bg-[radial-gradient(45.61%_17%_at_97.07%_26.43%,#6F0605_0%,rgba(86,5,4,0)_100%),radial-gradient(34.02%_16.27%_at_28.67%_52.34%,#841506_0%,rgba(132,21,6,0)_100%),#2C0404];
    :deep(.base-image) {
        @apply lg:relative lg:h-full;
    }
    & > .base-image {
        @apply absolute inset-0;
    }
      background: radial-gradient(45.61% 17% at 97.07% 26.43%, #6F0605 0%, rgba(86, 5, 4, 0) 100%),
              radial-gradient(34.02% 16.27% at 28.67% 52.34%, #841506 0%, rgba(132, 21, 6, 0) 100%),
              #2C0404;
    &__container {
        @apply px-4 pt-[11px] lg:absolute lg:inset-0 lg:mx-auto flex items-end justify-center w-full lg:max-w-[1401px] h-full;
        &--table {
            @apply lg:absolute lg:bottom-24 lg:left-56 lg:w-[506px] lg:h-[292px];
            background: url('/assets/images/danh-de-mien-phi/banner/banner-table.png') no-repeat
            center top / 100%;
            @media (max-width: 991px) {
                @apply w-full max-w-[343px] h-[312px];
                background: url('/assets/images/danh-de-mien-phi/banner/banner-table-mb.png') no-repeat
            center top / contain; 
            }
        }
        &--left {
            @apply w-full max-w-80 h-full min-h-20 lg:h-auto lg:w-[calc(100%/2.5)] lg:max-w-[34.4rem];
        }
        &--right {
            @apply flex items-center justify-center mt-[9px] relative lg:self-end w-full lg:h-[25.375rem] lg:bg-[url('/assets/images/danh-de-mien-phi/banner/bg-banner-table.png')] lg:bg-no-repeat lg:bg-cover lg:bg-left-bottom;
        }
        &--number {
            @apply flex items-center justify-center gap-x-1.5;
            span {
                @apply flex items-center justify-center font-montserrat font-medium text-[10px] leading-[14px] text-[#1C1C1C] size-[22px];
                background: url('/assets/images/danh-de-mien-phi/bong-gold.svg') no-repeat
                center / 100% 100%;
            }
        }
    }
    &__arrow {
        @apply relative flex items-center gap-x-[1.5px];
        &:before,&:after {
            @apply content-[''] inline-block w-0 h-0 border-l-[5.76px] border-l-red-500 border-r-0 border-y-4 border-y-transparent border-solid;

        }
        &--right {
            &:before,&:after {
                @apply rotate-180;
            }
        }
    }
    &__date {
        @apply lg:flex items-center justify-between lg:px-0 lg:h-[45px] lg:pl-[26px] rounded mt-6 lg:mt-[37px];
        background: linear-gradient(153.25deg, rgba(255, 255, 255, 0.7) 7.82%, rgba(255, 235, 204, 0) 68.44%);
        .btn-slide {
            &.swiper-button-disabled {
                @apply cursor-not-allowed;
                span {
                    @apply opacity-30;
                }
            }
        }
    }
    &__slide {
        &--item {
            @apply cursor-pointer;
            &.active {
                @apply pointer-events-none;
                > div {
                    @apply font-semibold text-z-red-500 bg-[#FFF2E8];
                }
            }
        }
    }
    &__result {
        @apply relative lg:flex items-center justify-between lg:h-[61px] pt-1 pb-1.5 lg:pb-0 lg:pt-0 px-[15px] lg:mt-[3px] rounded-[4px] gap-x-[32px] lg:mx-auto lg:max-w-[97.1%] lg:max-w-full;
        background: linear-gradient(90deg, rgba(255, 186, 148, 0.7) 0%, rgba(255, 235, 204, 0.7) 100%);
        &:before,&:after {
            @apply content-[''] absolute -translate-y-2/4 w-0 h-0 border-l-[6.5px] border-l-[#fbb488] border-r-0 border-y-[17px] border-y-transparent border-solid left-0 top-2/4;
        }
        &:after {
            @apply right-0 left-auto rotate-180;
        }
        &--item {
            @apply bg-z-red-100 rounded-full size-10 lg:size-[41px] flex items-center justify-center gap-x-[1px] ;
            &.layered-background {
                @apply bg-[url('/assets/images/danh-de-mien-phi/ball.png')] bg-no-repeat bg-center bg-[length:100%_100%];
            }
            span {
                @apply font-montserrat font-bold text-base;
                background: linear-gradient(338.45deg, #FFE57B 17.69%, #FFFFFF 65.98%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            small {
                @apply text-white text-[0.625rem] leading-[14px] lg:leading-[-1%] mt-0.5;
            }
        }
    }
    &__money {
        @apply font-montserrat flex items-center justify-center mt-5 lg:mt-8 gap-x-3 lg:gap-x-[15px];
        &--number {
            @apply font-montserrat font-bold lg:font-extrabold text-xl max-[360px]:text-2xl text-[28px] lg:text-4xl lg:leading-9;
            background: linear-gradient(355deg, #FFE57B 17.69%, #FFFFFF 65.98%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            @media (max-width: 991px) {
                background: linear-gradient(180deg, #FF3415 22.7%, #FD442F 78.26%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }
    &__btn {
        @apply mt-2.5 font-montserrat lg:mt-3 flex items-center justify-center font-extrabold text-lg h-12 text-center w-[162px] lg:w-[160px] cursor-pointer mx-auto lg:hover:brightness-[1.08] transition-all;
        background: url('/assets/images/danh-de-mien-phi/banner/btn.png') no-repeat
        center / 100% 100%;
         @media (max-width: 991px) {
            @apply self-end;
         }
        span {
            @apply font-montserrat;
            background: linear-gradient(180deg, #CF2604 33.65%, #A50E03 70.08%), 
                   linear-gradient(180deg, #EA462E 0%, #C7310C 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
    }
}
</style>