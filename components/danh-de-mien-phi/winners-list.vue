<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import {
    Autoplay,
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
} from 'swiper/modules'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import type { LuckyDrawData, LuckyDrawDataInfo } from '~/interfaces/event'
import { NumberUtils } from '~/utils'

dayjs.extend(timezone)
dayjs.extend(utc)

const useUserStoreInstance = useUserStore()
const { user, isLogged } = storeToRefs(useUserStoreInstance)
const { isDeskTop } = useWindowSize()
const router = useRouter()
const staticUrl = useRuntimeConfig().public.staticUrl
const useEventDanhDeStoreInstance = useEventDanhDeStore()

const {
    getCountdown,
    checkIsAfterTarget,
    splitResult,
    registerSwiperInstance,
    handleDayClick,
    initializeSelectedDate,
    updateSevenDays
} = useEventDanhDeStoreInstance

const { 
    serverTimestamp, 
    luckyDrawResultsMB, 
    luckyDrawAward, 
    isLoading,
    selectedDayIndex,
    selectedDate,
    sevenDays
} = storeToRefs(useEventDanhDeStoreInstance)

const useModalStoreInstance = useModalStore()
const { showBetFreeModal } = storeToRefs(useModalStoreInstance)

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
    luckyDrawInfo: {
        type: Object as PropType<LuckyDrawDataInfo>,
        required: true,
    },
    isLoadingLuckyDraw: {
        type: Boolean,
        default: false
    }
})

const countdownTime = ref({
    hours: '00',
    minutes: '00',
    seconds: '00'
})

const swiperRef = ref<any>(null)

const onSwiper = (swiper: any) => {
    swiperRef.value = swiper
    registerSwiperInstance('winnersList', swiper)
    nextTick(() => {
        if (selectedDayIndex.value !== undefined) {
            swiper.slideTo(selectedDayIndex.value)
        }
    })
}

let timer: NodeJS.Timeout | null = null

const updateCountdown = () => {
    if (serverTimestamp.value) {
        serverTimestamp.value += 1
        const result = getCountdown(serverTimestamp.value)
        countdownTime.value = {
            hours: result.hours,
            minutes: result.minutes,
            seconds: result.seconds
        }
        updateSevenDays(serverTimestamp.value)
    }
}

const firstLuckyNumber1 = computed((): string[] => {
    const value = props.luckyDraw?.lucky_number_1
    if (typeof value !== 'string') return []
    return value?.split(',').map((num: string) => num.trim()).filter(Boolean)
})

const firstLuckyNumber2 = computed((): string[] => {
    const value = props.luckyDraw?.lucky_number_2
    if (typeof value !== 'string') return []
    return value?.split(',').map((num: string) => num.trim()).filter(Boolean)
})

const handleDeposit = () => {
    router.push('/user/deposit?type=codepay')
}

const handleBetNunber = () => {
    if (isDeskTop.value) {
        showBetFreeModal.value = true
        return
    }
    const luckyNumberMobileElement = document.querySelector('.lucky-number-mobile-ct')
    if (luckyNumberMobileElement) {
        const elementPosition = luckyNumberMobileElement.getBoundingClientRect().top
        const offsetPosition = elementPosition + window.pageYOffset - 80

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        })
    }
}

onMounted(() => {
    nextTick(async () => {
        await initializeSelectedDate()
        updateCountdown()
        
       setTimeout(() => {
            if (swiperRef.value && selectedDayIndex.value !== undefined) {
                swiperRef.value.slideTo(selectedDayIndex.value)
            }
        }, 100)
        
        timer = setInterval(updateCountdown, 1000)
    })
})

onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>
<template>
    <div class="winners-list">
        <div class="winners-list__header font-svn-vt">danh sách trúng thưởng</div>
        <div class="winners-list__content">
        <div class="winners-list__table">
            <div class="winners-list__wrapper">
            <div class="winners-list__slide">
                <LazySwiper
                    :modules="[
                        Autoplay,
                        Pagination,
                        EffectCards,
                        EffectCreative,
                        FreeMode,
                        Navigation,
                    ]"
                    :spaceBetween="4"
                    :slidesPerView="3"
                    :loop="false"
                    :allowTouchMove="true"
                    :watchSlidesProgress="true"
                    :centeredSlidesBounds="true"
                    :centeredSlides="true"
                    :navigation="{
                        nextEl: '.btn-next-winners-list',
                        prevEl: '.btn-prev-winners-list',
                    }"
                    :breakpoints="{
                        992: {
                            slidesPerView: 7,
                            spaceBetween: 4,
                        },
                    }"
                    @swiper="onSwiper"
                >
                    <LazySwiperSlide 
                        v-for="(day, index) in sevenDays" 
                        :key="`${index}_${day.date}`"
                        class="winners-list__slide--item"
                        :class="{ 'active': selectedDayIndex === index }"
                        @click="handleDayClick(index, day.date)"
                    >
                        <div class="font-montserrat font-normal text-sm leading-5 text-center text-z-chroma-black h-full px-4 py-1.5 lg:px-5 lg:py-1.5 rounded">{{ day.date }}</div>

                    </LazySwiperSlide>
                </LazySwiper>
                
            <div
                class="btn-winners btn-next-winners-list absolute -right-6 top-1/2 -translate-y-1/2 z-[2] size-8 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-[0px_1px_2px_0px_#00000040]"
            >
                <NuxtIcon class="text-base text-[#838589]" name="chevron-right"></NuxtIcon>
            </div>
            <div
                class="btn-winners btn-prev-winners-list absolute -left-6 top-1/2 -translate-y-1/2 z-[2] size-8 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-[0px_-1px_2px_0px_#00000040]"
            >
                <NuxtIcon class="text-base text-[#838589]" name="chevron-left"></NuxtIcon>
            </div>
            </div>
        </div>
            <div class="winners-list__results">
                <div class="winners-list__results--bg font-montserrat font-bold text-xs lg:text-xs text-center text-z-chroma-black flex items-center justify-center gap-x-[1.125rem] lg:block">
                    Kết quả XSMB
                    <span class="font-bold text-base text-center text-z-red-dit block lg:mt-1">GIẢI ĐẶC BIỆT</span>
                </div>
                <div 
                    class="winners-list__results--number flex items-center gap-x-[9px] font-bold text-[16px] leading-[24px] tracking-[0%] text-center"
                    :class="checkIsAfterTarget(selectedDate) ? 'active' : ''"
                >
                    <template v-if="!checkIsAfterTarget(selectedDate)">
                         <template v-if="!isLoading">
                            <div
                                v-for="idx in 5"
                                :key="`loading-${idx}`"
                                class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center loading-lucky"
                            ></div>
                        </template>
                        <template v-else>
                            <div class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center"><span>-</span></div>
                            <div class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center"><span>-</span></div>
                            <div class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center"><span>-</span></div>
                            <div class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center"><span>-</span></div>
                            <div class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center"><span>-</span></div>
                        </template>
                    </template>
                    <template v-else>
                         <template v-if="!isLoading">
                            <div
                                v-for="idx in 5"
                                :key="`loading-${idx}`"
                                class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center loading-lucky"
                            ></div>
                        </template>
                        <template v-else>
                            <div v-for="(item, index) in splitResult(luckyDrawResultsMB)" :key="index" class="bg-z-red-700 layered-background size-[2.375rem] lg:size-[38px] rounded-full flex items-center justify-center">
                                <span>{{ item }}</span>
                            </div>
                        </template>
                    </template>
                </div>
            </div>
            <div class="winners-list__box">
                <div class="winners-list__box--row winners-list__box--thead">
                    <div class="winners-list__box--td">Tài Khoản</div>
                    <div class="winners-list__box--td">Loại</div>
                    <div class="winners-list__box--td">Giải Thưởng</div>
                </div>
                <div
                    v-if="!isLoading"
                    class="h-[11.5rem] lg:h-[184px] flex items-center justify-center"
                >
                    <img
                        width="50"
                        height="50"
                        :src="`${staticUrl}/loading.gif`"
                        alt="loading icon"
                    />
                </div>
                <template v-else>
                    <template v-if="!checkIsAfterTarget(selectedDate)">
                        <div class="flex items-center justify-center flex-col h-[11.5rem] lg:h-[184px] text-center">
                            <img :src="`${staticUrl}/danh-de-mien-phi/winners-list/clock.png`" alt="clock" class="w-[60px] lg:w-[77px] mx-auto">
                            <div class="font-montserrat font-medium text-base leading-6 lg:text-[16px] lg:leading-[24px] text-[#595A60] mt-2">Sắp bắt đầu...</div>
                            <div class="font-montserrat font-medium text-base text-z-chroma-black mt-[1.125rem] mt-3">
                                {{ countdownTime.hours }} giờ  {{ countdownTime.minutes }} phút {{ countdownTime.seconds }} giây
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div v-if="luckyDrawAward?.length" class="winners-scroll overflow-y-auto scrollbar h-[182px]">
                            <div 
                                v-for="(item, index) in luckyDrawAward"
                                :key="index"
                                class="winners-list__box--row"
                                :class="{
                                    'active': user?.username === item?.username
                                }"
                            >
                                <div class="winners-list__box--td">{{ item?.username }}</div>
                                <div class="winners-list__box--td">{{ Number(item?.lucky_draw || item?.type) === 1 ? 'Đề 2 số' : 'Đề 3 số' }}</div>
                                <div class="winners-list__box--td">{{ item?.amount_txt || NumberUtils.formatNumberWithComma(item.amount) }} VND</div>
                            </div>
                        </div>
                        <div v-else class="flex items-center justify-center flex-col h-[11.5rem] lg:h-[184px] text-center">
                            <img :src="`${staticUrl}/danh-de-mien-phi/winners-list/clock.png`" alt="clock" class="w-[4.5625rem] lg:w-[64px] mx-auto">
                            <div class="font-medium text-base text-[#595A60] mt-[1.125rem] lg:mt-[12px]">
                                Không có dữ liệu.
                            </div>
                        </div>
                    </template>
                </template>
            </div>
            <template v-if="luckyDraw?.deposit >= luckyDraw?.deposit_required">
                <div v-if="(typeof luckyDraw?.lucky_number_1 !== 'string' && typeof luckyDraw?.lucky_number_2 !== 'string')" class="winners-list__mynumber flex items-center justify-center gap-x-4 lg:gap-x-[16px]" :class="[!isLoadingLuckyDraw  && isLogged ? 'opacity-0' : '']">
                    <div class="font-normal text-z-chroma-black text-xs leading-[1.125rem] lg:text-[14px] lg:leading-[20.9px] tracking-[0px]">Vui lòng chọn số may mắn</div>
                    <div @click="handleBetNunber" class="winners-list__mynumber--btn font-medium text-sm text-white w-[120px] h-[34px] rounded-[0.5625rem] lg:w-[84px] lg:h-[28px] lg:rounded-[8px]">
                        Chọn số
                    </div>
                </div>
                <div v-else class="winners-list__mynumber flex items-center justify-center gap-x-4 lg:gap-x-[16px]" :class="[!isLoadingLuckyDraw && isLogged ? 'opacity-0' : '']">
                    <div class="font-normal text-xs lg:text-sm lg:font-medium text-z-chroma-black">Số đã chọn</div>
                    <div class="winners-list__mynumber--item">
                        <template v-if="firstLuckyNumber1.length > 0">
                            <div v-for="number in firstLuckyNumber1" :key="number">{{ number }}</div>
                        </template>
                        <template v-if="firstLuckyNumber2.length > 0">
                            <div v-for="number in firstLuckyNumber2" :key="number">{{ number }}</div>
                        </template>
                    </div>
                </div>
            </template>
            <div v-else class="winners-list__mynumber flex items-center justify-center gap-x-4 lg:gap-x-[16px]" :class="[!isLoadingLuckyDraw && isLogged ? 'opacity-0' : '']">
                <div class="font-montserrat font-normal text-xs leading-[1.125rem] lg:font-medium lg:text-sm tracking-[0px] text-z-chroma-black">Tham gia quay số may mắn ngay bây giờ</div>
                <div @click="handleDeposit" class="winners-list__mynumber--btn font-montserrat font-medium text-sm text-white px-6 h-[34px] rounded-[0.5625rem] lg:px-3.5 lg:h-7 lg:rounded-lg w-full max-w-[120px]">
                    Nạp Ngay
                </div>
            </div>
        </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.winners-list {
    @apply mt-12 lg:mt-16 lg:pl-[21px] lg:pt-9 relative;
    background: url('/assets/images/danh-de-mien-phi/winners-list/winner-right-bg.png') no-repeat right top / contain; ;
    @media (max-width: 991px) {
        @apply rounded-2xl;
        background: linear-gradient(82.47deg, rgba(182, 62, 32, 0.8) 4.35%, rgba(95, 20, 3, 0.8) 20.03%, rgba(88, 17, 3, 0.8) 60.36%, rgba(115, 34, 14, 0.8) 93.66%);
    }
    &__header {
        @apply absolute inset-0 -left-2 -mt-4 pl-8 h-[61px] pb-2 lg:left-1 lg:pl-11 font-normal text-xl lg:text-2xl leading-[1.625rem] lg:leading-[34px] uppercase text-z-chroma-black lg:h-[84px] lg:pb-[17px] w-full max-w-[339px] lg:max-w-[492px] flex items-center justify-start;
        background: url('/assets/images/danh-de-mien-phi/winners-list/winner-bg-header.png') no-repeat
        left top / 100% 100%;
        &::before {
            @apply mr-4;
            content: '';
            width: 50px;
            height: 50px;
            background: url('/assets/images/danh-de-mien-phi/winners-list/gift.png') no-repeat center / contain;
        }
        @media (max-width: 991px) {
             &::before {
                @apply mr-3;
                width: 40px;
                height: 40px;
            }
            background: url('/assets/images/danh-de-mien-phi/winners-list/winner-bg-header-mb.png') no-repeat
            left top / 100% 100%;
        }
    }
    &__content {
        @apply p-3 pt-[53px] lg:pt-[57px] lg:pl-[26px] lg:pb-7;
        background: linear-gradient(82.47deg, rgba(182, 62, 32, 0.8) 4.35%, rgba(95, 20, 3, 0.8) 20.03%, rgba(88, 17, 3, 0.32) 60.36%, rgba(74, 12, 4, 0) 93.66%);
        border-radius: 20px;
    }
    &__table {
        @apply rounded-xl lg:w-full lg:max-w-[835px];
        background: linear-gradient(157.95deg, #FDF1DB 11.38%, #FEDAC0 85.59%);
    }
    &__wrapper {
        @apply relative px-3 pt-2;
    }
    &__slide {
        @apply relative h-10 bg-white rounded-lg p-1;
        .btn-winners {
            @apply lg:hidden;
            &.swiper-button-disabled {
                @apply cursor-not-allowed;
                span {
                    @apply opacity-30;
                }
            }
        }
        &--item {
            @apply cursor-pointer;
            &.active {
                @apply pointer-events-none;
                > div {
                    @apply text-z-red-500 bg-[#FFF2E8] font-semibold;
                }
            }
        }
    }
    &__results {
        @apply lg:flex items-center justify-center gap-x-[50px] max-w-[462px] h-[54px] mx-auto my-4;
        background: linear-gradient(90deg, rgba(255, 186, 148, 0) 0%, rgba(255, 213, 179, 0.7) 49.04%, rgba(255, 235, 204, 0) 100%);
        @media (max-width: 991px) {
            @apply pt-2 pb-[0.3125rem] h-auto my-3;
            background: linear-gradient(90deg, rgba(255, 186, 148, 0) 4.05%, rgba(255, 213, 179, 0.7) 50.11%, rgba(255, 235, 204, 0) 97.97%);
        }
        &--bg {
            @media (max-width: 991px) {
                @apply w-full max-w-[250px] mb-1 mx-auto h-8 flex items-center justify-center gap-3;
                background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.8) 100%);
            }
        }
        &--number {
            .layered-background {
                @apply bg-[url('/assets/images/danh-de-mien-phi/ball.png')] bg-no-repeat bg-center bg-[length:100%_100%];
            }
            
            span {
                background: linear-gradient(0deg, #FFC0C0, #FFC0C0);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            &.active {
                div:nth-last-child(-n+3) {
                    span {
                        @apply font-black text-[28px] leading-7 tracking-[0%];
                        background: linear-gradient(338.45deg, #FFE57B 17.69%, #FFFFFF 65.98%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }
                }
            }
            @media (max-width: 991px) {
                @apply justify-center;
                span {
                    @apply text-base;
                }
                &.active {
                    div:nth-last-child(-n+3) {
                        span {
                            @apply text-[1.75rem] leading-7;
                        }
                    }
                }
            }
        }
    }
    &__box {
        @apply px-3 lg:px-5;
        &--row {
            @apply w-full grid grid-cols-[5.6rem_4.2rem_minmax(5.4rem,1fr)] gap-0.5 lg:gap-4 lg:grid-cols-3 items-center h-11 bg-[#FFF2E8] border-b-[#EBEBEB] border-b border-solid mb-0.5 last:mb-0;
            &.active {
                background: #FFDCC0;
            }
        }
        &--thead {
            @apply grid-cols-[5.6rem_4.2rem_minmax(5.4rem,1fr)] lg:!grid-cols-3 border-none rounded-lg mb-2 h-9 gap-0.5 lg:gap-3;
            background: linear-gradient(90deg, rgba(255, 191, 151, 0.6) 0%, rgba(247, 217, 185, 0.6) 95.72%);
            .winners-list__box--td {
                @apply font-medium text-z-chroma-black last:text-right lg:last:text-center #{!important};
            }
        }
        &--td {
            @apply font-montserrat font-normal text-xs xs:text-sm text-z-chroma-black first:pl-3 lg:first:pl-4 last:pr-3 lg:last:pr-4 first:truncate;
            &:not(:first-child) {
                @apply text-center;
            }
            &:last-child {
                @apply font-semibold text-z-red-dit text-right lg:text-center;
            }
        }
    }
    &__mynumber {
        @apply h-[79px] flex items-center justify-center flex-col gap-2 lg:gap-4 lg:flex-row lg:h-[48px] mt-1 lg:rounded-b-xl;
        background: linear-gradient(90deg, #FFDFB5 -16.45%, #FFCB9A 120.62%);
        &--item {
            @apply flex items-center gap-x-1 font-semibold text-sm text-center text-z-chroma-black;
            div {
                @apply flex items-center justify-center rounded-full size-8 lg:size-[32px] bg-gradient-to-b from-[#FFF765] to-[#FFBB00] shadow-[inset_0px_1px_4.4px_rgba(255,255,255,0.85)];
            }
        }
        &--btn {
            @apply flex items-center justify-center cursor-pointer;
            background: linear-gradient(270deg, #BF0606 -0.34%, #9F0D0D 97.6%);
            &:hover {
                @apply lg:brightness-[1.08];
            }
        }
    }
}
</style>
