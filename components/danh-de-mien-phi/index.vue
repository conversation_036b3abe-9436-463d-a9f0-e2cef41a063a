<script setup lang="ts">
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import Banner from './banner.vue'
import LuckyNumber from './lucky-number.vue'
import LuckyNumberMobile from './lucky-number-mb.vue'
import WinnersList from './winners-list.vue'
import Rules from './rules.vue'

const useUserStoreInstance = useUserStore()
const { isLogged } = storeToRefs(useUserStoreInstance)
const useModalStoreInstance = useModalStore()
const {
    showBetConditionModal,
    showRulesBetFreeModal,
    showBetFreeModal,
    showBetSuccessModal,
    showPromotionLimitModal,
    showBetFreeModalMB,
    showLuckyDrawCashbackModal,
    currentModal,
} = storeToRefs(useModalStoreInstance)
const useEventDanhDeStoreInstance = useEventDanhDeStore()
const { getLuckyDrawInfo, getLuckyDrawStatus } = useEventDanhDeStoreInstance
const { luckyDraw, luckyDrawInfo, isLoadingInfo, isLoadingLuckyDraw } =
    storeToRefs(useEventDanhDeStoreInstance)

const checkShowLuckyDrawCashbackModal = () => {
    if (
        !currentModal.value &&
        !showBetFreeModalMB.value &&
        [
            luckyDraw?.value?.lucky_number_1,
            luckyDraw?.value?.lucky_number_2,
        ].some((num) => num && typeof num !== 'string')
    ) {
        showLuckyDrawCashbackModal.value = true
    }
}

onMounted(() => {
    nextTick(async () => {
        if (isLogged.value) {
            await getLuckyDrawStatus()
        }
        await getLuckyDrawInfo()
        checkShowLuckyDrawCashbackModal()
        if (showBetFreeModalMB.value) {
            const luckyNumberMobileElement = document.querySelector(
                '.lucky-number-mobile-ct'
            )
            if (luckyNumberMobileElement) {
                const elementPosition =
                    luckyNumberMobileElement.getBoundingClientRect().top
                const offsetPosition = elementPosition + window.pageYOffset - 80

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth',
                })
            }
            showBetFreeModalMB.value = false
        }
    })
})

watch(
    () => isLogged.value,
    async (value) => {
        if (value) {
            await getLuckyDrawStatus()
            checkShowLuckyDrawCashbackModal()
        }
    }
)

watch(currentModal, () => {
    checkShowLuckyDrawCashbackModal()
})

watch(
    () => showBetFreeModal.value,
    (value) => {
        if (!value) {
            showLuckyDrawCashbackModal.value = false
        }
    }
)
</script>
<template>
    <div class="bet">
        <Banner
            :luckyDraw="luckyDraw"
            :luckyDrawInfo="luckyDrawInfo"
            :isLoadingLuckyDraw="isLoadingLuckyDraw"
        />
        <div class="bet__bg">
            <div class="container">
                <LuckyNumber
                    :luckyDraw="luckyDraw"
                    :luckyDrawInfo="luckyDrawInfo"
                    :isLoadingLuckyDraw="isLoadingLuckyDraw"
                    :isLoadingInfo="isLoadingInfo"
                    class="hidden lg:flex"
                />
                <LuckyNumberMobile
                    :luckyDraw="luckyDraw"
                    :luckyDrawInfo="luckyDrawInfo"
                    :isLoadingLuckyDraw="isLoadingLuckyDraw"
                    :isLoadingInfo="isLoadingInfo"
                    class="lucky-number-mobile-ct block lg:hidden"
                />
                <WinnersList
                    :luckyDraw="luckyDraw"
                    :luckyDrawInfo="luckyDrawInfo"
                    :isLoadingLuckyDraw="isLoadingLuckyDraw"
                />
                <Rules />
            </div>
        </div>
        <LazyModalDanhDeMienPhiBetCondition :show="showBetConditionModal" />
        <LazyModalDanhDeMienPhi :show="showRulesBetFreeModal" />
        <LazyModalDanhDeMienPhiBetFree :show="showBetFreeModal" />
        <LazyModalDanhDeMienPhiBetSuccess :show="showBetSuccessModal" />
        <LazyModalDanhDeMienPhiPromotionLimit :show="showPromotionLimitModal" />
        <LazyModalDanhDeMienPhiCashback
            :luckyDraw="luckyDraw"
            :show="
                showLuckyDrawCashbackModal && !currentModal && !showBetFreeModal
            "
        />
    </div>
</template>

<style lang="scss" scoped>
.bet {
    &__bg {
        @apply py-8 lg:pb-20 lg:pt-[56px];

        background: radial-gradient(
                45.61% 17% at 97.07% 26.43%,
                #6f0605 0%,
                rgba(86, 5, 4, 0) 100%
            ),
            radial-gradient(
                34.02% 16.27% at 28.67% 52.34%,
                #841506 0%,
                rgba(132, 21, 6, 0) 100%
            ),
            #2c0404;

        @include tablet() {
            background: radial-gradient(
                    45.61% 17% at 97.07% 26.43%,
                    #6f0605 0%,
                    rgba(86, 5, 4, 0) 100%
                ),
                radial-gradient(
                    34.02% 16.27% at 28.67% 52.34%,
                    #841506 0%,
                    rgba(132, 21, 6, 0) 100%
                ),
                url('/assets/images/danh-de-mien-phi/bet-bg.png') no-repeat
                    right top / 100% 100%,
                #2c0404;
        }
    }
}
</style>
