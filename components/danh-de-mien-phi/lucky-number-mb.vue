<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import { type LuckyDrawData, type LuckyDrawDataInfo, type BetFormValues, BetType } from '~/interfaces/event'
import { useForm } from 'vee-validate'
import { betFreeForm } from '~/forms/betting.schema'
import { computed } from 'vue'
const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { isLogged } = storeToRefs(useUserStoreInstance)
const { showLoginModal, showBetConditionModal, showPromotionLimitModal, betType, dateBetMB } = storeToRefs(
    useModalStoreInstance
)
const useEventDanhDeStoreInstance = useEventDanhDeStore()
const { betLuckyDraw, getLuckyNumbers, getAmountLucky } = useEventDanhDeStoreInstance
const { isAccountPromotion } = storeToRefs(useEventDanhDeStoreInstance)

const { t } = useI18n()

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
    luckyDrawInfo: {
        type: Object as PropType<LuckyDrawDataInfo>,
        required: true,
    },
    isLoadingInfo: {
        type: Boolean,
        default: false,
    },
    isLoadingLuckyDraw: {
        type: Boolean,
        default: false,
    }
})

const initialValuesTwoDigits = {
    numberOne: '',
    numberTwo: '',
}

const initialValuesThreeDigits = {
    numberThree: '',
    numberFour: '',
    numberFive: '',
}

const validationSchemaTwoDigits = computed(() => betFreeForm(t, 2))
const validationSchemaThreeDigits = computed(() => betFreeForm(t, 3))

const { handleSubmit: handleSubmitTwoDigits, errors: errorsTwoDigits, defineField: defineFieldTwoDigits, resetForm: resetFormTwoDigits } = useForm({
    initialValues: initialValuesTwoDigits,
    validationSchema: validationSchemaTwoDigits,
})

const { handleSubmit: handleSubmitThreeDigits, errors: errorsThreeDigits, defineField: defineFieldThreeDigits, resetForm: resetFormThreeDigits } = useForm({
    initialValues: initialValuesThreeDigits,
    validationSchema: validationSchemaThreeDigits,
})

const [numberOne] = defineFieldTwoDigits('numberOne')
const [numberTwo] = defineFieldTwoDigits('numberTwo')
const [numberThree] = defineFieldThreeDigits('numberThree')
const [numberFour] = defineFieldThreeDigits('numberFour')
const [numberFive] = defineFieldThreeDigits('numberFive')

const onPasteBetNumber = (text: string, field: keyof BetFormValues): void => {
    if (!text.match(/[\d]/g)) {
        return
    }
    if (field === 'numberOne') numberOne.value = text.trim()
    if (field === 'numberTwo') numberTwo.value = text.trim()
    if (field === 'numberThree') numberThree.value = text.trim()
    if (field === 'numberFour') numberFour.value = text.trim()
    if (field === 'numberFive') numberFive.value = text.trim()
}
const firstTwoNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_1?.toString())
})
const firstThreeNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_2?.toString())
})

const amountLuckyNumber1 = computed(() => {
    return getAmountLucky(props.luckyDrawInfo.lucky_number_1_txt, props.luckyDrawInfo.lucky_number_1)
})

const amountLuckyNumber2 = computed(() => {
    return getAmountLucky(props.luckyDrawInfo.lucky_number_2_txt, props.luckyDrawInfo.lucky_number_2)
})

const hasErrorsTwoDigits = computed(() => Object.keys(errorsTwoDigits.value).length > 0)
const hasErrorsThreeDigits = computed(() => Object.keys(errorsThreeDigits.value).length > 0)

const isDisabled = computed(() => {
    if (hasErrorsTwoDigits.value) return true
    return !(numberOne.value?.length === 2 && numberTwo.value?.length === 2)
})

const isDisabledThree = computed(() => {
    if (hasErrorsThreeDigits.value) return true
    return !(numberThree.value?.length === 3 && numberFour.value?.length === 3 && numberFive.value?.length === 3)
})

const handleBet = async (type: BetType) => {
    if (isLogged.value) {
        if (isAccountPromotion.value) {
            showPromotionLimitModal.value = true;
            return
        }
        betType.value = type
        if (type === BetType.LUCKY_NUMBER_1 && !props.luckyDraw?.lucky_number_1) {
            showBetConditionModal.value = true
            return
        }
        if (type === BetType.LUCKY_NUMBER_2 && !props.luckyDraw?.lucky_number_2) {
            showBetConditionModal.value = true
            return
        }
        try {
            if (type === BetType.LUCKY_NUMBER_1) {
                const numbers = `${numberOne.value},${numberTwo.value}`
                await betLuckyDraw(numbers, '')
                resetFormTwoDigits()
            } else {
                const numbers = `${numberThree.value},${numberFour.value},${numberFive.value}`
                await betLuckyDraw('', numbers)
                resetFormThreeDigits()
            }
        } catch (error) {
            console.error('Error betting:', error)
        }
        return
    }
    showLoginModal.value = true
}
</script>
<template>
    <div class="overflow-x-auto no-scrollbar -mx-[0.938rem]">
        <div class="lucky-number-mb">
            <form @submit.prevent="handleBet(2)" class="lucky-number-mb__item">
                <div
                    v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                    class="absolute top-0 left-0 size-full flex items-center justify-center"
                >
                    <img
                        width="50"
                        height="50"
                        :src="`${staticUrl}/loading.gif`"
                        alt="loading icon"
                    />
                </div>
                <template v-else>
                    <div class="lucky-number-mb__title text-2xl leading-6 tracking-[0%] font-svn-vt pt-2">đánh ĐỀ 2 SỐ</div>
                    <div v-if="(typeof luckyDraw?.lucky_number_1 !== 'string')" class="relative">
                        <label class="font-medium text-sm leading-5 tracking-[0%] text-z-chroma-black mt-5 mb-1 block">Đánh đề 2 số (00 - 99)</label>
                        <div class="flex items-center gap-[8px] pr-[28px]">
                            <CommonTextInputNumber
                                v-model="numberOne"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!p-3 [&_input]:!bg-white [&_input]:!rounded-lg [&_input]:!text-z-chroma-black [&_input]:placeholder:!text-z-dark-gray"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="2"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'numberOne')"
                            />
                            <CommonTextInputNumber
                                v-model="numberTwo"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!p-3 [&_input]:!bg-white [&_input]:!rounded-lg [&_input]:!text-z-chroma-black [&_input]:placeholder:!text-z-dark-gray"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="2"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'numberTwo')"
                            />
                        </div>
                        <div class="error-message max-[360px]:text-xs text-sm text-[#FF0000] mt-1 h-5" :class="errorsTwoDigits.numberOne || errorsTwoDigits.numberTwo ? 'opacity-100' : 'opacity-0'">
                            {{ errorsTwoDigits.numberOne || errorsTwoDigits.numberTwo }}
                        </div>
                        <div class="absolute top-9 right-0 icon-input">
                            <img :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`" alt="tooltip" class="size-[1.25rem]">
                        </div>
                        <div class="box-tooltip hidden absolute bottom-16 -right-3 bg-[#1C1C1C] rounded-[4px] p-[4px] py-[3px] text-white font-semibold text-[12px] leading-[120%] w-[208px]">
                            <ul>
                                <li>Chọn 1 số từ 00 đến 99.</li>
                                <li>So sánh với 2 số cuối giải ĐB miền Bắc.</li>
                                <li>Không thể chọn lại nếu đã bấm “Xác nhận”.</li>
                            </ul>
                        </div>
                    </div>
                    <div v-else class="mt-6">
                        <div class="flex items-center justify-between">
                            <div class="font-medium text-sm text-z-chroma-black">Số đã chọn</div>
                            <div class="lucky-number-mb__bet flex gap-x-4 font-normal text-2xl leading-12 text-center font-svn-vt">
                                <span v-for="(number, index) in firstTwoNumbers" :key="index">{{ number }}</span>
                            </div>
                        </div>
                        <div class="text-sm font-normal text-[#906411] mt-2.5 pb-[3.125rem] mb-3 border-b border-solid border-[#EEE2CB]">Đối chiếu kết quả với 2 số cuối giải Đặc biệt XSMB</div>
                    </div>
                    <div class="mt-1">
                        <label class="font-medium text-sm leading-5 tracking-[0%] text-z-chroma-black mb-1 block">Xổ số miền Bắc</label>
                        <div class="flex items-center font-normal text-sm leading-5 text-z-chroma-black h-11 bg-[#F5F6F7] rounded-lg border border-solid border-[#FBEBD3] px-3">{{ dateBetMB }}</div>
                    </div>
                    <div
                        v-if="(typeof luckyDraw?.lucky_number_1 !== 'string')"
                        @click="handleBet(2)" class="lucky-number-mb__btn mt-4 max-[360px]:mt-6"
                        :class="
                    isDisabled
                        ? 'grayscale-[1] pointer-events-none'
                        : ''
                "
                    >
                        <span>XÁC NHẬN</span>
                    </div>
                     <div
                        class="absolute bottom-2.5 left-0 w-full px-4 flex items-center justify-between"
                        :class="amountLuckyNumber1 == 0 ? 'opacity-0' : ''"
                    >
                        <div class="font-montserrat font-semibold text-xs text-white">QUỸ THƯỞNG</div>
                        <div class="lucky-number-mb__money" :class="[{'!text-lg':amountLuckyNumber2?.length > 10}]">{{ amountLuckyNumber1 }} VND</div>
                    </div>
                </template>
            </form>
            <form @submit.prevent="handleBet(3)" class="lucky-number-mb__item">
                <div
                    v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                    class="absolute top-0 left-0 size-full flex items-center justify-center"
                >
                    <img
                        width="50"
                        height="50"
                        :src="`${staticUrl}/loading.gif`"
                        alt="loading icon"
                    />
                </div>
                <template v-else>
                    <div class="lucky-number-mb__title text-2xl leading-6 tracking-[0%] font-svn-vt pt-2">đánh ĐỀ 3 SỐ</div>
                    <div v-if="(typeof luckyDraw?.lucky_number_2 !== 'string')" class="relative">
                        <label class="font-medium text-sm leading-5 tracking-[0%] text-z-chroma-black mt-5 mb-1 block">Đánh đề 3 số (000 - 999)</label>
                        <div class="flex items-center gap-[8px] pr-[28px]">
                            <CommonTextInputNumber
                                v-model="numberThree"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:truncate [&_input]:!border-[#FBEBD3] [&_input]:!p-3 [&_input]:!bg-white [&_input]:!rounded-lg [&_input]:!text-z-chroma-black [&_input]:placeholder:!text-z-dark-gray"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'numberThree')"
                            />
                            <CommonTextInputNumber
                                v-model="numberFour"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:truncate [&_input]:!border-[#FBEBD3] [&_input]:!p-3 [&_input]:!bg-white [&_input]:!rounded-lg [&_input]:!text-z-chroma-black [&_input]:placeholder:!text-z-dark-gray"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'numberFour')"
                            />
                            <CommonTextInputNumber
                                v-model="numberFive"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:truncate [&_input]:!border-[#FBEBD3] [&_input]:!p-3 [&_input]:!bg-white [&_input]:!rounded-lg [&_input]:!text-z-chroma-black [&_input]:placeholder:!text-z-dark-gray"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'numberFive')"
                            />
                        </div>
                        <div class="error-message  max-[360px]:text-xs text-sm text-[#FF0000] mt-1 h-5" :class="errorsThreeDigits.numberThree || errorsThreeDigits.numberFour || errorsThreeDigits.numberFive ? 'opacity-100' : 'opacity-0'">
                            {{ errorsThreeDigits.numberThree || errorsThreeDigits.numberFour || errorsThreeDigits.numberFive }}
                        </div>
                        <div class="absolute top-9 right-0 icon-input">
                            <img :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`" alt="tooltip" class="size-[1.25rem]">
                        </div>
                        <div class="box-tooltip hidden absolute bottom-16 -right-3 bg-[#1C1C1C] rounded-[4px] p-[4px] py-[3px] text-white font-semibold text-[12px] leading-[120%] w-[208px]">
                            <ul>
                                <li>Chọn 1 số từ 000 đến 999.</li>
                                <li>So sánh với 3 số cuối giải ĐB miền Bắc.</li>
                                <li>Không thể chọn lại nếu đã bấm “Xác nhận”.</li>
                            </ul>
                        </div>
                    </div>
                    <div v-else class="mt-6">
                        <div class="flex items-center justify-between">
                            <div class="font-medium text-sm text-z-chroma-black">Số đã chọn</div>
                            <div class="lucky-number-mb__bet flex gap-x-4 font-normal text-2xl leading-12 text-center font-svn-vt">
                                <span v-for="(number, index) in firstThreeNumbers" :key="index">{{ number }}</span>
                            </div>
                        </div>
                        <div class="text-sm font-normal text-[#906411] mt-2.5 pb-[3.125rem] mb-3 border-b border-solid border-[#EEE2CB]">Đối chiếu kết quả với 3 số cuối giải Đặc biệt XSMB</div>
                    </div>
                    <div class="mt-1">
                        <label class="font-medium text-sm leading-5 tracking-[0%] text-z-chroma-black mb-1 block">Xổ số miền Bắc</label>
                        <div class="flex items-center font-normal text-sm leading-5 text-z-chroma-black h-11 bg-[#F5F6F7] rounded-lg border border-solid border-[#FBEBD3] px-3">{{ dateBetMB }}</div>
                    </div>
                    <div
                        v-if="(typeof luckyDraw?.lucky_number_2 !== 'string')"
                        @click="handleBet(3)" class="lucky-number-mb__btn mt-4  max-[360px]:mt-6"
                        :class="
                    isDisabledThree
                        ? 'grayscale-[1] pointer-events-none'
                        : ''
                "
                    >
                        <span>XÁC NHẬN</span>
                    </div>
                    <div
                        class="absolute bottom-2.5 left-0 w-full px-4 flex items-center justify-between"
                        :class="amountLuckyNumber2 == 0 ? 'opacity-0' : ''"
                    >
                        <div class="font-montserrat font-semibold text-xs text-white">QUỸ THƯỞNG</div>
                        <div class="lucky-number-mb__money" :class="[{'!text-lg':amountLuckyNumber2?.length > 10}]">{{ amountLuckyNumber2 }} VND</div>
                    </div>
                </template>
            </form>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.lucky-number-mb {
    @apply flex gap-x-4 w-max px-[0.938rem];
    &__item {
        @apply relative px-6 w-[289px] h-[332px] last:w-[291px] last:px-[1.5625rem];
        background: url('/assets/images/danh-de-mien-phi/lucky-number/bg-lucky-number-mb.png') no-repeat
        center / 100% 100%;
        .icon-input {
            &:hover {
                & + .box-tooltip {
                    @apply block;
                }
            }
        }
        :deep(input) {
            &:focus {
                @apply shadow-[0px_0px_0px_2px_#F46E49];
            }
        }
    }
    &__title {
        background: linear-gradient(180deg, #CF3704 33.65%, #AD2106 70.08%, #CF1F04 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    &__btn {
        @apply font-mulish flex items-center justify-center font-black text-[0.9375rem] w-[11.25rem] h-9 mx-auto  max-[360px]:mt-6;
        background: url('/assets/images/danh-de-mien-phi/banner/btn-mb.png') no-repeat
        center / 100% 100%;
        span {
            background: linear-gradient(180deg, #EA462E 0%, #C7310C 100%),
            linear-gradient(180deg, #CF2604 33.65%, #A50E03 70.08%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    &__money {
        @apply font-montserrat font-bold text-xl leading-[30px];
        background: linear-gradient(338.45deg, #FFE57B 17.69%, #FFFFFF 65.98%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    &__bet {
        span {
            background: linear-gradient(180deg, #CF3704 33.65%, #AD2106 70.08%, #CF1F04 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
.box-tooltip {
    @media (max-width: 425px) {
        @apply py-1.5 text-[0.65rem];
    }
    ul {
        @apply pl-[20px];
        li {
            @apply list-disc;
        }
    }
    &::before {
        @apply content-[''] absolute w-0 h-0 border-t-4 border-t-[#1C1C1C] border-b-0 border-x-[5px] border-x-transparent border-solid right-[17px] -bottom-[4px];
    }
}
</style>
