<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { computed } from 'vue'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import type { LuckyDrawData, LuckyDrawDataInfo } from '~/interfaces/event'
import { BetType } from '~/interfaces/event'
const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { isLogged } = storeToRefs(useUserStoreInstance)
const { showLoginModal, showBetFreeModal, showBetConditionModal, showPromotionLimitModal, betType } = storeToRefs(
    useModalStoreInstance
)
const useEventDanhDeStoreInstance = useEventDanhDeStore()
const { getLuckyNumbers, getAmountLucky } = useEventDanhDeStoreInstance
const { isAccountPromotion } = storeToRefs(useEventDanhDeStoreInstance)

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
    luckyDrawInfo: {
        type: Object as PropType<LuckyDrawDataInfo>,
        required: true,
    },
    isLoadingInfo: {
        type: Boolean,
        default: false,
    },
    isLoadingLuckyDraw: {
        type: Boolean,
        default: false,
    }
})

const amountLuckyNumber1 = computed(() => {
    return getAmountLucky(props.luckyDrawInfo.lucky_number_1_txt, props.luckyDrawInfo.lucky_number_1)
})

const amountLuckyNumber2 = computed(() => {
    return getAmountLucky(props.luckyDrawInfo.lucky_number_2_txt, props.luckyDrawInfo.lucky_number_2)
})
const firstTwoNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_1?.toString())
})
const firstThreeNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_2?.toString())
})

const handleBet  = (type: BetType) => {
    if (isLogged.value) {
        if (isAccountPromotion.value) {
            showPromotionLimitModal.value = true;
            return
        }
        betType.value = type
        if (type === BetType.LUCKY_NUMBER_1 && props.luckyDraw?.lucky_number_1 === true) {
            showBetFreeModal.value = true
            return
        }
        if (type === BetType.LUCKY_NUMBER_2 && props.luckyDraw?.lucky_number_2 === true) {
            showBetFreeModal.value = true
            return
        }
        showBetConditionModal.value = true
        return
    }
    showLoginModal.value = true
}
</script>
<template>
    <div class="lucky-number">
        <div class="lucky-number__item">
            <div
                v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                class="absolute top-0 left-0 size-full flex items-center justify-center"
            >
                <img
                    width="50"
                    height="50"
                    :src="`${staticUrl}/loading.gif`"
                    alt="loading icon"
                />
            </div>
            <template v-else>
                <div class="font-montserrat font-bold text-base leading-7 tracking-[0px] align-middle uppercase text-z-chroma-black mt-[2px]">
                    {{ typeof luckyDraw?.lucky_number_1 !== 'string' ? 'số MAY MẮN' : 'Số đã chọn' }}
                </div>
                <div v-if="typeof luckyDraw?.lucky_number_1 !== 'string'" class="flex items-center justify-between mt-[15px]">
                    <div class="lucky-number__title text-[34px] leading-[47.31px] tracking-[0%] font-svn-vt mt-[5px]">đánh ĐỀ 2 SỐ</div>
                    <div @click="handleBet(2)" class="lucky-number__btn"><span>CHỌN SỐ</span></div>
                </div>
                <div v-else class="flex items-center justify-between mt-[20px]">
                    <div class="font-montserrat font-nomal text-xs leading-[18px] text-z-chroma-black mt-[5px]">Đối chiếu kết quả với 2 số cuối giải Đặc biệt XSMB</div>
                    <div class="lucky-number__bet flex gap-x-6 pr-2 font-normal text-[40px] leading-[100%] tracking-[0%] text-center font-svn-vt">
                        <span v-for="(number, index) in firstTwoNumbers" :key="index">{{ number }}</span>
                    </div>
                </div>
                <div
                    class="absolute bottom-[12px] left-0 w-full px-[42px] flex items-center justify-between"
                    :class="amountLuckyNumber1 == 0 ? 'opacity-0' : ''"
                >
                    <div class="font-montserrat font-bold text-base tracking-[0%] text-white">QUỸ THƯỞNG</div>
                    <div class="lucky-number__money"><span>{{ amountLuckyNumber1 }} VND</span></div>
                </div>
            </template>
        </div>
        <div class="lucky-number__item">
            <div
                v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                class="absolute top-0 left-0 size-full flex items-center justify-center"
            >
                <img
                    width="50"
                    height="50"
                    :src="`${staticUrl}/loading.gif`"
                    alt="loading icon"
                />
            </div>
            <template v-else>
                <div class="font-montserrat font-bold text-base leading-7 tracking-[0px] align-middle uppercase text-z-chroma-black mt-[2px]">
                    {{ typeof luckyDraw?.lucky_number_2 !== 'string' ? 'số MAY MẮN' : 'Số đã chọn' }}
                </div>
                <div v-if="typeof luckyDraw?.lucky_number_2 !== 'string'" class="flex items-center justify-between mt-[15px]">
                    <div class="lucky-number__title text-[34px] leading-[47.31px] tracking-[0%] font-svn-vt mt-[5px]">đánh ĐỀ 3 SỐ</div>
                    <div @click="handleBet(3)" class="lucky-number__btn"><span>CHỌN SỐ</span></div>
                </div>
                <div v-else class="flex items-center justify-between mt-[20px]">
                    <div class="font-montserrat font-nomal text-xs leading-[18px] text-z-chroma-black mt-[5px]">Đối chiếu kết quả với 3 số cuối giải Đặc biệt XSMB</div>
                    <div class="lucky-number__bet flex gap-x-6 pr-2 font-normal text-[40px] leading-[100%] tracking-[0%] text-center font-svn-vt">
                        <span v-for="(number, index) in firstThreeNumbers" :key="index">{{ number }}</span>
                    </div>
                </div>
                <div
                    class="absolute bottom-[12px] left-0 w-full px-[42px] flex items-center justify-between"
                    :class="amountLuckyNumber2 == 0 ? 'opacity-0' : ''"
                >
                    <div class="font-montserrat font-bold text-base tracking-[0%] text-white">QUỸ THƯỞNG</div>
                    <div class="lucky-number__money"><span>{{ amountLuckyNumber2 }} VND</span></div>
                </div>
            </template>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.lucky-number {
    @apply gap-x-[40px];
    &__item {
        @apply relative w-[calc((100%_-_40px)_/_2)] h-[186px] px-[38px] py-[12px];
        background: url('/assets/images/danh-de-mien-phi/lucky-number/bg-lucky-number.png') no-repeat
        center / 100% 100%;
    }
    &__title {
        background: linear-gradient(180deg, #CF3704 33.65%, #AD2106 70.08%, #CF1F04 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    &__btn {
        @apply font-montserrat flex items-center justify-center font-bold text-lg text-center w-[178px] h-[42px] cursor-pointer lg:hover:brightness-[1.08] transition-all;
        background: url('/assets/images/danh-de-mien-phi/banner/btn.png') no-repeat
        center / 100% 100%;
        span {
            background: linear-gradient(180deg, #EA462E 0%, #C7310C 100%),
            linear-gradient(180deg, #CF2604 33.65%, #A50E03 70.08%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    &__money {
        span {
            @apply font-extrabold text-[32px] leading-8 tracking-[0%];
            background: linear-gradient(338.45deg, #FFE57B 17.69%, #FFFFFF 65.98%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    &__bet {
        span {
            background: linear-gradient(180deg, #CF3704 33.65%, #AD2106 70.08%, #CF1F04 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
</style>
