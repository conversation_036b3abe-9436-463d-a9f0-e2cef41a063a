<template>
  <div class="flex items-center justify-center">
    <span class="relative flex h-2 w-2">
      <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-[#F71B26] opacity-75"></span>
      <span class="relative inline-flex rounded-full h-2 w-2 bg-[#F71B26]"></span>
    </span>
    <span class="text-white font-semibold text-base leading-[23px] font-roboto ml-1 flex items-center after:content-[''] after:inline-block after:w-px after:h-3.5 after:bg-[#3E4049] after:ml-2 after:min-w-[1px] after:min-h-[14px] after:align-middle">LIVE</span>
  </div>
</template>

<style lang="scss" scoped>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
</style>