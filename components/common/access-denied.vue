<template>
    <div
        class="mx-auto flex h-screen w-screen max-w-[1140px] flex-col items-center justify-center gap-[33px] px-[35px] lg:flex-row"
    >
        <CommonImage
            :src="`${staticUrl}/error/img-access-denied.png`"
            alt="access Restricted"
            class="h-[200px] w-[280px] min-w-[280px] lg:h-[300px] lg:w-[420px] lg:min-w-[420px]"
            width="420"
            height="300"
        />
        <div
            class="flex flex-col items-start justify-center gap-6 lg:w-[calc(100%-420px)]"
        >
            <div
                class="flex items-start justify-center gap-3 lg:mb-2 lg:items-center lg:gap-6"
            >
                <img
                    :src="`${staticUrl}/error/img-uk-flag.svg`"
                    alt="access Restricted"
                    class="h-8 w-8 lg:h-[50px] lg:w-[50px]"
                    width="50"
                    height="50"
                />
                <div class="flex flex-col items-start gap-1 lg:gap-2">
                    <h1
                        class="text-sm font-semibold text-[#F6F6F6] lg:font-normal"
                    >
                        Access Restricted
                    </h1>
                    <p class="text-xs text-[#AAAAAA] lg:text-sm">
                        Sorry, your region is not supported.
                    </p>
                </div>
            </div>

            <div
                class="flex items-start justify-center gap-3 lg:items-center lg:gap-6"
            >
                <img
                    :src="`${staticUrl}/error/img-vn-flag.svg`"
                    alt="access Restricted"
                    class="h-8 w-8 lg:h-[50px] lg:w-[50px]"
                    width="50"
                    height="50"
                />
                <div class="flex flex-col items-start gap-1 lg:gap-2">
                    <h1
                        class="text-sm font-semibold text-[#F6F6F6] lg:font-normal"
                    >
                        Truy Cập Bị Từ Chối
                    </h1>
                    <p class="text-xs text-[#AAAAAA] lg:text-sm">
                        Xin lỗi, khu vực của bạn không nằm trong danh sách hỗ
                        trợ.
                    </p>
                </div>
            </div>

            <hr class="w-full border-t border-[#36322F] lg:max-w-[447px]" />

            <div
                v-if="ip || error.ip || rayId || error.rayId"
                class="flex items-start justify-center gap-3 text-sm text-[#AAAAAA] lg:ml-[75px]"
            >
                <div
                    v-if="ip || error.ip"
                    class="w-1/2 lg:w-auto lg:min-w-[161px]"
                >
                    <span
                        class="text-nowrap font-semibold text-[#F6F6F6] lg:font-normal"
                    >
                        IP:
                    </span>
                    <span>{{ ip || error.ip }}</span>
                </div>

                <div
                    v-if="rayId || error.rayId"
                    class="w-1/2 text-nowrap lg:w-auto lg:min-w-[161px]"
                >
                    <span class="font-semibold text-[#F6F6F6] lg:font-normal">
                        RAY ID:
                    </span>
                    <span>{{ rayId || error.rayId }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
const staticUrl = useRuntimeConfig().public.staticUrl
const brandName = useRuntimeConfig().public.BRAND_NAME
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    message: {
        type: String,
        default: '',
    },
    error: {
        type: Object,
        default: () => ({}),
    },
})

useHead({
    title: `Access Restricted - ${brandName}`,
    meta: [
        {
            hid: 'description',
            name: 'description',
            content: 'Sorry, your region is not supported.',
        },
    ],
})
const ip = ref(props.error.ip)

const rayId = ref(props.error.rayId)

onMounted(() => {
    const headers = useRequestHeaders()
    ip.value =
        headers['cf-connecting-ip'] ||
        headers['x-forwarded-for'] ||
        headers['x-real-ip'] ||
        props.error.message
    rayId.value = headers['x-ray-id'] || headers['cf-ray'] || props.error.stack
})
</script>
