<template>
    <div class="title-event">
        <div
            class="title-hotmatch text-center text-2xl font-extrabold uppercase leading-[calc(30/24)] text-white lg:text-[50px] lg:leading-[60px]"
        >
            {{ title }}
        </div>
        <div
            class="mt-1 text-center text-sm font-semibold uppercase text-[#EBB23B] lg:mt-2 lg:text-[26px] lg:font-bold lg:leading-[calc(32.5/26)] lg:tracking-[2%]"
        >
            {{ suggestion }}
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps({
    title: {
        type: String,
        default: '',
    },
    suggestion: {
        type: String,
        default: '',
    },
})
</script>

<style scoped></style>
