<template>
    <div class="banner-prizes flex lg:hidden">
        <div
            v-if="!isAfterEndDate"
            class="banner-prizes-button flex min-w-[137px] items-center justify-center bg-[url('/assets/images/club-world-cup/ranking/bg-button.png')] bg-contain bg-center bg-no-repeat text-[10.7px] font-[900] uppercase italic text-[#000]"
            @click="handleClick"
        >
            {{ t('common.join_now') }}
        </div>
        <div class="banner-prizes-group">
            <div class="banner-prizes-slider">
                <Swiper
                    :effect="'fade'"
                    :modules="[
                        Pagination,
                        EffectCards,
                        EffectCreative,
                        FreeMode,
                        Navigation,
                    ]"
                    :slidesPerView="3.15"
                    :slidesPerGroup="1"
                    :allowTouchMove="true"
                    :spaceBetween="0"
                    :breakpoints="{
                        490: {
                            slidesPerView: 3,
                            spaceBetween: 14,
                        },
                        768: {
                            slidesPerView: 6,
                            spaceBetween: 14,
                        },
                    }"
                >
                    <SwiperSlide v-for="prize in listPrizes" :key="prize.id">
                        <img
                            :src="`${staticUrl}/${prize.imgPath}`"
                            class="aspect-1 min-h-[92px] min-w-[92px]"
                            alt="prize"
                        />
                    </SwiperSlide>
                </Swiper>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'
import {
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
} from 'swiper/modules'
import { useCountdown } from '~/composables/common/use-countdown'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const staticUrl = useRuntimeConfig().public.staticUrl
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)
const useModalStoreInstance = useModalStore()
const { showRegisterModal } = storeToRefs(useModalStoreInstance)
const { CLUB_WORLD_CUP_EVENT_START_DAY, CLUB_WORLD_CUP_EVENT_END_DAY } =
    useRuntimeConfig().public

const { isAfterEndDate } = useCountdown(
    CLUB_WORLD_CUP_EVENT_START_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY
)

const handleClick = () => {
    if (user.value) {
        navigateTo(PAGE_URL.SPORT)
    } else {
        showRegisterModal.value = true
    }
}

const listPrizes = [
    {
        id: 1,
        imgPath: 'olympic-2024/sp/prize1.svg',
    },
    {
        id: 2,
        imgPath: 'olympic-2024/sp/prize2.svg',
    },
    {
        id: 3,
        imgPath: 'olympic-2024/sp/prize3.svg',
    },
    {
        id: 4,
        imgPath: 'olympic-2024/sp/prize4.svg',
    },
    {
        id: 5,
        imgPath: 'olympic-2024/sp/prize5.svg',
    },
    {
        id: 6,
        imgPath: 'olympic-2024/sp/prize6.svg',
    },
]
</script>

<style lang="scss" scoped>
.banner-prizes {
    @apply relative mt-4 h-[144px] w-screen bg-[url(/assets/images/club-world-cup/prizes/bg.png)] bg-center bg-no-repeat;
    background-size: 100% 100%;

    .banner-prizes-group {
        @apply mx-[auto] my-[0] w-[89%] overflow-x-hidden;
    }

    .banner-prizes-button {
        @apply absolute -top-3.5 left-2/4 z-10 h-10 w-[8.4375rem] -translate-x-1/2 transform cursor-pointer;
    }

    .banner-prizes-slider {
        .swiper-wrapper {
            @apply mx-[auto] my-[0] h-[144px];
        }

        :deep(.swiper-wrapper) {
            @apply mx-[auto] my-[0] h-[144px];
            .swiper-slide {
                @apply flex -translate-y-[12px] transform items-end justify-center;
            }
        }
    }
}
</style>
