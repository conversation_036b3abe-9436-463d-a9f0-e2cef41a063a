<template>
    <div :class="className">
        <div
            v-if="!isAfterEndDate && !isFinish"
            :class="[
                'countdown-top',
                isAfterStartDate ? 'is-start' : '',
                isAfterEndDate ? 'is-end' : '',
            ]"
        >
            <div
                :class="[
                    'countdown-top__content',
                    isAfterStartDate ? 'remaining' : '',
                ]"
            >
                <div
                    class="countdown-title text-[10px] font-medium text-white lg:text-lg"
                >
                    <p>
                        {{
                            !isAfterStartDate
                                ? `${t('common.countdown.start_at')}:`
                                : `${t('common.countdown.remaining')}:`
                        }}
                    </p>
                </div>
                <div
                    class="countdown-list flex items-center gap-x-[3px] text-white lg:min-w-[13.9375rem] lg:gap-x-[0.4375rem]"
                >
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cDays) }}</p>
                        <p class="text">{{ t('common.countdown.date') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cHours) }}</p>
                        <p class="text">{{ t('common.countdown.hour') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cMinutes) }}</p>
                        <p class="text">{{ t('common.countdown.minute') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div
            v-else
            class="is-finish absolute flex items-center justify-center lg:right-[17%] lg:top-[33.8%] lg:w-[28.125%]"
        >
            <div class="countdown">
                {{ t('common.end_event') }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useCountdown } from '~/composables/common/use-countdown'

const { t } = useI18n()

defineProps({
    className: {
        type: String,
        default: '',
    },
})
const { CLUB_WORLD_CUP_EVENT_START_DAY, CLUB_WORLD_CUP_EVENT_END_DAY } =
    useRuntimeConfig().public
const {
    isFinish,
    isAfterEndDate,
    isAfterStartDate,
    cDays,
    cHours,
    cMinutes,
    formatTimer,
} = useCountdown(CLUB_WORLD_CUP_EVENT_START_DAY, CLUB_WORLD_CUP_EVENT_END_DAY)
</script>

<style scoped lang="scss" rel="preload">
.countdown-top {
    @apply absolute left-[6%] top-[67%] flex items-center justify-center sm:left-[10%] lg:left-auto lg:right-[15.7%] lg:top-[54%] lg:w-[27.313%];
    &.is-start {
        @apply left-[15px] xs:left-[4%] sm:left-[10%] lg:left-auto;
    }
    &.is-end {
        @apply left-[43px] xs:left-[3%] sm:left-[12%] lg:left-auto;
    }
    &__content {
        @apply flex h-[30px] min-w-[178.2px] items-center justify-center gap-x-[4px] rounded border-[0.83px] border-solid border-[#FFFFFF33] bg-[#EDA0A066] px-1.5 lg:right-[17%] lg:top-[24.8%] lg:h-[3.75rem] lg:min-w-[27.4rem] lg:gap-x-2.5 lg:rounded-lg lg:px-2;
        backdrop-filter: blur(10px);
        &.remaining {
            @apply w-[12.313rem] lg:w-[23.9rem];
            @media (max-width: 374px) {
                @apply w-[13.313rem];
            }
        }
    }
}
.countdown {
    @apply h-[2.25rem] w-[12.25rem] rounded-[6px] border border-solid border-[#FFFFFF33] bg-[#EDA0A066] text-sm font-bold text-white backdrop-blur-md lg:h-[3.25rem] lg:w-[16.1875rem] lg:rounded-[8.57px] lg:text-lg;
}
.countdown-item {
    @apply flex items-center gap-x-[3px] lg:gap-x-[0.4375rem];
    .time {
        @apply flex size-[1.1875rem] items-center justify-center rounded bg-[#00000080] text-[11px] font-bold shadow-[0px_0px_5.71px_0px_#00000040] lg:size-[2.375rem] lg:rounded-[0.356875rem] lg:text-[1.25rem];
    }
    .text {
        @apply text-[10px] font-semibold lg:text-[1.125rem];
    }
}
.is-finish {
    @include mba() {
        @apply bottom-4 left-2/4 -translate-x-2/4;
    }
}
</style>
