<template>
    <div class="rank-table">
        <div class="rank-table__container">
            <div
                :class="[
                    'rank-table__head',
                    { 'rank-table__head--mb': !isDeskTop },
                ]"
            >
                <template v-if="!isDeskTop">
                    <div class="rank-table__head-item text-left">Th<PERSON> hạng</div>
                    <div class="rank-table__head-item text-right">
                        Tiền thưởng
                    </div>
                </template>
                <template v-else>
                    <div class="rank-table__head-item">Th<PERSON> hạng</div>
                    <div class="rank-table__head-item"><PERSON><PERSON><PERSON><PERSON> chơi</div>
                    <div class="rank-table__head-item">Ch<PERSON><PERSON> nhiều nhất</div>
                    <div class="rank-table__head-item"><PERSON><PERSON> c<PERSON>c</div>
                    <div class="rank-table__head-item">Tiền thưởng</div>
                </template>
            </div>
            <div class="rank-table__body scrollbar-event">
                <template v-if="!isDeskTop">
                    <ClubWorldCupCommonRankingsItems
                        v-for="(user, index) in users"
                        :key="index"
                        :user="user"
                        :rankNumber="index + 1"
                        :isShowMoney="isShowMoney"
                        :isActivated="position === index + 1"
                        isMobile
                    />
                </template>
                <template v-else>
                    <ClubWorldCupCommonRankingsItems
                        v-for="(user, index) in users"
                        :key="index"
                        :user="user"
                        :rankNumber="index + 1"
                        :isShowMoney="isShowMoney"
                        :isActivated="position === index + 1"
                    />
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { UserRanking } from '~/composables/user/interface'

const { isDeskTop } = useWindowSize()
const props = defineProps({
    users: {
        type: Array as () => UserRanking[],
        default: () => [],
    },
    isShowMoney: {
        type: Boolean,
        default: false,
    },
    position: {
        type: Number,
        default: 0,
    },
})
</script>

<style lang="scss" scoped>
.rank-table {
    &__head {
        @apply grid grid-cols-[130px_repeat(4,_1fr)] text-center;
        &--mb {
            @apply grid-cols-[repeat(2,_1fr)];
        }
    }
    &__body {
        @apply max-h-[556px] overflow-y-auto overflow-x-hidden rounded-none;
        &::-webkit-scrollbar {
            @apply h-[4px] w-[4px];
        }

        &::-webkit-scrollbar-thumb {
            @apply rounded-[10px] bg-[#FFFFFF66];
        }

        &::-webkit-scrollbar-thumb:hover {
            @apply bg-[#FFFFFF66];
        }
    }
    &__head-item {
        @apply px-0 py-2.5 text-sm font-semibold leading-[20px] text-white lg:text-base lg:text-white;
    }
    :deep(.table-item) {
        @apply mb-1;
    }
    @media (max-width: 768px) {
        .rank-table {
            &__body {
                @apply max-h-[121vw];
            }
        }
    }
    @media (max-width: 376px) {
        .rank-table {
            &__body {
                @apply max-h-[118.5vw];
            }
        }
    }
}
</style>
