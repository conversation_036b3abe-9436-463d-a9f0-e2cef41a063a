<template>
    <div class="current-top">
        <div class="current-top__container">
            <div class="current-top__inner">
                <div v-if="isJoinNow" class="current-top__text">
                    Cùng đua top để nhận thưởng khủng
                </div>
                <div v-else class="current-user">
                    <div class="current-user__inner">
                        <div
                            class="current-user__stt"
                            v-if="
                                currentPositionUser?.position < 4 &&
                                currentPositionUser?.position > 0
                            "
                        >
                            <img
                                class="top-number__top"
                                :src="`${staticUrl}/olympic-2024/common/rankings/top_${currentPositionUser?.position}.png`"
                                alt="Top"
                            />
                        </div>
                        <div v-else class="current-user__stt out-top">
                            {{ currentPositionUser?.position || '99+' }}
                        </div>

                        <div class="current-user__name">
                            {{ username }}
                        </div>
                        <div class="current-user__text">
                            <PERSON><PERSON><PERSON> đã cược
                            <span>
                                {{
                                    NumberUtils.formatMoneyWithAmount(
                                        Number(
                                            userPosition?.stake &&
                                                !isNaN(userPosition?.stake)
                                                ? userPosition.stake.toFixed()
                                                : 0
                                        ),
                                        1,
                                        CURRENCY.D
                                    )
                                }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="current-top__inner">
                <div class="current-top__action">
                    <button type="button" @click="handleRacingTop">
                        {{ username ? 'ĐUA TOP NGAY' : 'ĐĂNG KÝ ĐUA TOP' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'
import { NumberUtils } from '~/utils'
import { CURRENCY } from '~/constants/common'

const useModalStoreInstance = useModalStore()
const { showRegisterModal } = storeToRefs(useModalStoreInstance)
const staticUrl = useRuntimeConfig().public.staticUrl
const props = defineProps({
    username: {
        type: String,
        default: '',
    },
    current: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 0,
    },
    userPosition: {
        type: Object,
    },
})


const isJoinNow = computed(() => {
    return !props.username && Object.keys(props.userPosition ?? {}).length === 0
})

const username = computed(() => {
    return props.username ? `${props.username?.substring(0, 4)}***` : ''
})

const currentPositionUser = computed(() => {
    return isJoinNow.value ? {} : props.userPosition
})

const handleRacingTop = () => {
    if (props.username) {
        navigateTo(PAGE_URL.SPORT)
    } else {
        showRegisterModal.value = true
    }
}
</script>

<style lang="scss" scoped>
.current-top {
    &__container {
        @apply relative flex flex-col items-center justify-between gap-x-3 gap-y-4;
        @media (min-width: 1024px) {
            @apply flex-row;
        }
    }
    &__text {
        @apply text-sm font-normal leading-[20px] text-white lg:text-base;
        @media (min-width: 1024px) {
            @apply text-base leading-[22px];
        }
    }
    &__action {
        button {
            @apply h-[40px] w-[172px] rounded-[6px] border-none bg-[#FFD55B] px-[16px] py-[11px] text-center text-sm font-bold not-italic leading-[15px] text-black outline-[none];
            @media (min-width: 1024px) {
                @apply px-[29px] py-[12px];
            }
        }
    }
    .current-user {
        &__inner {
            @apply flex items-center;
        }
        &__name {
            @apply my-[0] ml-3 mr-3 text-xs font-semibold leading-[15px] text-white;
            @media (min-width: 700px) {
                @apply my-[0] ml-[22px] mr-[44px] text-lg leading-[22px];
            }
        }
        &__stt {
            @apply flex h-[1.875rem] w-[1.875rem] items-center justify-center bg-no-repeat p-[5px] text-center text-xs font-extrabold italic text-white;
            text-shadow: 0 0.836365px 0.836365px rgba(0, 0, 0, 0.25);
            &.out-top {
                background: url(/assets/images/olympic-2024/common/rankings/top_bg1.svg);
                @apply bg-contain bg-no-repeat;
            }
        }
        &__text {
            @apply flex items-center gap-x-2 py-[0] pl-3 text-sm text-white [border-left:1px_solid_rgba(255,_255,_255,_0.2)] lg:text-base;
            @media (min-width: 700px) {
                @apply px-[22px] py-[0];
            }
            span {
                @apply font-semibold text-[#FFD55B];
            }
        }
    }
}
</style>
