<template>
    <div :class="className">
        <div v-if="!isAfterEndDate && !isFinish" class="countdown-top">
            <div
                :class="[
                    'countdown-top__content',
                    isStartDate ? 'remaining' : '',
                ]"
            >
                <div
                    class="countdown-title text-xs font-medium text-white lg:text-lg"
                >
                    <p>
                        {{
                            !isStartDate
                                ? `${t('common.countdown.start_at')}:`
                                : `${t('common.countdown.remaining')}:`
                        }}
                    </p>
                </div>
                <div
                    class="countdown-list flex min-w-[11rem] items-center gap-x-1 text-white lg:min-w-[17rem] lg:gap-x-[0.44625rem]"
                >
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cDays) }}</p>
                        <p class="text">{{ t('common.countdown.date') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cHours) }}</p>
                        <p class="text">{{ t('common.countdown.hour') }}</p>
                    </div>
                    <div class="countdown-item">
                        <p class="time">{{ formatTimer(cMinutes) }}</p>
                        <p class="text">{{ t('common.countdown.minute') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div
            v-else
            class="is-finish absolute flex h-[38px] w-[173px] items-center justify-center lg:right-[19%] lg:top-[39.8%] lg:w-[28.125%]"
        >
            <div class="countdown">
                {{ t('common.end_event') }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useCountdown } from '~/composables/common/use-countdown'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps({
    className: {
        type: String,
        default: '',
    },
})
const {
    CLUB_WORLD_CUP_EVENT_START_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY,
} = useRuntimeConfig().public
const {
    isFinish,
    isStartDate,
    isAfterEndDate,
    cDays,
    cHours,
    cMinutes,
    formatTimer,
} = useCountdown(
    CLUB_WORLD_CUP_EVENT_START_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY
)
</script>

<style scoped lang="scss" rel="preload">
.countdown-top {
    @apply absolute flex  items-center justify-center lg:right-[16.2%] lg:top-[38%] lg:w-[28.125%];
    &__content {
        @apply flex h-[2.5rem] w-[18.0625rem] items-center justify-center gap-x-3 rounded-lg border border-solid border-[#FFFFFF33] bg-[#EDA0A066] lg:right-[17%] lg:top-[24.8%] lg:h-[3.75rem] lg:w-[27.125rem] lg:gap-x-2.5;
        backdrop-filter: blur(6px);
        &.remaining {
            @apply w-[16.5rem] lg:w-[23.9rem];
        }
    }
    @include mba() {
        @apply bottom-4 left-2/4 -translate-x-2/4;
    }
}
.countdown {
    @apply h-[2.25rem] w-[12.25rem] rounded-[6px] border border-solid border-[#FFFFFF33] bg-[#EDA0A066] text-sm font-bold text-white backdrop-blur-md lg:h-[3.75rem] lg:w-[16.1875rem] lg:rounded-[8.57px] lg:text-lg;
}
.countdown-item {
    @apply flex items-center gap-x-1 lg:gap-x-[0.44625rem];
    .time {
        @apply flex size-[1.5475rem] items-center justify-center rounded bg-[#00000080] text-sm font-bold  shadow-[0px_0px_5.71px_0px_#00000040] lg:size-[2.375rem] lg:rounded-[0.356875rem] lg:text-xl;
    }
    .text {
        @apply text-xs font-semibold lg:text-lg;
    }
}
.is-finish {
    @include mba() {
        @apply bottom-[0.7rem] left-2/4 -translate-x-2/4;
        .countdown {
            @apply h-[38px] w-[173px] text-xs font-bold;
        }
    }
}
</style>
