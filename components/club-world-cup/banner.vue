<template>
    <div class="hero-banner">
        <div class="relative">
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/club-world-cup/pc.png`"
                :srcMb="`${staticUrl}/club-world-cup/mb.jpg`"
                alt="banner"
                @load="handleImageLoaded"
            />
            <LazyClubWorldCupCountdown
                v-show="isClient && showButton && imageLoaded"
            />
            <div
                v-if="!isAfterEndDate && showButton && imageLoaded"
                class="absolute hidden items-center justify-center lg:right-[16%] lg:top-[47.8%] lg:flex lg:w-[28.125%]"
            >
                <button
                    type="button"
                    @click="handleClick"
                    :class="['event-button ', !!user ? 'is-logged' : '']"
                ></button>
            </div>
        </div>
        <ClubWorldCupCommonPrizes class="flex lg:hidden" />
    </div>
</template>

<script setup lang="ts">
import { useCountdown } from '~/composables/common/use-countdown'
import { PAGE_URL } from '~/constants/page-urls'

const staticUrl = useRuntimeConfig().public.staticUrl
const router = useRouter()
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)
const useModalStoreInstance = useModalStore()
const { showRegisterModal } = storeToRefs(useModalStoreInstance)
const {
    CLUB_WORLD_CUP_EVENT_START_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY,
} = useRuntimeConfig().public

const isClient = ref(false)
const showButton = ref(false)
const imageLoaded = ref(false)
const { isAfterEndDate } = useCountdown(
    CLUB_WORLD_CUP_EVENT_START_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY
)

const handleImageLoaded = () => {
    imageLoaded.value = true
}

const handleClick = () => {
    if (user.value) {
        router.push(PAGE_URL.SPORT)
    } else {
        showRegisterModal.value = true
    }
}

onMounted(() => {
    nextTick(() => {
        if (process.client) {
            isClient.value = true
            setTimeout(() => {
                showButton.value = true
            }, 300)
        }
    })
})
</script>
<style lang="scss" scoped>
.hero-banner {
    @apply aspect-[375/316] lg:aspect-[960/463];
}
.event-button {
    @apply h-[5.625rem] w-[15.1875rem] animate-[0.7s_brightness_infinite] bg-[url('/assets/images/club-world-cup/pc/dua-top.png')] bg-cover bg-center bg-no-repeat;

    &.is-logged {
        @apply bg-[url('/assets/images/club-world-cup/pc/thamgia.png')] bg-cover bg-center bg-no-repeat;
    }
}
@keyframes brightness {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.1);
    }
    100% {
        filter: brightness(1);
    }
}
</style>
