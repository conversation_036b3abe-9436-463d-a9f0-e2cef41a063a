<template>
    <div>
        <div class="justify-between gap-6 lg:flex">
            <div class="relative box-border flex-1">
                <div class="item-total overflow-hidden">
                    <div class="icon lg:mb-3">
                        <img
                            :src="`${staticUrl}/pages/user/account/plan/total-today.svg`"
                            alt="icon"
                        />
                    </div>
                    <div class="content">
                        <div class="label max-w-full lg:max-w-20">
                            {{ t('user.overview.plan.total_bet_today') }}
                        </div>
                        <div class="amount">
                            {{ NumberUtils.formatAmount(betToday) }}
                        </div>
                    </div>
                    <img
                        class="bg-total img-phone"
                        :src="`${staticUrl}/pages/user/account/plan/bg-total-today.svg`"
                        alt="icon"
                    />
                </div>
                <div class="absolute right-1.5 top-1.5">
                    <div class="relative">
                        <img
                            :src="`${staticUrl}/account/overview/info.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="!isTooltipVisible1"
                            @mouseenter="isTooltipVisible1 = true"
                        />
                        <img
                            :src="`${staticUrl}/account/overview/info-hover.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="isTooltipVisible1"
                            @mouseleave="isTooltipVisible1 = false"
                        />
                        <div
                            class="tooltip absolute bottom-7 left-[-110%] z-[100] w-[150px] -translate-x-1/2 rounded bg-[#d07400] p-2 text-sm text-[#FBFDFF] lg:bottom-[30px] lg:left-1/2 lg:w-[388px]"
                            v-show="isTooltipVisible1"
                            @mouseleave="isTooltipVisible1 = false"
                        >
                            {{ t('user.overview.plan.total_bet') }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative box-border flex-1">
                <div class="item-total overflow-hidden">
                    <div class="icon lg:mb-3">
                        <img
                            :src="`${staticUrl}/pages/user/account/plan/return-today.svg`"
                            alt="icon"
                        />
                    </div>
                    <div class="content">
                        <div class="label max-w-full lg:max-w-20">
                            {{ t('user.overview.plan.refund_today') }}
                        </div>
                        <div class="amount">
                            {{ NumberUtils.formatAmount(commissionToday) }}
                        </div>
                    </div>
                    <img
                        class="bg-total img-money"
                        :src="`${staticUrl}/pages/user/account/plan/bg-return-today.svg`"
                        alt="icon"
                    />
                </div>
                <div class="absolute right-1.5 top-1.5">
                    <div class="relative">
                        <img
                            :src="`${staticUrl}/account/overview/info.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="!isTooltipVisible2"
                            @mouseenter="isTooltipVisible2 = true"
                        />
                        <img
                            :src="`${staticUrl}/account/overview/info-hover.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="isTooltipVisible2"
                            @mouseleave="isTooltipVisible2 = false"
                        />
                        <div
                            class="tooltip absolute left-[-110%] top-[calc(100%_-_160px)] z-[10] w-[150px] -translate-x-1/2 rounded bg-[#d07400] p-2 text-sm text-[#FBFDFF] lg:left-1/2 lg:top-[-350%] lg:w-[388px]"
                            v-show="isTooltipVisible2"
                            @mouseleave="isTooltipVisible2 = false"
                        >
                            Hoàn trả hôm nay sẽ là số tiền mà {{ brandName }} sẽ
                            cập nhật trả lại cho bạn vào lúc 11:45AM hằng ngày.
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative box-border flex-1">
                <div class="item-total overflow-hidden">
                    <div class="icon lg:mb-3">
                        <img
                            :src="`${staticUrl}/pages/user/account/plan/return-total.svg`"
                            alt="icon"
                        />
                    </div>
                    <div class="content">
                        <div class="label">
                            {{ t('user.overview.plan.refund_total') }}
                        </div>
                        <div class="amount">
                            {{ NumberUtils.formatAmount(commissionAll) }}
                        </div>
                    </div>
                    <img
                        class="bg-total img-pkg"
                        :src="`${staticUrl}/pages/user/account/plan/bg-return-total.svg`"
                        alt="icon"
                    />
                </div>
                <div class="absolute right-1.5 top-1.5">
                    <div class="relative">
                        <img
                            :src="`${staticUrl}/account/overview/info.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="!isTooltipVisible3"
                            @mouseenter="isTooltipVisible3 = true"
                        />
                        <img
                            :src="`${staticUrl}/account/overview/info-hover.svg`"
                            alt="info"
                            class="info-icon size-[18px] cursor-pointer"
                            v-show="isTooltipVisible3"
                            @mouseleave="isTooltipVisible3 = false"
                        />
                        <div
                            class="tooltip absolute left-[-110%] top-[calc(100%_-_120px)] z-[10] w-[150px] -translate-x-1/2 rounded bg-[#d07400] p-2 text-sm text-[#FBFDFF] lg:left-1/2 lg:top-[-350%] lg:w-[388px]"
                            v-show="isTooltipVisible3"
                            @mouseleave="isTooltipVisible3 = false"
                        >
                            Tổng hoàn trả là tổng số tiền hoàn trả bạn đã nhận
                            được tại {{ brandName }}.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <UserAccountPromotionNote class="hidden lg:block" />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

defineProps({
    betToday: {
        type: Number,
        default: 0,
    },
    commissionAll: {
        type: Number,
        default: 0,
    },
    commissionToday: {
        type: Number,
        default: 0,
    },
})

const { t } = useI18n()
const { BRAND_NAME: brandName } = useRuntimeConfig().public
const staticUrl = useRuntimeConfig().public.staticUrl
const isTooltipVisible1 = ref(false)
const isTooltipVisible2 = ref(false)
const isTooltipVisible3 = ref(false)
</script>

<style lang="scss" scoped>
.item-total {
    @apply relative mb-4 flex items-center rounded-[0.625rem] border border-solid border-[rgba(255,255,255,0.15)] bg-[#2A2A2A] leading-[1.21] lg:mb-0 lg:block lg:flex-1 lg:p-4;

    .bg-total {
        @apply absolute -right-1 bottom-3 lg:bottom-0 lg:right-[0.938rem];
    }
    .img-phone {
        @apply -right-1 w-[5.813rem] lg:-right-10 lg:w-[7.5rem];
    }
    .img-money {
        @apply -right-1 w-[4.5rem] lg:-right-5 lg:w-[4.813rem];
    }
    .img-pkg {
        @apply right-0 w-[5.688rem] lg:-right-8 lg:w-[7.375rem];
    }
    .content {
        @apply pl-3 lg:pl-0;
        .label {
            @apply mb-1 text-sm font-semibold text-white lg:min-h-10;
        }

        .amount {
            @apply text-base font-bold leading-normal text-z-red-dit lg:text-xl;
        }
    }
}

.tooltip {
    &::before {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #d07400;
    }
    @include mba() {
        &::before {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 30px;
            left: auto;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid #d07400;
        }
    }
}
</style>
