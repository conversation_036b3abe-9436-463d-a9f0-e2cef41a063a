<template>
    <CommonModal @close="closeSelf" size="xxl" sticky>
        <template #default>
            <div class="aff-gift__body">
                <div class="mb-2">{{ $t('fifa_club_popup.title') }}</div>
                <div
                    class="aff-gift__amount mb-2 flex h-[86px] items-center justify-center rounded-xl bg-white text-center font-black leading-[60px]"
                >
                    <span class="text-[32px] uppercase">
                        {{
                            amountText
                        }}
                    </span>
                </div>
                <div class="whitespace-pre-line">
                    <div>{{ $t('fifa_club_popup.description1') }}</div>
                    <div class="font-bold uppercase">
                        {{ $t('fifa_club_popup.description2') }}
                    </div>
                </div>
                <div class="aff-gift__btn">
                    <span @click="closeSelf">{{
                        $t('fifa_club_popup.btn')
                    }}</span>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { NumberUtils } from '~/utils'
const brandName = useRuntimeConfig().public.BRAND_NAME
const useModalStoreInstance = useModalStore()

const usePromotionInstance = usePromotion()
const { handleModalClose } = useModalStoreInstance
const { closeFifaClubModal } = usePromotionInstance
const { fifaClubDataPopup } = storeToRefs(usePromotionInstance)

const amountText = computed(() => {
    const amountTxt = fifaClubDataPopup.value?.amount_txt
    if (amountTxt) {
        if (amountTxt.toUpperCase().includes('VND')) {
            return amountTxt
        }
        return `${amountTxt} VND`
    }
    return NumberUtils.formatAmount(
        fifaClubDataPopup?.amount,
        'VND'
    )
})

const closeSelf = async () => {
    await closeFifaClubModal(fifaClubDataPopup.value?.id)
    handleModalClose()
}
</script>
<style lang="scss" scoped>
:deep(.modal-dialog__wrap) {
    @apply p-0 lg:p-2;
}
:deep(.modal-content) {
    @apply flex size-full h-[674px] flex-col justify-center py-5 lg:w-[658px];
    background: url('/assets/images/fifa-club-popup/bg-light.avif') center
        no-repeat;
    background-size: 110% 100%;
    @media (max-width: 992px) {
        background-size: 140% 100%;
    }
}

.aff-gift {
    &__body {
        background: url('/assets/images/fifa-club-popup/bg-gift.avif') center top
            no-repeat;
        background-size: 100% 100%;
        @apply mx-auto h-[354px] w-[376px] max-w-full px-[38.5px] pt-[140px] text-sm leading-5 text-black;
    }
    &__amount {
        span {
            @apply font-bold;
            background: linear-gradient(90deg, #d00003 0%, #9d0003 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
    }
    &__btn {
        span {
            @apply mx-auto mt-8 lg:mt-[44px] xl:mt-10 flex h-[39.72px] w-[163.38px] cursor-pointer items-center justify-center text-base font-extrabold leading-6 text-[#1B1B1B];
            background: url('/assets/images/fifa-club-popup/btn.avif') center
                no-repeat;
            background-size: 100% 100%;
        }
    }
}
</style>
