<template>
    <CommonModal
        :show="showFifaClubBannerModal"
        @close="closeSelf"
        size="xxl"
        class="z-[103]"
    >
        <template #header>
            <div class="relative">
                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute right-2 top-2 lg:right-6 lg:top-2"
                    @click="closeSelf"
                >
                    <img
                        :src="'/assets/images/fifa-club-modal/close.svg'"
                        alt="icon close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="text-white">
                <div
                    class="aff-body text-left text-[14px] font-normal leading-5"
                >
                    <div class="aff-body__scroll">
                        <div>
                            <img
                                :src="`${staticUrl}/fifa-club-modal/banner.png`"
                                alt="Fifa Club"
                                class="hidden lg:block"
                            />
                            <img
                                :src="`${staticUrl}/fifa-club-modal/banner-mb.png`"
                                alt="Fifa Club"
                                class="lg:hidden"
                            />
                        </div>
                        <div class="aff-body__btn">
                            <span @click="openPopupRules">
                                {{ $t('fifa_club_rules.btn_join') }}
                            </span>
                        </div>
                        <div
                            class="aff-body__checked text-sm font-normal leading-6"
                        >
                            <span
                                @click="noShowAgainBanner"
                                :class="{
                                    active: noShowAgainFifaClubBannerModal,
                                }"
                                >{{ $t('fifa_club_rules.no_show') }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const {
    showFifaClubBannerModal,
    noShowAgainFifaClubBannerModal,
    showFifaClubModal,
} = storeToRefs(useModalStoreInstance)
const showAgainHours = Number(
    useRuntimeConfig().public.FIFA_CLUB_MODAL_SHOW_AGAIN_HOURS
)
const showAgainMilliseconds = showAgainHours * 60 * 60 * 1000

const closeSelf = () => {
    showFifaClubBannerModal.value = false
    if (!noShowAgainFifaClubBannerModal.value) {
        noShowAgainFifaClubBannerModal.value = true
        useModalStoreInstance.setShowAgainFifaClubBannerModal(
            noShowAgainFifaClubBannerModal.value
        )

        if (noShowAgainFifaClubBannerModal.value) {
            setTimeout(() => {
                useModalStoreInstance.setShowAgainFifaClubBannerModal(false)
                showFifaClubBannerModal.value = true
            }, showAgainMilliseconds)
        }
    }
}
const noShowAgainBanner = () => {
    noShowAgainFifaClubBannerModal.value = !noShowAgainFifaClubBannerModal.value
    useModalStoreInstance.setNoShowAgainFifaClubBannerModal(
        noShowAgainFifaClubBannerModal.value
    )
}
const openPopupRules = () => {
    showFifaClubModal.value = true
    useModalStoreInstance.setShowCheckedFifaClubModal(true)
    closeSelf()
}
onMounted(() => {
    useModalStoreInstance.initShowAgainFifaClubBannerModal()

    if (
        noShowAgainFifaClubBannerModal.value &&
        useModalStoreInstance.fifaClubBannerModalCheckTime
    ) {
        const checkTime = dayjs(
            useModalStoreInstance.fifaClubBannerModalCheckTime
        ).tz('Asia/Ho_Chi_Minh')
        const now = dayjs().tz('Asia/Ho_Chi_Minh')
        const timePassed = now.diff(checkTime)

        if (timePassed >= showAgainMilliseconds) {
            useModalStoreInstance.setShowAgainFifaClubBannerModal(false)
            showFifaClubBannerModal.value = true
        } else {
            const remainingTime = showAgainMilliseconds - timePassed
            setTimeout(() => {
                useModalStoreInstance.setShowAgainFifaClubBannerModal(false)
                showFifaClubBannerModal.value = true
            }, remainingTime)
        }
    }
})
</script>
<style lang="scss" scoped>
:deep(.modal-content) {
    @apply w-full overflow-visible rounded-[16px] lg:min-w-[858px] lg:max-w-[858px];
    background-color: transparent;
}
.aff-body {
    &__title {
        @apply mx-auto -mt-[35px] mb-[26px] flex h-[73px] w-[309px] items-center
        justify-center whitespace-pre-wrap text-center text-[18px] font-extrabold leading-[25px];
        background: url('/assets/images/fifa-club-modal/title.png') center
            no-repeat;
        background-size: 100%;
        span {
            background: linear-gradient(
                180deg,
                #ffffff -14.29%,
                #ddeaff 82.14%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        @media (max-width: 992px) {
            @apply -mt-[36px] mb-[17px];
        }
    }
    &__ul {
        @apply pl-2.5;
        li {
            @apply relative pl-[12px] text-sm font-normal leading-5;
            &:before {
                @apply absolute left-0 top-[8px] size-[3px] rounded-full bg-white content-[''];
            }
            &:not(:last-child) {
                @apply mb-2;
            }
        }
    }
    &__btn {
        span {
            background: url('/assets/images/fifa-club-modal/btn.avif') center
                no-repeat;
            background-size: 100%;
            @apply mx-auto my-8 flex h-[48px] w-[198px] cursor-pointer items-center justify-center text-base font-bold leading-6 text-[#17181A] transition-[0.3s] lg:my-6;
            &:hover {
                @apply opacity-80;
            }
        }
    }
    &__checked {
        @apply flex justify-center;
        @media screen and (max-width: 992px) and (orientation: landscape) {
            @apply mb-[50px];
        }
        span {
            @apply relative cursor-pointer pl-[30px] text-[#D5D7D9];
            &:before,
            &:after {
                @apply absolute left-0 top-0 size-[24px] content-[''];
            }
            &:before {
                @apply rounded border-2 border-solid border-[#AAAAAA];
            }
            &:after {
                background: url('/assets/images/fifa-club-modal/checked.svg')
                    center no-repeat;
                background-size: 25px;
                @apply opacity-0;
            }
            &.active {
                &:after {
                    @apply opacity-100;
                }
            }
        }
    }
}
</style>
