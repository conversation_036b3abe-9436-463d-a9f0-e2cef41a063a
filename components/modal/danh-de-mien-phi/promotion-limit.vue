<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'

const staticUrl = useRuntimeConfig().public.staticUrl
const { showPromotionLimitModal } = storeToRefs(useModalStore())
const { isDeskTop } = useWindowSize()

const closeSelf = () => {
    showPromotionLimitModal.value = false
}
</script>
<template>
    <CommonModal
        @close="closeSelf"
        class="z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-gradient-to-b from-[#FFEAD6] to-[#FFDFBE] max-w-[343px] lg:!max-w-[428px] modal-event"
    >
        <template #default>
            <button
                class="close absolute right-2 top-2 z-10 size-8"
                @click="closeSelf"
            >
                <NuxtIcon
                    class="flex items-center justify-center text-3xl text-z-old-silver"
                    name="close"
                ></NuxtIcon>
            </button>
            <div class="px-4 py-8 lg:px-[51px]">
                <div class="mx-auto size-[140px] px-3 py-6">
                    <CommonImage
                        :src="`${staticUrl}/danh-de-mien-phi/promotion-limit.png`"
                        alt="promotion-limit"
                        class="aspect-[114.58/91.35] h-full w-full object-contain"
                    />
                </div>

                <p
                    class="mb-2 mt-6 font-montserrat text-base font-bold uppercase text-z-chroma-black"
                >
                    Game không áp dụng khuyến mãi!
                </p>
                <div
                    class="font-montserrat text-sm font-normal text-z-chroma-black"
                >
                    Bạn không thể chơi game này do đang sử dụng khuyến mãi.
                </div>
                <div class="mt-8 flex items-center justify-between gap-4">
                    <nuxt-link
                        to="/"
                        @click="closeSelf"
                        class="flex h-11 w-full items-center justify-center rounded-lg border border-z-red-dit text-sm font-semibold uppercase text-z-red-dit"
                    >
                        TRANG CHỦ
                    </nuxt-link>
                    <nuxt-link
                        :to="
                            isDeskTop
                                ? '/user?type=overview'
                                : '/user/promotion'
                        "
                        class="hover-btn flex h-11 w-full items-center justify-center rounded-lg bg-z-red-dit text-sm font-semibold uppercase text-white"
                        @click="closeSelf"
                    >
                        XEM KHUYẾN MÃI
                    </nuxt-link>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<style lang="scss" scoped></style>
