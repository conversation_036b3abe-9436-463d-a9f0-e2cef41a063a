<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { NumberUtils } from '~/utils'
import { PROMOTION_POPUP_METHOD } from '~/constants/common'

const staticUrl = useRuntimeConfig().public.staticUrl
const usePromotionInstance = usePromotion()
const { closePopup } = usePromotionInstance
const { luckyDrawAward, luckyDrawAward2 } = storeToRefs(usePromotionInstance)
const useModalStoreInstance = useModalStore()
const { handleModalClose, queueModal } = useModalStoreInstance

const closeSelf = async () => {
    await closePopup(luckyDrawAward?.value?.id)
    handleModalClose()

    if (
        luckyDrawAward2.value &&
        luckyDrawAward2.value?.is_show &&
        luckyDrawAward2.value?.id !== luckyDrawAward.value?.id
    ) {
        setTimeout(() => {
            if (luckyDrawAward2.value && luckyDrawAward2.value?.is_show) {
                luckyDrawAward.value = luckyDrawAward2.value
                queueModal(PROMOTION_POPUP_METHOD.LUCKY_DRAW_2)
            }
        }, 1000)
    }
}
</script>
<template>
    <CommonModal
        @close="closeSelf"
        class="z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-gradient-to-b from-[#FFEAD6] to-[#FFDFBE] max-w-[343px] lg:!max-w-[428px] modal-event"
    >
        <template #default>
            <button
                class="close absolute right-2 top-2 z-10 size-8"
                @click="closeSelf"
            >
                <NuxtIcon
                    class="flex items-center justify-center text-3xl text-z-old-silver"
                    name="close"
                ></NuxtIcon>
            </button>
            <div class="px-4 py-8 lg:px-[51px]">
                <img
                    :src="`${staticUrl}/danh-de-mien-phi/lucky-draw-award.svg`"
                    alt="deposit"
                    class="mx-auto size-[130px]"
                />
                <p
                    class="mb-2 mt-6 text-base font-bold uppercase text-z-chroma-black"
                >
                    Chúc Mừng Nhận Thưởng!
                </p>
                <div class="mb-8 text-sm text-z-chroma-black">
                    Bạn đã trúng
                    <strong
                        >{{
                            luckyDrawAward?.amount_txt ||
                            NumberUtils.formatNumberWithComma(
                                luckyDrawAward?.amount
                            )
                        }}
                        VND</strong
                    >
                    từ loại Đề
                    {{
                        Number(
                            luckyDrawAward?.lucky_draw ||
                                luckyDrawAward?.type ||
                                1
                        ) + 1
                    }}
                    số trong chương trình Đánh Đề Miễn Phí.
                </div>
                <button
                    class="hover-btn h-11 w-full rounded bg-z-red-dit p-2.5 text-sm font-semibold uppercase text-white"
                    @click="closeSelf"
                >
                    Xác nhận
                </button>
            </div>
        </template>
    </CommonModal>
</template>
<style lang="scss" scoped></style>
