<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useWindowSize } from '~/composables/use-window'
import { BetType, type LuckyDrawData } from '~/interfaces/event'
import { PAGE_URL } from '~/constants/page-urls'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
})

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const {
    showLuckyDrawCashbackModal,
    showBetFreeModal,
    showPromotionLimitModal,
    betType,
    showBetFreeModalMB,
} = storeToRefs(useModalStoreInstance)
const { isDeskTop } = useWindowSize()
const route = useRoute()
const useEventDanhDeStoreInstance = useEventDanhDeStore()
const { isAccountPromotion } = storeToRefs(useEventDanhDeStoreInstance)

const isLUCKYDRAW = computed(() => route.path === PAGE_URL.LUCKY_DRAW)

const handleBetLuckyDraw = async () => {
    closeSelf()
    if (isAccountPromotion.value) {
        showPromotionLimitModal.value = true
        return
    }
    if (isDeskTop.value) {
        if (
            typeof props.luckyDraw?.lucky_number_1 === 'string' &&
            typeof props.luckyDraw?.lucky_number_2 !== 'string'
        ) {
            betType.value = BetType.LUCKY_NUMBER_2
        }
        showBetFreeModal.value = true
        if (!isLUCKYDRAW.value) {
            await navigateTo(PAGE_URL.LUCKY_DRAW)
        }
        return
    } else {
        await navigateTo(PAGE_URL.LUCKY_DRAW)
        showBetFreeModalMB.value = true
        const luckyNumberMobileElement = document.querySelector(
            '.lucky-number-mobile-ct'
        )
        if (luckyNumberMobileElement) {
            const elementPosition =
                luckyNumberMobileElement.getBoundingClientRect().top
            const offsetPosition = elementPosition + window.pageYOffset - 80

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth',
            })
        }
    }
}
const closeSelf = () => {
    showLuckyDrawCashbackModal.value = false
}
</script>
<template>
    <CommonModal
        @close="closeSelf"
        class="z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-gradient-to-b from-[#FFEAD6] to-[#FFDFBE] max-w-[343px] lg:!max-w-[428px] modal-event"
    >
        <template #default>
            <button
                class="close absolute right-2 top-2 z-10 size-8"
                @click="closeSelf"
            >
                <NuxtIcon
                    class="flex items-center justify-center text-3xl text-z-old-silver"
                    name="close"
                ></NuxtIcon>
            </button>
            <div class="px-4 py-8 lg:px-[51px]">
                <img
                    :src="`${staticUrl}/danh-de-mien-phi/lucky-number/success.png`"
                    alt="deposit"
                    class="mx-auto size-[140px]"
                />
                <p
                    class="mb-2 mt-6 text-base font-bold uppercase text-z-chroma-black"
                >
                    Chúc mừng!
                </p>
                <div class="mb-8 text-sm text-z-chroma-black">
                    Bạn đã nhận được lượt đánh đề miễn phí.
                </div>
                <button
                    class="hover-btn h-11 w-full rounded bg-z-red-dit p-2.5 text-sm font-semibold uppercase text-white"
                    @click="handleBetLuckyDraw"
                >
                    CHỌN SỐ NGAY
                </button>
            </div>
        </template>
    </CommonModal>
</template>
<style lang="scss" scoped></style>
