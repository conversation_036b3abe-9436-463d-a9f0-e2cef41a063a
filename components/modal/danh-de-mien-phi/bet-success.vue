<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useServerTime } from '~/composables/common/time-server'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(isBetween)
dayjs.extend(utc)
dayjs.extend(timezone)

const { staticUrl } = useRuntimeConfig().public
const useModalStoreInstance = useModalStore()
const { showBetSuccessModal, betType, luckyDrawDateBetApi } = storeToRefs(useModalStoreInstance)
const useServerTimeInstance = useServerTime()
const { getServerTime } = useServerTimeInstance

const serverTimestamp = ref()
const checkNotification = ref(false)
const luckyDrawDateBet = ref('')

const getTimeServer = async () => {
    const time = await getServerTime()
    if (time) {
        serverTimestamp.value = time
        const now = dayjs.unix(time).tz('Asia/Ho_Chi_Minh')
        const startTime = now.hour(18).minute(0).second(0).millisecond(0)
        const endTime = now.hour(23).minute(59).second(59).millisecond(999)
        
        if (now.isBetween(startTime, endTime)) {
            checkNotification.value = true
            luckyDrawDateBet.value = now.add(1, 'day').format('DD/MM/YY')
        } else {
            checkNotification.value = false
        }
    }
}

watch(showBetSuccessModal, (newValue) => {
    if (newValue) {
        getTimeServer()
    }
})

const closeSelf = () => {
    showBetSuccessModal.value = false
}
</script>
<template>
    <CommonModal
        @close="closeSelf"
        class="font-primary z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-gradient-to-b from-[#FFEAD6] to-[#FFDFBE] max-w-[343px] lg:!max-w-[428px] modal-event"
    >
        <button
            class="close absolute top-2 z-10 right-2"
            @click="closeSelf"
        >
            <NuxtIcon class="text-3xl text-z-old-silver flex items-center justify-center" name="close"></NuxtIcon>
        </button>
        <div class="py-8 px-4 lg:px-[51px]">
            <img
                :src="`${staticUrl}/danh-de-mien-phi/lucky-number/success.png`"
                alt="deposit"
                class="size-[140px] mx-auto"
            />
            <p class="text-z-chroma-black text-base font-bold mt-6 mb-2 uppercase">
                {{ checkNotification ? 'Thông báo dự thưởng' : 'Đánh đề thành công!' }}
            </p>
            <div class="text-sm text-z-chroma-black mb-8">
                <template v-if="checkNotification">
                    <span v-html="`Các số dự thưởng đã được áp dụng cho lượt dự đoán của ngày <strong>${luckyDrawDateBetApi || luckyDrawDateBet}</strong>`"></span>
                </template>
                <template v-else>
                    Đối chiếu kết quả với {{ betType }} số cuối giải Đặc biệt XSMB để kiểm tra.
                </template>
            </div>
            <button class="h-11 w-full rounded bg-z-red-dit p-2.5 font-semibold uppercase text-white hover-btn text-sm" @click="closeSelf">Đã hiểu</button>
        </div>
    </CommonModal>
</template>
<style lang="scss" scoped>

</style>
