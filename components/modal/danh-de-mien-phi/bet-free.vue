<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useForm } from 'vee-validate'
import { useModalStore } from '~/stores'
import { useEventDanhDeStore } from '~/composables/use-event-danh-de'
import { betFreeForm } from '~/forms/betting.schema'
import type { BetFormValues } from '~/interfaces/event'
import { BetType } from '~/interfaces/event'
const { staticUrl } = useRuntimeConfig().public
const { t } = useI18n()
const useModalStoreInstance = useModalStore()
const { showBetFreeModal, betType } = storeToRefs(useModalStoreInstance)
const useEventDanhDeStoreInstance = useEventDanhDeStore()
const { betLuckyDraw } = useEventDanhDeStoreInstance

const hasErrors = ref(false)

const initialValues: BetFormValues = {
    numberOne: '',
    numberTwo: '',
    numberThree: '',
    numberFour: '',
    numberFive: '',
}

const validationSchema = computed(() => betFreeForm(t, betType.value))
const { handleSubmit, errors, defineField, resetForm } = useForm({
    initialValues,
    validationSchema,
})

const [numberOne] = defineField('numberOne')
const [numberTwo] = defineField('numberTwo')
const [numberThree] = defineField('numberThree')
const [numberFour] = defineField('numberFour')
const [numberFive] = defineField('numberFive')

const onPasteBetNumber = (text: string, field: keyof BetFormValues): void => {
    if (!text.match(/[\d]/g)) {
        return
    }
    if (field === 'numberOne') numberOne.value = text.trim()
    if (field === 'numberTwo') numberTwo.value = text.trim()
    if (field === 'numberThree') numberThree.value = text.trim()
    if (field === 'numberFour') numberFour.value = text.trim()
    if (field === 'numberFive') numberFive.value = text.trim()
}

const handleBet = handleSubmit(async (values: BetFormValues) => {
    try {
        if (betType.value === BetType.LUCKY_NUMBER_1) {
            const numbers = `${values.numberOne},${values.numberTwo}`
            await betLuckyDraw(numbers, '')
        } else {
            const numbers = `${values.numberThree},${values.numberFour},${values.numberFive}`
            await betLuckyDraw('', numbers)
        }
        closeSelf()
    } catch (error) {
        console.error('Error betting:', error)
    }
})

const closeSelf = () => {
    resetForm()
    showBetFreeModal.value = false
}

watch(showBetFreeModal, (value) => {
    if (value) {
        resetForm()
    }
})

watch(errors, (newErrors) => {
    hasErrors.value = Object.keys(newErrors).length > 0
})

const isFormValid = computed(() => {
    if (hasErrors.value) return false

    if (betType.value === BetType.LUCKY_NUMBER_1) {
        return numberOne.value?.length === 2 && numberTwo.value?.length === 2
    }
    return numberThree.value?.length === 3 && numberFour.value?.length === 3 && numberFive.value?.length === 3
})
</script>

<template>
    <CommonModal
        @close="closeSelf"
        class="font-primary z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-transparent !max-w-[514px] modal-event pt-[8px]"
    >
        <button
            class="close absolute top-0 z-10 right-0"
            @click="closeSelf"
        >
            <img
                :src="`${staticUrl}/danh-de-mien-phi/lucky-number/close.svg`"
                alt="close"
                class="close size-[32px]"
            />
        </button>
        <form @submit.prevent="handleBet" class="lucky-number-mb__item">
            <div class="lucky-number-mb__title text-[24px] leading-[24px] tracking-[0%] font-svn-vt pt-[16px]">CHỌN SỐ MAY MẮN</div>
            <div v-if="betType === BetType.LUCKY_NUMBER_1" class="relative">
                <label class="font-medium text-[14px] leading-[20px] tracking-[0%] text-z-chroma-black mt-[26px] mb-[4px] block">Đánh đề 2 số (00 - 99) - Được chọn 2 số</label>
                <div class="flex items-center gap-[8px] pr-[33px]">
                    <CommonTextInputNumber
                        v-model="numberOne"
                        placeholder="Nhập số"
                        inputClass="h-[44px] [$_input]:!bg-white [&_input]:text-sm [&_input]:!p-3 [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-z-dark-gray"
                        class="w-full !bg-white !rounded-lg !border-[#FBEBD3]"
                        inputmode="numeric"
                        type="tel"
                        :maxLength="2"
                        @onPaste="(text: string) => onPasteBetNumber(text, 'numberOne')"
                    />
                    <CommonTextInputNumber
                        v-model="numberTwo"
                        placeholder="Nhập số"
                         inputClass="h-[44px] [$_input]:!bg-white [&_input]:text-sm [&_input]:!p-3 [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-z-dark-gray"
                        class="w-full !bg-white !rounded-lg !border-[#FBEBD3]"
                        inputmode="numeric"
                        type="tel"
                        :maxLength="2"
                        @onPaste="(text: string) => onPasteBetNumber(text, 'numberTwo')"
                    />
                </div>
                <div v-if="errors.numberOne || errors.numberTwo" class="error-message text-[14px] leading-[20px] text-[#FF0000] mt-[4px]">
                    {{ errors.numberOne || errors.numberTwo }}
                </div>
                <div class="absolute top-[33px] right-0 icon-input">
                    <img :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`" alt="tooltip" class="size-[25px]">
                </div>
                <div class="box-tooltip hidden absolute bottom-[40px] right-0 bg-[#1C1C1C] rounded-[4px] p-1 py-1.5 text-white font-semibold text-[12px] leading-[120%] w-[208px]">
                    <ul>
                        <li>Chọn 2 số từ 00 đến 99.</li>
                        <li>So sánh với 2 số cuối giải ĐB miền Bắc.</li>
                        <li>Không thể chọn lại nếu đã bấm "Xác nhận".</li>
                    </ul>
                </div>
            </div>
            <div v-if="betType === BetType.LUCKY_NUMBER_2" class="relative">
                <label class="font-medium text-[14px] leading-[20px] tracking-[0%] text-z-chroma-black mt-[26px] mb-[4px] block">Đánh đề 3 số (000 - 999) - Được chọn 3 số</label>
                <div class="flex items-center gap-[8px] pr-[33px]">
                    <CommonTextInputNumber
                        v-model="numberThree"
                        placeholder="Nhập số"
                        inputClass="h-[44px] [$_input]:!bg-white [&_input]:text-sm [&_input]:!p-3 [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-z-dark-gray"
                        class="w-full !bg-white !rounded-lg !border-[#FBEBD3]"
                        inputmode="numeric"
                        type="tel"
                        :maxLength="3"
                        @onPaste="(text: string) => onPasteBetNumber(text, 'numberThree')"
                    />
                    <CommonTextInputNumber
                        v-model="numberFour"
                        placeholder="Nhập số"
                        inputClass="h-[44px] [$_input]:!bg-white [&_input]:text-sm [&_input]:!p-3 [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-z-dark-gray"
                        class="w-full !bg-white !rounded-lg !border-[#FBEBD3]"
                        inputmode="numeric"
                        type="tel"
                        :maxLength="3"
                        @onPaste="(text: string) => onPasteBetNumber(text, 'numberFour')"
                    />
                    <CommonTextInputNumber
                        v-model="numberFive"
                        placeholder="Nhập số"
                        inputClass="h-[44px] [$_input]:!bg-white [&_input]:text-sm [&_input]:!p-3 [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-z-dark-gray"
                        class="w-full !bg-white !rounded-lg !border-[#FBEBD3]"
                        inputmode="numeric"
                        type="tel"
                        :maxLength="3"
                        @onPaste="(text: string) => onPasteBetNumber(text, 'numberFive')"
                    />
                </div>
                <div v-if="errors.numberThree || errors.numberFour || errors.numberFive" class="error-message text-[14px] leading-[20px] text-[#FF0000] mt-1">
                    {{ errors.numberThree || errors.numberFour || errors.numberFive }}
                </div>
                <div class="absolute top-[33px] right-0 icon-input">
                    <img :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`" alt="tooltip" class="size-[25px]">
                </div>
                <div class="box-tooltip hidden absolute bottom-[40px] right-0 bg-[#1C1C1C] rounded-[4px] p-1 py-1.5 text-white font-semibold text-[12px] leading-[120%] w-[208px]">
                    <ul>
                        <li>Chọn 3 số từ 000 đến 999.</li>
                        <li>So sánh với 3 số cuối giải ĐB miền Bắc.</li>
                        <li>Không thể chọn lại nếu đã bấm "Xác nhận".</li>
                    </ul>
                </div>
            </div>
            <button
                type="submit"
                class="lucky-number-mb__btn"
                :class="!isFormValid ? 'grayscale-[1] pointer-events-none' : 'cursor-pointer'"
            >
                <span>XÁC NHẬN</span>
            </button>
        </form>
    </CommonModal>
</template>

<style lang="scss" scoped>
.lucky-number-mb {
    &__item {
        @apply relative px-[24px] w-[496px] h-[218px] text-left;
        background: url('/assets/images/danh-de-mien-phi/lucky-number/bg-popup-bet.png') no-repeat
        center / 100% 100%;
        .disabled-input {
            .icon-input {
                @apply grayscale-[1] pointer-events-none;
            }
            :deep(input) {
                @apply bg-[#F5F6F7] opacity-100 #{!important};
            }
        }
        :deep(input) {
            &:focus {
                @apply shadow-[0px_0px_0px_2px_#F46E49];
            }
        }
        .icon-input {
            &:hover {
                & + .box-tooltip {
                    @apply block;
                }
            }
        }
    }
    &__title {
        background: linear-gradient(180deg, #CF3704 33.65%, #AD2106 70.08%, #CF1F04 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    &__btn {
        @apply font-mulish absolute bottom-[9px] left-0 right-0 flex items-center justify-center font-black text-[15px] w-[178px] h-[36px] mx-auto;
        background: url('/assets/images/danh-de-mien-phi/banner/btn-mb.png') no-repeat
        center / 100% 100%;
        span {
            background: linear-gradient(180deg, #EA462E 0%, #C7310C 100%),
            linear-gradient(180deg, #CF2604 33.65%, #A50E03 70.08%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
.box-tooltip {
    ul {
        @apply pl-[20px];
        li {
            @apply list-disc text-xs font-montserrat font-normal;
        }
    }
    &::before {
        @apply content-[''] absolute w-0 h-0 border-t-4 border-t-[#1C1C1C] border-b-0 border-x-[5px] border-x-transparent border-solid right-[17px] -bottom-1;
    }
}
</style>
