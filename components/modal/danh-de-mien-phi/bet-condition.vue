<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
const router = useRouter()
const { staticUrl } = useRuntimeConfig().public
const useModalStoreInstance = useModalStore()
const { showBetConditionModal, betType } = storeToRefs(useModalStoreInstance)
const closeSelf = () => {
    showBetConditionModal.value = false
}
const handleDeposit = () => {
    router.push('/user/deposit?type=codepay')
    closeSelf()
}
</script>
<template>
    <CommonModal
        @close="closeSelf"
        class="font-primary z-[103]"
        classCustomWrapper="!p-0"
        classCustom="!bg-z-chroma-black w-full max-w-[343px] xs:max-w-[375px] lg:!max-w-[504px] modal-event"
    >
        <button
            class="close absolute top-2 z-10 right-2"
            @click="closeSelf"
        >
            <NuxtIcon class="text-3xl text-z-old-silver flex items-center justify-center" name="close"></NuxtIcon>
        </button>
        <div class="p-8 pt-10 lg:p-8 pb-[23px]">
            <img
                :src="`${staticUrl}/danh-de-mien-phi/lucky-number/failure.svg`"
                alt="deposit"
                class="size-16 mx-auto"
            />
            <p class="font-montserrat text-white text-xl leading-8 font-bold mb-2 lg:mb-6 mt-4 uppercase">
                Chưa đủ điều kiện!
            </p>
            <div class="font-montserrat font-normal text-sm leading-5 text-white mb-8 lg:mb-4">Chỉ cần nạp tối thiểu <strong>{{ betType * 100 }} D</strong> để nhận ngay lượt chơi miễn phí.</div>
            <button class="h-11 w-full rounded bg-z-red-dit p-2.5 font-semibold uppercase text-white hover-btn text-sm" @click="handleDeposit">NẠP NHANH</button>
        </div>
    </CommonModal>
</template>
<style lang="scss" scoped>

</style>
