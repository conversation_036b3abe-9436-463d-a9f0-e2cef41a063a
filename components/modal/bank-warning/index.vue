<template>
  <CommonModal
      :show="showBankWarningModal"
      @close="closeSelf"
      :sticky="true"
      size="md"
      class="font-primary"
      classCustomWrapper="items-stretch sm:items-center"
      classCustom="bg-z-chroma-black p-4 rounded-[10px] max-w-[434px] h-min m-auto"
  >
      <button class="close absolute right-3 top-3" @click="closeSelf">
          <NuxtIcon
              class="mb-0 text-[2rem] text-dhu-gray opacity-70"
              name="close"
          ></NuxtIcon>
      </button>
      <div class="content-support px-4 pt-4 lfex items-center">
          <p class="text-2xl font-bold text-z-red-dit">
            {{ $t('user.modal.bank.deposit_warning.title') }}
          </p>
          <CommonImage
            class="mx-auto my-6 w-20 h-20"
            :src="`/assets/images/icons/bank.png`"
          />
          <p class="text-white">
            {{ $t('user.modal.bank.deposit_warning.description1') }}
            <span class="text-z-gold"> {{  $t('user.modal.bank.deposit_warning.description2') }} </span>
            {{ $t('user.modal.bank.deposit_warning.description3') }}
          </p>
      </div>
      <button
        type="button"
        class="w-full my-8 rounded-md bg-z-red-dit p-3 font-bold uppercase leading-snug text-white"
        @click="closeSelf"
    >
        {{ $t('user.modal.bank.deposit_warning.confirm') }}
      </button>
      <div class="flex items-center gap-2 justify-center mb-4">
        <div 
          @click=onToggleDoNotShowAgain
          class="h-4 flex items-center justify-center relative w-4 border rounded-sm border-z-red-dit cursor-pointer"
          :class="{
            'bg-z-red-dit': showAgain
          }"
        >
        <CommonImage
          v-if="showAgain"
          class="mx-auto"
          src="/assets/images/games/menu/checked.svg"
        />
      </div>
        {{ $t('common.donot_show_again') }}
      </div>
  </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
const useModalStoreInstance = useModalStore()
const { showBankWarningModal, showRegisterModal } = storeToRefs(
  useModalStoreInstance
)
const showAgain = ref(false);

const key = 'deposit_warning';

const handleSetDonotShowAgainToStorage = () => {
  if(showAgain.value){
    localStorage.setItem(key,showAgain.value)
  }
}

const closeSelf = () => {
  handleSetDonotShowAgainToStorage()
  showBankWarningModal.value = false
}

const onToggleDoNotShowAgain = () => {
  showAgain.value = !showAgain.value
}

const handleCheckIfUserHasSetDoNotShowAgain = () => {
  const showDepositWarning = localStorage.getItem(key)
  showBankWarningModal.value = !showDepositWarning;
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      handleCheckIfUserHasSetDoNotShowAgain();    
    }, 2000);  
  })
})
</script>
