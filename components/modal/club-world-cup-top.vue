<template>
    <CommonModal
        @close="closeSelf"
        size="md"
        classCustomWrapper="race-modal"
        classCustom="race-modal-inner"
    >
        <div class="refund-modal-content">
            <div
                class="close absolute right-[8px] top-[144px] cursor-pointer"
                @click="closeSelf"
            >
                <NuxtIcon
                    name="close"
                    class="text-2xl text-[rgba(255,_255,_255,_0.6)]"
                />
            </div>
            <div class="icons absolute left-2/4 top-[66px] -translate-x-2/4">
                <div class="relative">
                    <img
                        :src="`${staticUrl}/top-event/popup/${
                            convertIcon(+clubWorldCupData?.ranking || 0).icon
                        }`"
                        alt="icon"
                        class="h-[216px] w-[217px] min-w-[217px]"
                    />
                    <span
                        :class="[
                            'ranking absolute left-2/4  top-2/4 -translate-x-2/4 -translate-y-2/4 font-montserrat font-black',
                            +clubWorldCupData?.ranking <= 3 &&
                            +clubWorldCupData?.ranking > 0
                                ? 'text-[4.875rem]  leading-[calc(117/78)]'
                                : 'text-5xl',
                        ]"
                        >{{
                            convertIcon(+clubWorldCupData?.ranking || 0).rank
                        }}</span
                    >
                </div>
            </div>
            <div
                class="modal-box absolute bottom-[36px] left-2/4 w-full -translate-x-2/4 px-[2rem]"
            >
                <div
                    class="title-event mb-4 text-[28px] font-black not-italic leading-[calc(34/28)] lg:text-[32px] lg:leading-[calc(39/32)]"
                >
                    {{ $t('top_event.popup.title') }}
                </div>
                <div
                    class="content-text flex h-[114px] flex-col items-center justify-center rounded-[12px] px-6 py-3 [background:linear-gradient(200.53deg,_#FFFFFF_9.03%,_#FFF3D6_104.27%)] lg:h-[142px] lg:p-6"
                >
                    <p
                        class="text-sm font-extrabold text-z-koku lg:text-[16px] lg:leading-[calc(24/16)]"
                    >
                        {{ $t('top_event.popup.notify.text_1') }}
                        {{ clubWorldCupData?.ranking || 0 }}
                    </p>
                    <p
                        class="mb-3 text-xs font-bold text-z-koku lg:text-[16px] lg:leading-[calc(24/16)]"
                    >
                        {{ $t('top_event.popup.notify.text_2') }}
                    </p>
                    <p class="amount text-2xl font-extrabold lg:text-[1.75rem]">
                        {{
                            `${NumberUtils.formatMoney(
                                clubWorldCupData?.amount,
                                'VND'
                            )}`
                        }}
                    </p>
                </div>
            </div>
        </div>
    </CommonModal>
</template>
<script setup lang="ts">
const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { handleModalClose } = useModalStoreInstance
const usePromotionInstance = usePromotion()
const { closeWorldCupModal } = usePromotionInstance
const { clubWorldCupData } = storeToRefs(usePromotionInstance)
const closeSelf = async () => {
    await closeWorldCupModal()
    handleModalClose()
}
const convertIcon = (ranking: number) => {
    switch (true) {
        case ranking === 1:
            return {
                icon: 'top_1.png',
                rank: '1',
            }
        case ranking === 2:
            return {
                icon: 'top_2.png',
                rank: '2',
            }
        case ranking === 3:
            return {
                icon: 'top_3.png',
                rank: '3',
            }
        case ranking > 3 && ranking < 100:
            return {
                icon: 'top_normal.png',
                rank: ranking,
            }
        case ranking >= 100:
            return {
                icon: 'top_normal.png',
                rank: '99+',
            }
        default:
            return {
                icon: 'top_normal.png',
                rank: '',
            }
    }
}
</script>

<style lang="scss" scoped>
:deep(.race-modal-inner) {
    @apply relative h-[479px] w-[343px] bg-transparent bg-[url('/assets/images/top-event/popup/bg-mb.png')] bg-contain bg-center bg-no-repeat sm:max-w-full lg:h-[512px] lg:w-[460px] lg:bg-[url('/assets/images/top-event/popup/bg-pc.png')];
}
.ranking {
    text-shadow: 1.976px 1.976px 0px rgb(255 129 0 / 9%),
        3.953px 1.976px 9.882px rgba(0, 0, 0, 0.04);
    background: linear-gradient(183.37deg, #ffffff 30.93%, #ced7e9 92.45%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.title-event {
    text-shadow: 0px 2px 8px 0 rgba(0, 0, 0, 0.64);
    background: linear-gradient(360deg, #fff1d9 0%, #ffc267 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.amount {
    background: linear-gradient(6.07deg, #ff4701 45.64%, #fa895e 101.89%);
    text-shadow: 0.78px 0.78px 0px 0px #e54000d1 inset;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
</style>
