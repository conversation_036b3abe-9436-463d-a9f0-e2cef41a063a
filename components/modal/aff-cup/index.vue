<template>
    <CommonModal
        :show="showAffCupModal"
        @close="closeSelf"
        size="xxl"
        class="aff-modal z-[103]"
    >
        <template #header>
            <div class="relative px-6 pt-8 text-white">
                <div
                    class="mt-[50px] text-center text-[30px] font-bold uppercase leading-[34px] xl:mt-[25px] xl:text-[18px] xl:leading-7"
                ></div>

                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute right-2 top-2"
                    @click="closeSelf"
                >
                    <img
                        :src="'/assets/images/aff-modal/close.svg'"
                        alt="icon close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="px-4 text-white lg:px-6">
                <div
                    class="aff-body pb-6 text-left text-[14px] font-normal leading-5"
                >
                    <div class="aff-body__title">
                        <span>{{ $t('aff_rules.title') }}</span>
                    </div>
                    <div class="aff-body__scroll scrollbar">
                        <div
                            class="mb-1 text-sm font-semibold uppercase leading-5"
                        >
                            {{ $t('aff_rules.rule_title') }}
                        </div>
                        <div
                            class="mb-4 rounded-lg bg-[#292929] py-4 pl-2.5 pr-4"
                        >
                            <ul class="aff-body__ul">
                                <li>{{ $t('aff_rules.rule_des_1') }}</li>
                                <li>{{ $t('aff_rules.rule_des_2') }}</li>
                                <li>{{ $t('aff_rules.rule_des_3') }}</li>
                                <li>{{ $t('aff_rules.rule_des_4') }}</li>
                            </ul>
                        </div>
                        <div
                            class="mb-1 text-sm font-semibold uppercase leading-5"
                        >
                            {{ $t('aff_rules.terms_title') }}
                        </div>
                        <div class="rounded-lg bg-[#292929] py-4 pl-2.5 pr-4">
                            <ul class="aff-body__ul">
                                <li>
                                    {{ $t('aff_rules.terms_des_1') }}
                                    <span class="font-bold">
                                        {{ brandName }}
                                    </span>
                                </li>
                                <li>{{ $t('aff_rules.terms_des_2') }}</li>
                                <li>{{ $t('aff_rules.terms_des_3') }}</li>
                                <li>
                                    <span class="font-bold">
                                        {{ brandName }}
                                    </span>
                                    {{ $t('aff_rules.terms_des_4') }}
                                </li>
                                <li>
                                    {{ $t('aff_rules.terms_des_5') }}
                                    <span class="font-bold">
                                        {{ brandName }}
                                    </span>
                                    {{ $t('aff_rules.terms_des_6') }}
                                </li>
                                <li>{{ $t('aff_rules.terms_des_7') }}</li>
                            </ul>
                        </div>
                        <div class="aff-body__btn">
                            <span @click="handleToKSport">
                                {{ $t('aff_rules.btn') }}
                            </span>
                        </div>
                        <div
                            class="aff-body__checked text-sm font-normal leading-5"
                        >
                            <span
                                @click="noShowAgain"
                                :class="{ active: noShowAgainAffCupModal }"
                                >{{ $t('aff_rules.no_show') }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useWindowSize } from '~/composables/use-window'
import { SPORT_TYPE } from '~/constants/sport'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)

const router = useRouter()
const useModalStoreInstance = useModalStore()
const { showAffCupModal, noShowAgainAffCupModal } = storeToRefs(
    useModalStoreInstance
)
const brandName = useRuntimeConfig().public.BRAND_NAME
const { isDeskTop } = useWindowSize()
const { openSport } = usePlayGame()

const closeSelf = () => {
    showAffCupModal.value = false
}
const noShowAgain = () => {
    noShowAgainAffCupModal.value = !noShowAgainAffCupModal.value
    localStorage.setItem(
        'noShowAgainAffCupModal',
        JSON.stringify(noShowAgainAffCupModal.value)
    )
    const now = dayjs().tz('Asia/Ho_Chi_Minh')
    const endOfDay = now.endOf('day')
    const timeRemaining = endOfDay.diff(now)
    setTimeout(() => {
        noShowAgainAffCupModal.value = false
        localStorage.setItem(
            'noShowAgainAffCupModal',
            JSON.stringify(noShowAgainAffCupModal.value)
        )
        showAffCupModal.value = true
    }, timeRemaining)
}
const handleToKSport = () => {
    if (isDeskTop.value) {
        router.push('/the-thao-ksport')
    } else {
        try {
            openSport({
                type: SPORT_TYPE.K_SPORT.toString(),
                newTab: true,
                link: 'the-thao-ksport',
                apiUrl: 'ksportsUrl',
                isMainApi: true,
            })
        } catch (error) {
            alert(t('error.maintain'))
            return
        }
    }
    closeSelf()
}
onMounted(() => {
    useModalStoreInstance.initShowAgainAffCupModal()
})
</script>
<style lang="scss" scoped>
.aff-modal {
    @media (max-width: 992px) {
        &:deep(.modal-dialog__wrap) {
            @apply max-w-full items-end p-0;
            .modal-content {
                @apply max-w-full rounded-[12px_12px_0_0];
            }
        }
    }
}
:deep(.modal-content) {
    @apply w-full overflow-visible lg:min-w-[658px] lg:max-w-[658px];
    background: linear-gradient(180deg, #3b3b3b 0%, #302f2f 25.05%);
}
.aff-body {
    &__scroll {
        @media (max-width: 992px) {
            @apply max-h-[63.31vh] overflow-y-auto;
        }
    }
    &__title {
        @apply mx-auto -mt-[120px] mb-6 flex h-[92px] w-[262px] items-center
        justify-center whitespace-pre-wrap pr-5 pt-8 text-center text-[18px] font-extrabold leading-[25px];
        background: url('/assets/images/aff-modal/title.png') center no-repeat;
        background-size: 100%;
        span {
            background: linear-gradient(
                180deg,
                #fff0d2 -14.29%,
                #ffcc6c 82.14%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        @media (max-width: 992px) {
            @apply -mt-[140px];
        }
    }
    &__ul {
        @apply pl-2.5;
        li {
            @apply relative pl-[12px] text-xs font-normal leading-5 lg:text-sm;
            &:before {
                @apply absolute left-0 top-[8px] size-[3px] rounded-full bg-white content-[''];
            }
            &:not(:last-child) {
                @apply mb-2;
            }
        }
    }
    &__btn {
        span {
            background: url('/assets/images/aff-modal/btn.png') center no-repeat;
            background-size: 100%;
            @apply mx-auto my-5 flex h-[60px] w-[183px] cursor-pointer items-center justify-center text-xl font-extrabold leading-8 text-[#7F190B] transition-[0.3s];
            &:hover {
                @apply opacity-80;
            }
        }
    }
    &__checked {
        @apply flex justify-center;
        span {
            @apply relative cursor-pointer pl-[24px];
            &:before,
            &:after {
                @apply absolute left-0 top-[2px] size-[16px] content-[''];
            }
            &:before {
                @apply rounded border border-solid border-[#AAAAAA];
            }
            &:after {
                background: url('/assets/images/aff-modal/checked.svg') center
                    no-repeat;
                background-size: 16px;
                @apply opacity-0;
            }
            &.active {
                &:after {
                    @apply opacity-100;
                }
            }
        }
    }
}
</style>
