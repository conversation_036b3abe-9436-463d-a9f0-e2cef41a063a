<template>
    <CommonModal
        @close="closeSelf"
        size="md"
        classCustomWrapper="race-modal"
        classCustom="golden-star-warrios-modal"
        sticky
    >
        <div
            class="close absolute right-1 top-1 cursor-pointer"
            @click="closeSelf"
        >
            <NuxtIcon name="close" class="text-[32px] text-white" />
        </div>
        <div class="golden-star-warrios-modal__content">
            <div
                class="header-content h-[202px] w-full bg-[url('/assets/images/golden-star-warrios/popup/bg-popup-mb.png')] bg-cover bg-center bg-no-repeat lg:bg-[url('/assets/images/golden-star-warrios/popup/bg-popup-pc.png')]"
            ></div>
            <div class="popup-content px-8 pb-8 pt-6">
                <div
                    class="text-center text-[20px] font-semibold leading-[calc(29/20)] text-white"
                >
                    Sự kiện <PERSON>ến Binh Sao Vàng đang diễn ra!
                </div>
                <p
                    class="mt-2 text-center text-sm leading-[calc(24/14)] text-white"
                >
                    Tham gia ngay để có cơ hội nhận thưởng khủng.
                </p>
                <div
                    @click="handleRedirectEvent"
                    class="mt-8 flex h-11 w-full cursor-pointer items-center justify-center rounded-lg bg-z-red-dit text-sm font-semibold uppercase text-white"
                >
                    CHI TIẾT SỰ KIỆN
                </div>
            </div>
        </div>
    </CommonModal>
</template>
<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'

const useModalStoreInstance = useModalStore()
const { handleModalClose } = useModalStoreInstance

const closeSelf = () => {
    if (process.client) {
        localStorage.setItem('goldenStarWarriosPopupClosed', 'true')
    }
    handleModalClose()
}

const handleRedirectEvent = () => {
    navigateTo(PAGE_URL.GOLDEN_STAR_WARRIORS)
    closeSelf()
}
</script>

<style lang="scss" scoped>
:deep(.golden-star-warrios-modal) {
    @apply relative h-[448px] w-full max-w-[398px] rounded-2xl bg-[linear-gradient(173.49deg,_#333333_-6.4%,_#2D2D2D_65.7%)] lg:h-[395px] lg:max-w-[504px];
}
</style>
