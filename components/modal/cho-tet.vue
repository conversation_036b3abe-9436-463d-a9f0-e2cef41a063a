<template>
    <CommonModal
        @close="closeSelf"
        size="md"
        sticky
        classCustomWrapper="free-spin-modal"
        classCustom="free-spin-inner"
    >
        <div class="refund-modal-content">
            <div
                class="close absolute right-1 top-1 cursor-pointer"
                @click="closeSelf"
            >
                <NuxtIcon name="close" class="text-[32px] text-z-old-silver" />
            </div>
            <div class="icons"></div>
            <div
                class="content mb-8 mt-6 whitespace-pre-line px-2 text-center text-white lg:px-0"
            >
                {{
                    t('cho_tet.popup_title', {
                        spin: freeSpinTetData?.free_spin || 10,
                    })
                }}
                {{ t('cho_tet.popup_des') }}
            </div>
            <div class="lg:px-[1px]">
                <button
                    @click="handlePlayGame"
                    type="button"
                    class="btn flex h-11 w-full items-center justify-center rounded-[8px] bg-z-red-dit text-sm font-semibold uppercase text-white lg:h-[46px] lg:rounded-[6px] lg:font-bold"
                >
                    {{ t('common.bet_now') }}
                </button>
            </div>
        </div>
    </CommonModal>
</template>

<script setup lang="ts">
import { CHO_TET } from '~/constants/game'
const useModalStoreInstance = useModalStore()
const { handleModalClose } = useModalStoreInstance
const usePromotionInstance = usePromotion()
const { closeFreeSpinTetModal } = usePromotionInstance
const { freeSpinTetData } = storeToRefs(usePromotionInstance)
const { openGame } = usePlayGame()
const { t } = useI18n()
const closeSelf = async () => {
    await closeFreeSpinTetModal()
    handleModalClose()
}

const handlePlayGame = () => {
    closeSelf()
    openGame(CHO_TET)
}
</script>

<style scoped lang="scss">
:deep(.free-spin-inner) {
    @apply my-0 h-[23.625rem] w-[21.625rem] max-w-[21.625rem] bg-[url('/assets/images/free-spin/popup/bg-mb.png')] bg-center bg-no-repeat px-6 lg:h-[384px] lg:w-[428px] lg:max-w-[428px] lg:bg-[url('/assets/images/free-spin/popup/bg-pc.png')] lg:px-[3.125rem];
}
.icons {
    @apply mx-auto mt-[3.2rem] flex h-[8.125rem] w-[8.125rem] items-center justify-center lg:mt-[3.5rem] lg:mt-[52px] lg:h-[134px] lg:w-[134px];
    background: url('/assets/images/cho-tet/spin.png') no-repeat;
    background-size: 100% 100%;
    mix-blend-mode: screen;
}
</style>
