<template>
    <CommonModal @close="closeSelf" size="xxl" sticky>
        <template #default>
            <div class="aff-gift__body">
                <div class="mb-2">{{ $t('uefa_popup.title') }}</div>
                <div
                    class="aff-gift__amount mb-2 flex h-[86px] items-center justify-center rounded-xl bg-white text-center font-black leading-[60px]"
                >
                    <span class="text-[32px]">
                        {{
                            uefaChampionDataPopup?.amount_txt ||
                            NumberUtils.formatAmount(
                                uefaChampionDataPopup?.amount,
                                'VND'
                            )
                        }}
                    </span>
                </div>
                <div class="whitespace-pre-line">
                    {{ $t('uefa_popup.description', { brandName }) }}
                </div>
                <div class="aff-gift__btn">
                    <span @click="closeSelf">{{ $t('uefa_popup.btn') }}</span>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { NumberUtils } from '~/utils'
const brandName = useRuntimeConfig().public.BRAND_NAME
const useModalStoreInstance = useModalStore()

const usePromotionInstance = usePromotion()
const { handleModalClose } = useModalStoreInstance
const { closeUefaModal } = usePromotionInstance
const { uefaChampionDataPopup } = storeToRefs(usePromotionInstance)

const closeSelf = async () => {
    await closeUefaModal(uefaChampionDataPopup.value?.id)
    handleModalClose()
}
</script>
<style lang="scss" scoped>
:deep(.modal-content) {
    @apply flex size-full h-[674px] flex-col justify-center py-5 lg:w-[658px];
    background: url('/assets/images/uefa-popup/bg-light.png') center no-repeat;
    background-size: 100% 100%;
}

.aff-gift {
    &__body {
        background: url('/assets/images/uefa-popup/bg-gift.png') center top
            no-repeat;
        background-size: 100% 100%;
        @apply mx-auto h-[376px] w-[380px] max-w-full px-[38.5px] pt-[140px] text-sm font-bold uppercase leading-[23px] text-black;
    }
    &__amount {
        span {
            @apply font-black;
            background: linear-gradient(90deg, #d00003 0%, #9d0003 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
    }
    &__btn {
        span {
            @apply mx-auto mt-[38px] flex h-[48px] w-[166px] cursor-pointer items-center justify-center text-base font-bold leading-[30px] text-white;
            background: url('/assets/images/uefa-popup/btn.png') center
                no-repeat;
            background-size: 100% 100%;
        }
    }
}
</style>
