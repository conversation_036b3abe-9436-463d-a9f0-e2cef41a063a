<template>
    <CommonModal
        @close="closeSelf"
        size="md"
        classCustomWrapper="race-modal"
        classCustom="club-world-cup"
        sticky
    >
        <div
            class="close absolute right-1 top-1 cursor-pointer"
            @click="closeSelf"
        >
            <NuxtIcon name="close" class="text-[32px] text-white" />
        </div>
        <div class="club-world-cup__content">
            <div
                class="header-content h-[200px] w-full bg-[url('/assets/images/club-world-cup/popup/bg-popup-mb.png')] bg-cover bg-center bg-no-repeat lg:bg-[url('/assets/images/club-world-cup/popup/bg-popup-pc.png')]"
            ></div>
            <div class="popup-content px-5 pb-8 pt-[23px]">
                <div
                    class="text-center text-[20px] font-semibold leading-[calc(29/20)] text-white"
                >
                    Sự kiện B<PERSON>ủ<br class="lg:hidden" />
                    Club WorldCup đang diễn ra!
                </div>
                <p
                    class="mt-2 text-center text-sm leading-[calc(24/14)] text-white"
                >
                    Tham gia ngay để có cơ hội nhận thưởng khủng.
                </p>
                <div
                    @click="handleRedirectEvent"
                    class="mx-auto mt-8 flex h-[39px] w-full max-w-[440px] cursor-pointer items-center justify-center rounded-lg bg-z-red-dit text-sm font-semibold uppercase text-white"
                >
                    chi tiết sự kiện
                </div>
            </div>
        </div>
    </CommonModal>
</template>
<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'

const useModalStoreInstance = useModalStore()
const { handleModalClose } = useModalStoreInstance

const closeSelf = () => {
    if (process.client) {
        localStorage.setItem('clubWorldCupPopupClosed', 'true')
    }
    handleModalClose()
}

const handleRedirectEvent = () => {
    navigateTo(PAGE_URL.CLUB_WORLD_CUP)
    closeSelf()
}
</script>

<style lang="scss" scoped>
:deep(.club-world-cup) {
    @apply relative min-h-[417px] w-full max-w-[400px] rounded-2xl bg-[linear-gradient(173.49deg,_#333333_-6.4%,_#2D2D2D_65.7%)] lg:min-h-[388px] lg:max-w-[504px];
}
</style>
