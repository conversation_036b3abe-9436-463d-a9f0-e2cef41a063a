<template>
    <CommonModal
        :show="showFifaClubModal"
        @close="closeSelf"
        size="xxl"
        class="aff-modal z-[103]"
    >
        <template #header>
            <div class="relative">
                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute right-[18.67px] top-[18.67px]"
                    @click="closeSelf"
                >
                    <img
                        :src="'/assets/images/fifa-club-modal/close.svg'"
                        alt="icon close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="px-4 text-white lg:px-6">
                <div
                    class="aff-body pb-6 text-left text-[14px] font-normal leading-5"
                >
                    <div class="aff-body__title">
                        <span>{{ $t('fifa_club_rules.title') }}</span>
                    </div>
                    <div class="aff-body__scroll">
                        <div class="aff-body__scroll--content scrollbar">
                            <div
                                class="mb-1 text-sm font-semibold uppercase leading-5"
                            >
                                {{ $t('fifa_club_rules.rule_title') }}
                            </div>
                            <div
                                class="mb-4 rounded-lg bg-[#292929] py-4 pl-2.5 pr-4"
                            >
                                <ul class="aff-body__ul">
                                    <li>
                                        {{ $t('fifa_club_rules.rule_des_1_1') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_1_2')
                                        }}</strong>
                                        {{ $t('fifa_club_rules.rule_des_1_3') }}
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.rule_des_2') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_2_1')
                                        }}</strong>
                                        {{ $t('fifa_club_rules.rule_des_2_2') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_2_3')
                                        }}</strong
                                        >{{
                                            $t('fifa_club_rules.rule_des_2_4')
                                        }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_2_5')
                                        }}</strong>
                                        {{ $t('fifa_club_rules.rule_des_2_6') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_2_7')
                                        }}</strong>.
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.rule_des_3') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_3_1')
                                        }}</strong>
                                        {{ $t('fifa_club_rules.rule_des_3_2') }}
                                        <strong>{{
                                            $t('fifa_club_rules.rule_des_3_3')
                                        }}</strong>.
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.rule_des_4') }}
                                    </li>
                                </ul>
                            </div>
                            <div
                                class="mb-1 text-sm font-semibold uppercase leading-5"
                            >
                                {{ $t('fifa_club_rules.terms_title') }}
                            </div>
                            <div
                                class="rounded-lg bg-[#292929] py-4 pl-2.5 pr-4"
                            >
                                <ul class="aff-body__ul">
                                    <li>
                                        {{ $t('fifa_club_rules.terms_des_1') }}
                                        <span class="font-bold">
                                            {{ brandName }}
                                        </span>.
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.terms_des_2') }}
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.terms_des_3') }}
                                    </li>
                                    <li>
                                        <span class="font-bold">
                                            {{ brandName }}
                                        </span>
                                        {{ $t('fifa_club_rules.terms_des_4') }}
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.terms_des_5') }}
                                        <span class="font-bold">
                                            {{ brandName }}
                                        </span>
                                        {{ $t('fifa_club_rules.terms_des_6') }}
                                    </li>
                                    <li>
                                        {{ $t('fifa_club_rules.terms_des_7') }}
                                    </li>
                                </ul>
                            </div>
                            <div class="aff-body__btn">
                                <span @click="handleToKSport">
                                    {{ $t('fifa_club_rules.btn') }}
                                </span>
                            </div>
                        </div>
                        <!--                        <div-->
                        <!--                            v-if="!showCheckedUEFAModal"-->
                        <!--                            class="aff-body__checked text-sm font-normal leading-6"-->
                        <!--                        >-->
                        <!--                            <span-->
                        <!--                                @click="noShowAgain"-->
                        <!--                                :class="{-->
                        <!--                                    active: noShowAgainUefaChampionModal,-->
                        <!--                                }"-->
                        <!--                                >{{ $t('uefa_rules.no_show') }}</span-->
                        <!--                            >-->
                        <!--                        </div>-->
                    </div>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useWindowSize } from '~/composables/use-window'
import { SPORT_TYPE } from '~/constants/sport'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import { useCountdown } from '~/composables/common/use-countdown'
dayjs.extend(timezone)

const router = useRouter()
const useModalStoreInstance = useModalStore()
const {
    showFifaClubModal,
    noShowAgainFifaClubModal,
    // showCheckedUEFAModal,
} = storeToRefs(useModalStoreInstance)
const brandName = useRuntimeConfig().public.BRAND_NAME
const { isDeskTop } = useWindowSize()
const { openSport } = usePlayGame()
const showAgainHours = Number(
    useRuntimeConfig().public.FIFA_CLUB_MODAL_SHOW_AGAIN_HOURS
)
const showAgainMilliseconds = showAgainHours * 60 * 60 * 1000
const { getServerTime } = useCountdown()

const closeSelf = () => {
    showFifaClubModal.value = false
    useModalStoreInstance.setShowCheckedFifaClubModal(false)
}
// const noShowAgain = () => {
//     noShowAgainUefaChampionModal.value = !noShowAgainUefaChampionModal.value
//     useModalStoreInstance.setShowAgainUefaChampionModal(
//         noShowAgainUefaChampionModal.value
//     )
//
//     if (noShowAgainUefaChampionModal.value) {
//         setTimeout(() => {
//             useModalStoreInstance.setShowAgainUefaChampionModal(false)
//             showUefaChampionModal.value = true
//         }, showAgainMilliseconds)
//     }
// }
const handleToKSport = () => {
    if (isDeskTop.value) {
        router.push('/the-thao-ksport')
    } else {
        try {
            openSport({
                type: SPORT_TYPE.K_SPORT.toString(),
                newTab: true,
                link: 'the-thao-ksport',
                apiUrl: 'ksportsUrl',
                isMainApi: true,
            })
        } catch (error) {
            alert(t('error.maintain'))
            return
        }
    }
    setTimeout(() => {
        closeSelf()
    }, 200)
}
// onMounted(() => {
//     useModalStoreInstance.initShowAgainUefaChampionModal()
//     useModalStoreInstance.initShowCheckedUEFAModal()
//
//     if (
//         noShowAgainUefaChampionModal.value &&
//         useModalStoreInstance.uefaChampionModalCheckTime
//     ) {
//         const checkTime = dayjs(
//             useModalStoreInstance.uefaChampionModalCheckTime
//         ).tz('Asia/Ho_Chi_Minh')
//         const now = getServerTime()
//         const timePassed = now.diff(checkTime)
//
//         if (timePassed >= showAgainMilliseconds) {
//             useModalStoreInstance.setShowAgainUefaChampionModal(false)
//             showUefaChampionModal.value = true
//         } else {
//             const remainingTime = showAgainMilliseconds - timePassed
//             setTimeout(() => {
//                 useModalStoreInstance.setShowAgainUefaChampionModal(false)
//                 showUefaChampionModal.value = true
//             }, remainingTime)
//         }
//     }
// })
</script>
<style lang="scss" scoped>
.aff-modal {
    @media (max-width: 992px) {
        &:deep(.modal-dialog__wrap) {
            @apply max-w-full items-end p-0;
            .modal-content {
                @apply max-w-full rounded-[12px_12px_0_0];
            }
        }
    }
}
:deep(.modal-content) {
    @apply w-full overflow-visible rounded-[16px] lg:min-w-[658px] lg:max-w-[658px];
    background: linear-gradient(180deg, #3b3b3b 0%, #302f2f 25.05%);
}
.aff-body {
    &__scroll {
        @media (max-width: 992px) {
            @apply h-[63.31vh];
            &--content {
                @apply max-h-[calc(100%_-_74px)] overflow-y-auto;
            }

            .aff-body__btn {
                @apply absolute bottom-0 left-0 right-0;
            }
        }
        @media screen and (max-width: 992px) and (orientation: landscape) {
            @apply max-h-[57.31vh];
        }
    }
    &__title {
        @apply mx-auto -mt-[35px] mb-[26px] flex h-[73px] w-[309px] items-center
        justify-center whitespace-pre-wrap text-center text-[18px] font-extrabold leading-6;
        background: url('/assets/images/fifa-club-modal/title.avif') center
            no-repeat;
        background-size: 100%;
        span {
            background: linear-gradient(
                180deg,
                #ffffff -14.29%,
                #ddeaff 82.14%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        @media (max-width: 992px) {
            @apply -mt-[36px] mb-[17px];
        }
        @media (max-width: 400px) {
            @apply scale-90;
        }
        @media (max-width: 359px) {
            @apply scale-[0.7];
        }
    }
    &__ul {
        @apply pl-2.5;
        li {
            @apply relative pl-[12px] text-sm font-normal leading-5;
            &:before {
                @apply absolute left-0 top-[8px] size-[3px] rounded-full bg-white content-[''];
            }
            &:not(:last-child) {
                @apply mb-2;
            }
        }
    }
    &__btn {
        span {
            background: url('/assets/images/fifa-club-modal/btn-rule.avif') center
                no-repeat;
            background-size: 100%;
            @apply mx-auto mt-6 mb-6 xl:mb-0 flex h-[44px] w-[181px] cursor-pointer items-center justify-center text-xl font-bold leading-6 text-[#17181A] transition-[0.3s];
            &:hover {
                @apply opacity-80;
            }
        }
    }
    &__checked {
        @apply flex justify-center;
        @media screen and (max-width: 992px) and (orientation: landscape) {
            @apply mb-[30px];
        }
        span {
            @apply relative cursor-pointer pl-[30px] text-[#D5D7D9];
            &:before,
            &:after {
                @apply absolute left-0 top-0 size-[24px] content-[''];
            }
            &:before {
                @apply rounded border-2 border-solid border-[#AAAAAA];
            }
            &:after {
                background: url('/assets/images/fifa-club-modal/checked.svg')
                    center no-repeat;
                background-size: 25px;
                @apply opacity-0;
            }
            &.active {
                &:after {
                    @apply opacity-100;
                }
            }
        }
    }
}
</style>
