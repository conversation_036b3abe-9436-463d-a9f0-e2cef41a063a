<template>
    <CommonModal
        :show="showWarningTooManyQrCodePay"
        @close="closeSelf"
        size="md"
        class="font-primary"
        class-custom="modal-confirm-cancel-coupon animated fadeInUp"
    >
        <div class="coupon-confirm">
            <button
                aria-label="Close"
                class="close absolute right-2.5 top-2.5"
                data-dismiss="modal"
                type="button"
                @click="closeSelf"
            >
                <NuxtIcon
                    class="mb-0 text-[2rem] text-z-gray-ice hover:opacity-70"
                    name="close"
                ></NuxtIcon>
            </button>
            <div class="modal-body">
                <div class="icon-header flex items-center justify-center">
                    <img
                        src="~/assets/images/icons/warning.svg"
                        alt="icon"
                        class="h-[7.5rem] w-[7.5rem] object-contain"
                    />
                </div>
                <div
                    class="title text-z-black-900 mt-4 text-center text-xl font-bold"
                >
                    {{ $t('deposit.modalConfirm.warningTitle') }}
                </div>
                <div>
                    {{ $t('deposit.modalConfirm.warning') }}
                </div>
                <div
                    class="btn-group mt-5 flex w-full justify-between gap-x-3 lg:mt-6"
                >
                    <button
                        class="btn-cancel hover-btn border-z-gray-100 bg-z-gray-80 text-z-black-700 mx-auto flex h-11 w-full flex-1 items-center justify-center rounded border border-solid text-sm font-medium uppercase"
                        @click="closeSelf"
                    >
                        {{ $t('deposit.modalConfirm.cancel') }}
                    </button>
                    <button
                        class="btn-accept hover-btn mx-auto flex h-11 w-full flex-1 items-center justify-center rounded bg-z-red-dit text-sm font-medium uppercase text-white"
                        @click="onClickLiveChat"
                    >
                        {{ $t('deposit.modalConfirm.cskh') }}
                    </button>
                </div>
            </div>
        </div>
    </CommonModal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore, useLiveChat } from '~/stores'
const useModalStoreInstance = useModalStore()
const { showWarningTooManyQrCodePay } = storeToRefs(useModalStoreInstance)
const useLiveChatInstance = useLiveChat()
const { onClickLiveChat } = useLiveChatInstance

const closeSelf = () => {
    showWarningTooManyQrCodePay.value = false
}
</script>

<style scoped lang="scss">
:deep(.modal-confirm-cancel-coupon) {
    @apply h-auto max-w-[22.875rem] px-4 py-5 lg:max-w-[440px] lg:p-8;
}
.reset-password {
    @apply flex w-full flex-col rounded-xl bg-white px-4 py-5 lg:p-8;
}
.btn-group {
    @apply mt-5 flex items-center justify-center gap-x-3 lg:mt-6;
    button {
        @apply h-11 w-full cursor-pointer rounded uppercase;
    }
}
</style>
