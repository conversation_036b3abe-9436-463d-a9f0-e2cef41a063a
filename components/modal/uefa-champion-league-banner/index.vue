<template>
    <CommonModal
        :show="showUefaChampionBannerModal"
        @close="closeSelf"
        size="xxl"
        class="z-[103]"
    >
        <template #header>
            <div class="relative">
                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute right-2 top-2 lg:right-[265px] lg:top-[20px]"
                    @click="closeSelf"
                >
                    <img
                        :src="'/assets/images/uefa-modal/close.svg'"
                        alt="icon close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="text-white">
                <div
                    class="aff-body text-left text-[14px] font-normal leading-5"
                >
                    <div class="aff-body__scroll">
                        <div>
                            <img
                                :src="`${staticUrl}/uefa-modal/banner.png`"
                                alt="UEFA Champion League"
                                class="hidden lg:block"
                            />
                            <img
                                :src="`${staticUrl}/uefa-modal/banner-mb.png`"
                                alt="UEFA Champion League"
                                class="lg:hidden"
                            />
                        </div>
                        <div class="aff-body__btn">
                            <span @click="openPopupRules">
                                {{ $t('uefa_rules.btn_join') }}
                            </span>
                        </div>
                        <div
                            class="aff-body__checked text-sm font-normal leading-6"
                        >
                            <span
                                @click="noShowAgainBanner"
                                :class="{
                                    active: noShowAgainUefaChampionBannerModal,
                                }"
                                >{{ $t('uefa_rules.no_show') }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const {
    showUefaChampionBannerModal,
    noShowAgainUefaChampionBannerModal,
    showUefaChampionModal,
} = storeToRefs(useModalStoreInstance)
const showAgainHours = Number(
    useRuntimeConfig().public.UEFA_MODAL_SHOW_AGAIN_HOURS
)
const showAgainMilliseconds = showAgainHours * 60 * 60 * 1000

const closeSelf = () => {
    showUefaChampionBannerModal.value = false
    if (!noShowAgainUefaChampionBannerModal.value) {
        noShowAgainUefaChampionBannerModal.value = true
        useModalStoreInstance.setShowAgainUefaChampionBannerModal(
            noShowAgainUefaChampionBannerModal.value
        )

        if (noShowAgainUefaChampionBannerModal.value) {
            setTimeout(() => {
                useModalStoreInstance.setShowAgainUefaChampionBannerModal(false)
                showUefaChampionBannerModal.value = true
            }, showAgainMilliseconds)
        }
    }
    
}
const noShowAgainBanner = () => {
    noShowAgainUefaChampionBannerModal.value =
        !noShowAgainUefaChampionBannerModal.value
    useModalStoreInstance.setNoShowAgainUefaChampionBannerModal(
        noShowAgainUefaChampionBannerModal.value
    )
}
const openPopupRules = () => {
    showUefaChampionModal.value = true
    useModalStoreInstance.setShowCheckedUEFAModal(true)
    closeSelf()
}
onMounted(() => {
    useModalStoreInstance.initShowAgainUefaChampionBannerModal()

    if (
        noShowAgainUefaChampionBannerModal.value &&
        useModalStoreInstance.uefaChampionBannerModalCheckTime
    ) {
        const checkTime = dayjs(
            useModalStoreInstance.uefaChampionBannerModalCheckTime
        ).tz('Asia/Ho_Chi_Minh')
        const now = dayjs().tz('Asia/Ho_Chi_Minh')
        const timePassed = now.diff(checkTime)

        if (timePassed >= showAgainMilliseconds) {
            useModalStoreInstance.setShowAgainUefaChampionBannerModal(false)
            showUefaChampionBannerModal.value = true
        } else {
            const remainingTime = showAgainMilliseconds - timePassed
            setTimeout(() => {
                useModalStoreInstance.setShowAgainUefaChampionBannerModal(false)
                showUefaChampionBannerModal.value = true
            }, remainingTime)
        }
    }
})
</script>
<style lang="scss" scoped>
:deep(.modal-content) {
    @apply w-full overflow-visible rounded-[16px] lg:min-w-[860px] lg:max-w-[860px];
    background-color: transparent;
}
.aff-body {
    &__title {
        @apply mx-auto -mt-[35px] mb-[26px] flex h-[73px] w-[309px] items-center
        justify-center whitespace-pre-wrap text-center text-[18px] font-extrabold leading-[25px];
        background: url('/assets/images/uefa-modal/title.png') center no-repeat;
        background-size: 100%;
        span {
            background: linear-gradient(
                180deg,
                #ffffff -14.29%,
                #ddeaff 82.14%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        @media (max-width: 992px) {
            @apply -mt-[36px] mb-[17px];
        }
    }
    &__ul {
        @apply pl-2.5;
        li {
            @apply relative pl-[12px] text-sm font-normal leading-5;
            &:before {
                @apply absolute left-0 top-[8px] size-[3px] rounded-full bg-white content-[''];
            }
            &:not(:last-child) {
                @apply mb-2;
            }
        }
    }
    &__btn {
        span {
            background: url('/assets/images/uefa-modal/btn.png') center
                no-repeat;
            background-size: 100%;
            @apply mx-auto my-[28px] flex h-[48px] w-[198px] cursor-pointer items-center justify-center text-base font-extrabold leading-8 text-white transition-[0.3s] lg:my-6;
            &:hover {
                @apply opacity-80;
            }
        }
    }
    &__checked {
        @apply flex justify-center;
        @media screen and (max-width: 992px) and (orientation: landscape) {
            @apply mb-[50px];
        }
        span {
            @apply relative cursor-pointer pl-[30px] text-[#D5D7D9];
            &:before,
            &:after {
                @apply absolute left-0 top-0 size-[24px] content-[''];
            }
            &:before {
                @apply rounded border-2 border-solid border-[#AAAAAA];
            }
            &:after {
                background: url('/assets/images/uefa-modal/checked.svg') center
                    no-repeat;
                background-size: 25px;
                @apply opacity-0;
            }
            &.active {
                &:after {
                    @apply opacity-100;
                }
            }
        }
    }
}
</style>
