<template>
    <CommonModal
        :show="showGo88Modal"
        @close="closeSelf"
        classCustom="floating-popup"
        size="xxl"
        sticky
    >
        <template #header>
            <div class="relative px-6 pt-8">
                <div class="hammer-icon2 mx-auto w-[162px]">
                    <img
                        :src="`${staticUrl}/home/<USER>/title.png`"
                        alt=""
                        class="go88"
                    />
                </div>

                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute right-2 top-2"
                    @click="closeSelf"
                >
                    <img
                        :src="`${staticUrl}/home/<USER>/close-popup.svg`"
                        alt=""
                        class="close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="px-3 pb-7 lg:px-7">
                <div class="floating__body text-[14px] font-normal leading-5">
                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-3">
                        <div
                            v-for="item in jackpotGo88Popup"
                            :key="item.gameId"
                            class="relative h-[112px] w-[141px] cursor-pointer overflow-hidden rounded-lg lg:h-[112px] lg:w-[164px]"
                        >
                            <div
                                @click.prevent="openGame(item?.game)"
                                :style="{
                                    backgroundImage: PARTNER_GAME_ID.includes(
                                        item?.gId
                                    )
                                        ? `url(${staticUrl}${item?.game.image}`
                                        : 'none',
                                }"
                                :class="{
                                    livestream: PARTNER_GAME_ID.includes(
                                        item?.gId
                                    ),
                                }"
                            >
                                <CommonImage
                                    lazyload
                                    classWrapper="inset-0 object-contain"
                                    class="h-full w-full"
                                    :src="`${staticUrl}${item?.game.image}`"
                                    :imageDefault="`${staticUrl}/loading.gif`"
                                    :alt="item?.game.name"
                                />
                                <div class="hunt-money" v-if="item?.jackpot">
                                    <img
                                        :src="`${staticUrl}/icons/icon-hu.svg`"
                                        class="h-3 w-3 lg:h-4 lg:w-4"
                                        alt="icon money"
                                    />
                                    <div class="hunt-money__value">
                                        <CommonAnimatedNumber
                                            :number="item?.jackpot"
                                            :previousNumber="
                                                prevJackpotGo88[item?.gameId]
                                            "
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { usePlayGame } from '~/composables/use-play-game'

import { PARTNER_GAME_ID, GAMES_DATA_CASINO } from '~/resources/live-casino'

const { $verifyToken, $loadNanoPlayer } = useNuxtApp()

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { showGo88Modal } = storeToRefs(useModalStoreInstance)
const socketStoreInstance = useSocket()
const { jackpotGo88Popup, prevJackpotGo88 } = storeToRefs(socketStoreInstance)
const { openGame } = usePlayGame()

const closeSelf = () => {
    showGo88Modal.value = false
}

watch(
    () => showGo88Modal.value,
    (showModal) => {
        nextTick(() => {
            if (showModal) {
                const tokenMap = new Map(GAMES_DATA_CASINO)
                const liveStreamItems = jackpotGo88Popup.value?.length
                    ? jackpotGo88Popup.value.filter((item) =>
                          ['qs_txgo-101', 'vgmn_109'].includes(item.gId)
                      )
                    : []
                if (liveStreamItems?.length) {
                    for (let i = 0; i < 2; i++) {
                        const liveCasinoKey = `go_${liveStreamItems[i].gId}`
                        const tokenInfo = tokenMap.get(liveCasinoKey)
                        if (tokenInfo) {
                            $loadNanoPlayer()
                            const domain = window.location.hostname
                            $verifyToken(
                                liveCasinoKey,
                                tokenInfo.id,
                                tokenInfo.key,
                                domain
                            )
                        }
                    }
                }
            }
        })
    }
)
</script>
<style lang="scss" scoped>
:deep(.floating-popup) {
    background: url('/assets/images/home/<USER>/bg-popup-mb.jpg') center
        center no-repeat;
    @media (min-width: 992px) {
        background: url('/assets/images/home/<USER>/bg-popup.jpg') center
            center no-repeat;
    }
    background-size: 100% 100% !important;
    @apply my-8 min-h-[549px] min-w-0 max-w-[343px] lg:min-h-[438px] lg:max-w-[612px];
}
.floating {
    &__body {
        background: url('/assets/images/home/<USER>/bg-content-popup-mb.png')
            center center no-repeat;
        @media (min-width: 992px) {
            background: url('/assets/images/home/<USER>/bg-content-popup.png')
                center center no-repeat;
        }
        background-size: 100% 100% !important;
        @apply -mt-4 h-[412px] p-3 pt-10 lg:h-[296px] lg:p-5 lg:pt-10;
        .livestream {
            @apply relative z-[4] h-full w-full max-w-full overflow-hidden rounded-lg p-[3px];
            --offset: 3px;
            img {
                @apply border-2 border-solid border-transparent;
            }
            &:before {
                @apply absolute left-2/4 top-2/4 aspect-[1] w-full -translate-x-2/4 -translate-y-2/4 animate-[rotate_3s_linear_infinite] content-[""];
                background: conic-gradient(
                    transparent 200deg,
                    #fff 240deg,
                    #fff 300deg,
                    transparent 340deg
                );
            }
            &:after {
                @apply absolute inset-[3px] z-10 h-full w-full animate-[flickerOpacity_2s_ease-in-out_infinite] rounded-lg content-[""];
                background: linear-gradient(
                    45deg,
                    #ffffff4d,
                    #fffc,
                    #fff,
                    #fffc,
                    #ffffff4d
                );
            }
        }
    }
}
:deep(.base-image) {
    @apply flex aspect-[166/131] size-full items-center justify-center  lg:aspect-[250/171];
    .v-lazy-image {
        @apply mx-auto my-0 object-contain p-[25%];
        &.v-lazy-image-loaded {
            @apply m-auto aspect-[166/115] size-full object-cover p-0 lg:aspect-[250/174];
        }
    }
}
.hunt-money {
    @apply absolute left-1 top-1 flex w-auto items-center gap-x-1 overflow-hidden rounded-[100px] bg-[#1D1D1D99] px-1.5 py-0.5 [backdrop-filter:blur(4px)];

    img {
        @apply translate-y-[0.5px];
    }
    &__value {
        span {
            @apply flex translate-y-[0.5px] justify-center text-xs font-medium leading-[1.4] text-[#FFCB41] lg:text-sm;
        }
    }
}
@keyframes flickerOpacity {
    0%,
    to {
        opacity: 0.3;
    }

    20% {
        opacity: 0;
    }

    40% {
        opacity: 0.3;
    }

    60% {
        opacity: 0;
    }

    80% {
        opacity: 0.3;
    }
}
@keyframes rotate {
    0% {
        transform: translate(-50%, -50%) scale(1.4) rotate(0);
    }

    to {
        transform: translate(-50%, -50%) scale(1.4) rotate(1turn);
    }
}
</style>
