<template>
    <CommonModal @close="closeSelf" size="xxl" sticky>
        <template #header>
            <div
                class="relative mx-auto w-[380px] max-w-full px-6 pt-6 text-[#feffff]"
            >
                <button
                    type="button"
                    aria-label="Close"
                    class="close absolute -top-4 right-0"
                    @click="closeSelf"
                >
                    <img
                        :src="'/assets/images/aff-popup/close.svg'"
                        alt="icon close"
                    />
                </button>
            </div>
        </template>
        <template #default>
            <div class="aff-gift__body">
                <div class="mb-2">{{ $t('aff_popup.title') }}</div>
                <div
                    class="aff-gift__amount mb-2 flex h-[86px] items-center justify-center rounded-xl bg-white text-center font-black leading-[60px]"
                >
                    <span
                        :class="
                            affDataPopup?.amount_txt
                                ? 'text-[48px]'
                                : 'text-[32px]'
                        "
                    >
                        {{
                            affDataPopup?.amount_txt ||
                            NumberUtils.formatAmount(
                                affDataPopup?.amount,
                                'VND'
                            )
                        }}
                    </span>
                </div>
                <div class="whitespace-pre-line">
                    {{ $t('aff_popup.description') }}
                </div>
                <div class="aff-gift__btn">
                    <span @click="closeSelf">{{ $t('aff_popup.btn') }}</span>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { NumberUtils } from '~/utils'

const useModalStoreInstance = useModalStore()

const usePromotionInstance = usePromotion()
const { handleModalClose } = useModalStoreInstance
const { closeAffModal } = usePromotionInstance
const { affDataPopup } = storeToRefs(usePromotionInstance)

const closeSelf = async () => {
    await closeAffModal(affDataPopup.value?.id)
    handleModalClose()
}
</script>
<style lang="scss" scoped>
:deep(.modal-content) {
    @apply flex size-full h-[674px] flex-col justify-center py-5 lg:w-[658px];
    background: url('/assets/images/aff-popup/bg-light.png') center no-repeat;
    background-size: 100% 100%;
}

.aff-gift {
    &__body {
        background: url('/assets/images/aff-popup/bg-gift.png') center top -20px
            no-repeat;
        background-size: 100% 100%;
        @apply mx-auto h-[350px] w-[380px] max-w-full px-9 pt-[110px] text-sm font-extrabold uppercase leading-[23px] text-black;
    }
    &__amount {
        span {
            background: linear-gradient(
                180deg,
                #df4928 -14.29%,
                #c83d11 82.14%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
    }
    &__btn {
        span {
            @apply mx-auto mt-[15px] flex h-[60px] w-[189px] cursor-pointer items-center justify-center text-xl font-bold leading-[30px] text-[#7F190B];
            background: url('/assets/images/aff-popup/btn.png') center no-repeat;
            background-size: 100% 100%;
        }
    }
}
</style>
