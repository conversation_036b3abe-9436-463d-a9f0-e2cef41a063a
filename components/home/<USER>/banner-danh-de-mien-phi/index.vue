<template>
    <div class="banner-event cursor-pointer"  @click.prevent="onClickBanner(bannerInfo)">
        <CommonImage
            class="size-full"
            :max="`(max-width: 991px)`"
            :src="`${staticUrl}/home/<USER>/danh-de-mien-phi/pc.png`"
            :srcMb="`${staticUrl}/home/<USER>/danh-de-mien-phi/mb.png`"
            alt="banner"
            loading="eager"
            :fetchpriority="'high'"
            :width="isMobile ? 430 : 'auto'"
            :height="isMobile ? 195 : 'auto'"
        />
    </div>
</template>

<script setup lang="ts">
import { type IBanner } from '~/composables/hero-banner/interface'
import { useHeroBanner } from '~/composables/hero-banner/use-hero-banner'
import { PAGE_URL } from '~/constants/page-urls'

const { isMobile } = useDevice()
const staticUrl = useRuntimeConfig().public.staticUrl

const { onClickBanner } = useHeroBanner()
const bannerInfo: IBanner = {
    link: PAGE_URL.LUCKY_DRAW,
    isLoginRequired: false,
    isNewTab: false,
    alt: '',
}
</script>

<style scoped>
.banner-event {
    cursor: pointer;
}
</style>
