<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeMount } from 'vue'
import { useModalStore } from '~/stores'
import { usePlayGame } from '~/composables/use-play-game'
import { useScrollLock } from '~/composables/use-scroll-lock'
import type { JackpotGame } from '~/interfaces/game'
import { formatVietnameseCurrency } from '~/utils/index'
import { storeToRefs } from 'pinia'

const staticUrl = useRuntimeConfig().public.staticUrl
const activeJackpot = ref<boolean>(false)
const timeOut = ref<ReturnType<typeof setTimeout> | null>(null)
const socketStoreInstance = useSocket()
const { jackpotGo88, prevJackpotGo88, jackpotGo88Popup, bigWinUser } = storeToRefs(socketStoreInstance)
const useModalStoreInstance = useModalStore()
const { showGo88Modal, noShowFloating } = storeToRefs(useModalStoreInstance)
const { openGame } = usePlayGame()
const { showMobile } = useCommon()
const { lockScroll, unlockScroll } = useScrollLock()

const componentX1 = ref<number>(0)
const componentX2 = ref<number>(0)
const componentY1 = ref<number>(0)
const componentY2 = ref<number>(0)
const isDragging = ref<boolean>(false)
const touchStartTime = ref(0)
const touchThreshold = 200 // milliseconds

const maxJackpotItem = computed(() => {
    if (jackpotGo88.value.length === 0) return null

    const maxAmount = Math.max(
        ...jackpotGo88.value.map((item: JackpotGame) => item.amount)
    )
    return jackpotGo88.value.find(
        (item: JackpotGame) => item.amount === maxAmount
    )
})

const totalJackpotEnabled = computed(() =>
    jackpotGo88Popup.value.reduce(
        (acc: number, cur: JackpotGame) => Math.round(acc + +cur.jackpot),
        0
    )
)

const closeJackpot = (event: MouseEvent): void => {
    event.preventDefault()
    event.stopPropagation()
    showGo88Modal.value = false
    isDragging.value = false

    noShowFloating.value = true
    localStorage.setItem('noShowFloating', JSON.stringify(noShowFloating.value))
}

const dragStart = (event: DragEvent): void => {
    isDragging.value = false
    
    lockScroll()
    
    const target = event.target as HTMLElement
    const rect = target.getBoundingClientRect()
    componentX1.value = rect.x
    componentY1.value = rect.y
    componentX2.value = rect.x
    componentY2.value = rect.y
}

const dragMove = (event: DragEvent): void => {
    isDragging.value = true
}

const dragEnd = (event: DragEvent): void => {
    unlockScroll()
    
    try {
        const minBoundaryX = 0 // Define the minimum X boundary
        const maxBoundaryX = window.innerWidth - 114 // Define the maximum X boundary
        const minBoundaryY = 105 // Define the minimum Y boundary
        const maxBoundaryY = window.innerHeight // Define the maximum Y boundary
        
        const target = event.target as HTMLElement
        const rect = target.getBoundingClientRect()

        componentX2.value = rect.x
        componentY2.value = rect.y

        // Calculate the new position within boundaries
        const adjustedX = Math.max(
            minBoundaryX,
            Math.min(componentX2.value, maxBoundaryX)
        )

        const adjustedY = Math.max(
            minBoundaryY,
            Math.min(componentY2.value, maxBoundaryY)
        )

        if (!showMobile.value) {
            target.style.position = 'fixed'
        }
        // Apply the adjusted position
        target.style.left = `${adjustedX}px`
        target.style.top = `${adjustedY}px`

        const isSameX =
            Math.floor(componentX1.value) - Math.floor(componentX2.value) <= 2
        const isSameY =
            Math.floor(componentY1.value) - Math.floor(componentY2.value) <= 2

        if (isSameX && isSameY && !isDragging.value) {
            showGo88Modal.value = true
            isDragging.value = true
        }
    } catch (error) {
        console.log('errors', error)
        unlockScroll()
    }
}

const onClickFloating = (): void => {
    showGo88Modal.value = true
    isDragging.value = true
}

const onTouchStart = (event: TouchEvent): void => {
    touchStartTime.value = new Date().getTime()
    
    lockScroll()
    
    dragStart(event as unknown as DragEvent)
}

const onTouchEnd = (event: TouchEvent): void => {
    const touchDuration = new Date().getTime() - touchStartTime.value
    
    unlockScroll()
    
    if (touchDuration < touchThreshold) {
        onClickFloating()
    }
    dragEnd(event as unknown as DragEvent)
}

watch(
    () => jackpotGo88.value,
    (jackpots: JackpotGame[]) => {
        const jackpotsGameIds =
            jackpots?.map((item: JackpotGame) => item.gameId) || []

        const isDifferent =
            jackpots?.length &&
            (jackpots?.length > prevJackpotGo88.value?.length ||
                prevJackpotGo88.value.some(
                    (item: JackpotGame) =>
                        !jackpotsGameIds.includes(item.gameId)
                ))

        if (isDifferent) {
            activeJackpot.value = true
            timeOut.value = setTimeout(() => {
                activeJackpot.value = false
            }, 5000)
        }
    },
    { immediate: true, deep: true }
)

let previousLength = jackpotGo88Popup.value.length
watch(jackpotGo88Popup.value, (newValue) => {
    const newLength = newValue.length
    if (newLength > previousLength) {
        noShowFloating.value = false
        localStorage.setItem(
            'noShowFloating',
            JSON.stringify(noShowFloating.value)
        )
    }
    previousLength = newLength
}, { deep: true })

onMounted(() => {
    useModalStoreInstance.initNoShowFloating()
})

onBeforeMount(() => {
    if (timeOut.value) clearTimeout(timeOut.value)
})
</script>

<template>
    <div>
        <div
            v-if="!noShowFloating"
            class="floating"
            v-drag
            @v-drag-start="dragStart"
            @v-drag-end="dragEnd"
            @v-drag-moving="dragMove"
            @touchstart="onTouchStart"
            @touchend="onTouchEnd"
        >
            <div class="floating__icon">
                <CommonImage
                    :src="`${staticUrl}/home/<USER>/close.svg`"
                    alt="close"
                    class="ml-auto mr-1 -mb-[18px] w-[14px] relative z-[1]"
                    @click.prevent="closeJackpot"
                    @touchend="closeJackpot"
                    width="14"
                    height="14"
                />
                <div class="hammer-icon relative">
                    <CommonImage
                        :src="`${staticUrl}/home/<USER>/float-icon.png`"
                        alt="floating icon"
                        class="w-[114px]"
                        width="114"
                        height="114"
                    />
                    <div
                        class="jackpot-number absolute !top-auto !bottom-[5px] !left-1/2 -translate-x-1/2 font-montserrat text-sm font-black leading-[calc(17/14)]"
                    >
                        {{ formatVietnameseCurrency(totalJackpotEnabled) }}
                    </div>
                </div>
            </div>
            <div
                v-if="bigWinUser"
                @click.prevent="openGame(maxJackpotItem?.game)"
                :class="['floating__box active', { active: activeJackpot }]"
            >
                <div class="floating__txt flex flex-col items-end justify-end">
                    <p>Chúc mừng {{ bigWinUser.username }}</p>
                    <p>
                        nổ hũ <span>{{ bigWinUser.winloss_txt }}</span> từ game
                        {{ bigWinUser.product }}!
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.floating {
    @apply fixed right-1 top-[35%] z-[101] cursor-pointer lg:right-3 lg:top-[45%] w-[114px];
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    
    &.drag-draggable {
        @apply fixed;
    }
    &__icon {
        @apply -mt-[105px] w-[114px] cursor-pointer;
        &.position-relative {
            @apply relative;
        }
    }
    &__box {
        background: url('/assets/images/home/<USER>/bg.png') center
            center no-repeat;
        background-size: 100% 100%;
        @apply absolute bottom-2 right-[70px] -z-[1] w-[0] whitespace-nowrap py-[13px] pl-[5px] pr-[40px] text-center text-xs font-black italic leading-[14.06px] opacity-0 transition-[0.5s];
        &.active {
            @apply w-full opacity-100;
        }
    }
    &__txt {
        p {
            background: linear-gradient(180deg, #ededed 59.5%, #ffed63 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            span {
                background: linear-gradient(180deg, #ffffff 0%, #ffe205 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                @apply border-b border-solid border-b-[#FFE205];
            }
        }
    }
    .jackpot-number {
        background: linear-gradient(180deg, #fff 0%, #ffe205 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}
</style>