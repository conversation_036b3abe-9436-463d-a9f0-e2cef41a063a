<script setup lang="ts">
import { useNuxtApp } from '#app'
import { useLiveCasinoStore } from '~/composables/use-livecasino'
import { useSocket } from '~/composables/use-socket'
import type { IGame, TokenInfo } from '~/interfaces/game'
import { storeToRefs } from 'pinia'
import { GAMES_DATA_CASINO } from '~/resources/live-casino'
const {
    $verifyToken,
    $muteAll,
    $setSoundOnByDivId,
    $setSoundOffByDivId,
    $checkPlayerByDivId,
    $checkPlayerErrorByDivId,
    $listenNetworkChange,
} = useNuxtApp()
const staticUrl = useRuntimeConfig().public.staticUrl
const useLiveCasinoStoreInstance = useLiveCasinoStore()
const { getGames } = useLiveCasinoStoreInstance
const socketStoreInstance = useSocket()

const { previousJackpots, jackpots, viewers, previousViewers } =
    storeToRefs(socketStoreInstance)
const { games } = storeToRefs(useLiveCasinoStoreInstance)
const queryParams = ref({
    type: '',
    partner: 'vingame',
    filter: '',
    sort: 'hot',
    key: '',
    limit: 20,
    page: 1,
})

await useAsyncData('livestream', () => getGames(queryParams.value))
const validGameIds = ['bc_77784', 'sb_77783', 'xd_77786']
const isVingameBC = ['bc_77784']
const activeIndex = ref(0)
const gameMutedList = ref<Record<string, boolean>>({})

const gameList = computed(() => {
    const filteredGames = games.value.filter((game: IGame) =>
        validGameIds.includes(game.partner_game_id || '')
    )
    return filteredGames
        .sort((a, b) => {
            const indexA = validGameIds.indexOf(a.partner_game_id)
            const indexB = validGameIds.indexOf(b.partner_game_id)
            return indexA - indexB
        })
        .slice(0, 3)
})

const setGameMutedList = (newVal: IGame[]) => {
    gameMutedList.value = newVal.reduce((acc, game) => {
        acc[`${game.partner}_${game.partner_game_id}`] = false
        return acc
    }, {})
}

onMounted(() => {
    setGameMutedList(games.value)
    $listenNetworkChange()
})

watch(
    () => games.value,
    (newVal) => {
        setGameMutedList(newVal)
    }
)
const tokenMap = new Map<string, TokenInfo>(GAMES_DATA_CASINO)
const handleItemLiveStream = (index: number, item: IGame) => {
    activeIndex.value = index
    const liveCasinoKey = `${item.partner_provider}_${item.partner_game_id}`
    const tokenInfo = tokenMap.get(liveCasinoKey)
    $muteAll()
    gameMutedList.value = Object.keys(gameMutedList.value).reduce(
        (acc, key) => {
            acc[key] = false
            return acc
        },
        {}
    )

    if (
        tokenInfo &&
        (!$checkPlayerByDivId(
            `${item.partner_provider}_${item.partner_game_id}`
        ) ||
            $checkPlayerErrorByDivId(
                `${item.partner_provider}_${item.partner_game_id}`
            ))
    ) {
        // const domain = 'z01sv02.s2z.mooo.com'

        const domain = window.location.hostname
        $verifyToken(liveCasinoKey, tokenInfo.id, tokenInfo.key, domain)
    }
}

const handleToggleRadio = (item: IGame) => {
    gameMutedList.value[`${item.partner}_${item.partner_game_id}`] =
        !gameMutedList.value[`${item.partner}_${item.partner_game_id}`]

    if (gameMutedList.value[`${item.partner}_${item.partner_game_id}`]) {
        $setSoundOnByDivId(`${item.partner_provider}_${item.partner_game_id}`)
    } else {
        $setSoundOffByDivId(`${item.partner_provider}_${item.partner_game_id}`)
    }
}
</script>
<template>
    <div
        v-if="gameList?.length"
        class="livestream-home mt-4 flex gap-x-[2vw] lg:hidden"
    >
        <div class="livestream-menu flex flex-col gap-y-[1.15vw]">
            <div
                v-for="(item, index) in gameList"
                :key="index"
                class="livestream-menu__item w-[12.796vw] cursor-pointer"
                @click="handleItemLiveStream(index, item)"
            >
                <img
                    :src="`${staticUrl}/home/<USER>/${
                        item?.partner_game_id
                    }${activeIndex === index ? '-active' : ''}.avif`"
                    :alt="item?.partner"
                    class="w-[12.796vw]"
                />
            </div>
        </div>
        <div class="w-[calc(100vw_-_17.126vw)]">
            <div
                v-for="(item, index) in gameList"
                :key="index"
                class="wrapper-content__data__col relative overflow-hidden rounded-lg"
                v-show="activeIndex === index"
            >
                <CommonLiveCasinoItem
                    :item="item"
                    :games="gameList"
                    :isLiveCasino="true"
                    isLoadedVideo
                    :isFavorite="queryParams.sort === 'favorite'"
                    :jackpots="jackpots"
                    :previousJackpots="previousJackpots"
                    :viewers="viewers"
                    :previousViewers="previousViewers"
                    :isMuted="
                        gameMutedList[`${item.partner}_${item.partner_game_id}`]
                    "
                    @toggleRadio="handleToggleRadio(item)"
                    page="home"
                />
                <div class="livestream-home__highlight">
                    <div class="livestream-home__highlight--name">
                        {{ item?.name }}
                    </div>
                    <div
                        v-if="isVingameBC.includes(item?.partner_game_id)"
                        class="x30 flex items-center gap-x-[3px]"
                    >
                        <span>{{ $t('livestream.winning') }}</span>
                        <img
                            :src="`${staticUrl}/home/<USER>/x30.webp`"
                            :alt="item?.partner"
                            class="-mr-[7px] -mt-[3.5px] w-[38px]"
                        />
                    </div>
                    <div
                        v-if="item?.partner_game_id === 'sb_77783'"
                        class="x30 flex items-center gap-x-[3px] !pr-[12px]"
                    >
                        <span>{{ $t('livestream.winning_2') }}</span>
                        <img
                            :src="`${staticUrl}/home/<USER>/x999.webp`"
                            :alt="item?.partner"
                            class="-mr-[7px] -mt-[5px] w-[44px]"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.livestream-home {
    &:deep(.game-item) {
        .game-name {
            @apply hidden;
        }

        .favorite-item {
            @apply absolute left-2 top-2 z-[12] flex h-6 w-6 items-center justify-center rounded-[4px] bg-[#00000099];

            span {
                @apply text-[16px];
            }
        }

        .hunt-money-video {
            @apply absolute bottom-[8px] left-[8px] flex h-[20px] items-center gap-x-[4px] rounded-full bg-[#00000080] px-[4px] py-0 pr-[6px];

            img {
                @apply size-4 #{!important};
            }

            &__value {
                span {
                    @apply text-[14px] leading-[24px] text-[#FACC15];
                }
            }
        }

        .logo-partner {
            @apply bottom-[8px] right-[8px];

            &.go {
                img {
                    @apply h-[27px] #{!important};
                }
            }

            &.vingame {
                img {
                    @apply h-[38px] #{!important};
                }
            }
        }

        .js-radio {
            @apply block;
        }

        .img-video-custom {
            @apply size-full object-cover p-0;
        }

        .item-viewer-ct {
            @apply left-auto right-[8px] top-[8px];
        }

        .item-viewer {
            @apply flex h-[24px] items-center rounded-[8px] bg-[#00000080] px-[8px] py-0;
        }

        .game__label-partner--image {
            img {
                @apply opacity-0;
            }
        }

        .game__item__thumb {
            @apply aspect-[289/162] size-full #{!important};
        }

        video {
            @apply object-cover;
        }
    }

    &__highlight {
        @apply pointer-events-none absolute bottom-[8px] left-[8px] z-[10] flex flex-col items-start justify-start gap-[4px] text-left font-roboto text-[16px] font-semibold;

        &--name {
            @apply align-middle font-medium capitalize leading-[100%] tracking-[0%];
        }

        span {
            @apply text-[10px];
            background: linear-gradient(
                90deg,
                #fcdb87 0%,
                #ffb700 50%,
                #ffd56a 100%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .x30 {
            @apply flex h-[26px] items-center justify-center rounded-[48px] bg-[#00000080] px-[8px];
        }

        .sicbo {
            @apply flex h-[18px] items-center justify-center rounded-[48px] bg-[#00000080] px-[4px] font-normal;
        }

        &.no-x30 {
            @apply bottom-[34px];
        }
    }
}

.baccarat-overlay {
    position: relative;

    &::after {
        content: '';
        position: absolute;
        width: 12.796vw;
        height: 20.59%;
        left: -4.5%;
        bottom: 10.5%;
        background-image: var(--baccarat-text-url);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }

    &.baccarat-active::after {
        background-image: var(--baccarat-text-active-url);
    }
}
</style>
