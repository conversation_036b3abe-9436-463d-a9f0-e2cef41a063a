<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner(bannerInfo)">
        <div>
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/home/<USER>/baccarrat/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/baccarrat/mb.jpg`"
                alt="banner"
                :width="isMobile ? 430 : 'auto'" :height="isMobile ? 195 : 'auto'"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { type IBanner } from '~/composables/hero-banner/interface'
import { useHeroBanner } from '~/composables/hero-banner/use-hero-banner'
import { PAGE_URL } from '~/constants/page-urls'
const { isMobile } = useDevice()

const staticUrl = useRuntimeConfig().public.staticUrl
const { onClickBanner } = useHeroBanner()
const bannerInfo: IBanner = {
    bannerId: 'baccarrat',
    link: PAGE_URL.LIVE_CASINO_BACCARRAT,
    isLoginRequired: false,
    isNewTab: false,
    alt: '',
}
</script>
