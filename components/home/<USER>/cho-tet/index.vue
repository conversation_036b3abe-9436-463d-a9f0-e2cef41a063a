<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner(bannerInfo)">
        <div>
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/home/<USER>/cho-tet/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/cho-tet/mb.jpg`"
                alt="banner"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { type IBanner } from '~/composables/hero-banner/interface'
import { useHeroBanner } from '~/composables/hero-banner/use-hero-banner'
import { PAGE_URL } from '~/constants/page-urls'

const staticUrl = useRuntimeConfig().public.staticUrl
const { onClickBanner } = useHeroBanner()
const bannerInfo: IBanner = {
    link: PAGE_URL.CHO_TET,
    isLoginRequired: false,
    isNewTab: false,
    alt: '',
}
</script>
