<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner">
        <div>
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/home/<USER>/fifa-club/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/fifa-club/mb.jpg`"
                alt="banner"
                loading="eager"
                :fetchpriority="'high'"
                :width="isMobile ? 430 : 'auto'"
                :height="isMobile ? 195 : 'auto'"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { useModalStore } from '~/stores'
const { isMobile } = useDevice()

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { showFifaClubModal } = storeToRefs(useModalStoreInstance)
const onClickBanner = () => {
    showFifaClubModal.value = true
    useModalStoreInstance.setShowCheckedFifaClubModal(true)
}
</script>
