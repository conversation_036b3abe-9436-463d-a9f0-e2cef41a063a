<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner(bannerInfo)">
        <div>
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/home/<USER>/anh-tai/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/anh-tai/mb.jpg`"
                alt="banner"
                loading="lazy"
                :fetchpriority="'low'"
                :width="isMobile ? 430 : 'auto'"
                :height="isMobile ? 195 : 'auto'"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { type IBanner } from '~/composables/hero-banner/interface'
import { useHeroBanner } from '~/composables/hero-banner/use-hero-banner'
import { PAGE_URL } from '~/constants/page-urls'
const { isMobile } = useDevice()
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const staticUrl = useRuntimeConfig().public.staticUrl
const { onClickBanner } = useHeroBanner()
const bannerInfo: IBanner = {
    link: (user.value && user.value?.package_id) ? PAGE_URL.HOAN_TRA : PAGE_URL.BONUS_110,
    isLoginRequired: true,
    isNewTab: false,
    alt: '',
}
</script>
