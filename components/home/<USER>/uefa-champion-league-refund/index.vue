<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner">
        <div>
            <CommonImage
                class="size-full"
                :max="`(max-width: 991px)`"
                :src="`${staticUrl}/home/<USER>/uefa-champion-league/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/uefa-champion-league/mb.jpg`"
                alt="banner"
                loading="lazy"
                :fetchpriority="'low'"
                :width="isMobile ? 430 : 'auto'"
                :height="isMobile ? 195 : 'auto'"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { useModalStore } from '~/stores'
const { isMobile } = useDevice()

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { showUefaChampionModal } = storeToRefs(useModalStoreInstance)
const onClickBanner = () => {
    showUefaChampionModal.value = true
    useModalStoreInstance.setShowCheckedUEFAModal(true)
}
</script>
