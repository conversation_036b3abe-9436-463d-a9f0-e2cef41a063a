<template>
    <div class="hero-banner cursor-pointer" @click="onClickBanner(bannerInfo)">
        <div class="relative">
            <CommonImage
                class="size-full max-md:aspect-[430/195]"
                :max="`(max-width: 991px)`"
                :src="showMobile ? `${staticUrl}/home/<USER>/golden-star-warrios/mb.jpg` : `${staticUrl}/home/<USER>/golden-star-warrios/pc.jpg`"
                alt="banner" loading="lazy" :fetchpriority="'low'"
                :width="showMobile ? 430 : 'auto'" :height="showMobile ? 195 : 'auto'"
            />
            <ClientOnly>
                <GoldenStarWarriosCommonCountdown
                    v-if="isMounted"
                    className="hero-golden-star-warrios"
                />
                <div
                    v-if="!isAfterEndDate && isMounted"
                    class="absolute left-[12px] top-[74%] w-[50%] flex items-center justify-center lg:right-[16%] lg:top-[77.1%] lg:left-auto lg:flex lg:w-[30%]"
                >
                    <button type="button" class="event-button" aria-label="Tham gia"></button>
                </div>
            </ClientOnly>
        </div>
    </div>
</template>

<script setup lang="ts">
import {useCountdown} from '~/composables/common/use-countdown'
import { type IBanner } from '~/composables/hero-banner/interface'
import { useHeroBanner } from '~/composables/hero-banner/use-hero-banner'
import { PAGE_URL } from '~/constants/page-urls'

const { showMobile } = useCommon()

const staticUrl = useRuntimeConfig().public.staticUrl

const { GOLDEN_STAR_WARRIORS_EVENT_START_DAY, GOLDEN_STAR_WARRIORS_EVENT_END_DAY } = useRuntimeConfig().public

const { isAfterEndDate } = useCountdown(GOLDEN_STAR_WARRIORS_EVENT_START_DAY, GOLDEN_STAR_WARRIORS_EVENT_END_DAY)
const isMounted = ref(false)

const { onClickBanner } = useHeroBanner()
const bannerInfo: IBanner = {
    link: PAGE_URL.GOLDEN_STAR_WARRIORS,
    isLoginRequired: false,
    isNewTab: false,
    alt: '',
}
onMounted(() => {
    nextTick(() => {
        isMounted.value = true
    })
})
</script>
<style lang="scss" scoped rel="preload">
.event-button {
    @apply h-10 w-[6.6875rem] lg:w-[12.65625rem] lg:h-[4.6875rem] animate-[1s_brightness_infinite] lg:bg-[url('/assets/images/golden-star-warrios/pc/thamgia.png')] bg-[url('/assets/images/golden-star-warrios/sp/thamgia.png')] bg-cover bg-center bg-no-repeat;
}
:deep(.hero-golden-star-warrios) {
    .is-finish {
        @apply left-[28%] right-0 top-[36%] lg:left-auto lg:right-[17%] lg:top-[60%];
        @include mba() {
            .countdown-top__content {
                @apply w-max min-w-max gap-x-1 rounded pl-2 pr-1 py-[5.71px];
                .countdown-list {
                    @apply min-w-[8.3125rem];
                }
                .countdown-title {
                    @apply text-[0.5625rem] font-medium;
                }
               
            }
        }
        .countdown {
            @apply lg:w-[272px] lg:h-[50px] lg:text-[16px] text-[9px] font-bold w-[136px] h-[25px];
        }
    }

    .countdown-top {
        
        &:not(.is-start), &:not(.is-end) {
            @apply max-[1023px]:left-[14%] max-[680px]:left-[10%] max-[420px]:left-[4%];
        }
    }
}
@keyframes brightness {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.1);
    }
    100% {
        filter: brightness(1);
    }
}
</style>
