<template>
    <client-only>
        <div class="relative min-h-[85vh]">
            <div v-if="!isLoading" class="event">
                <ClubWorldCupBanner />
                <div class="container">
                    <ClubWorldCupRanks
                        class="hidden lg:block"
                        v-if="isDeskTop"
                    />
                    <ClubWorldCupRanksMb class="block lg:hidden" v-else />
                    <ClubWorldCupCommonListSport />
                    <ClubWorldCupHotmatch />
                    <ClubWorldCupRule />
                </div>
            </div>
            <div v-else class="loading-container">
                <div class="loader"></div>
            </div>
        </div>
    </client-only>
</template>

<script setup lang="ts">
const { isDeskTop } = useWindowSize()
const isLoading = ref(true)

onMounted(async () => {
    try {
        await new Promise((resolve) => setTimeout(resolve, 1500))
        isLoading.value = false
    } catch (error) {
        isLoading.value = false
    }
})
</script>
<style lang="scss" scoped>
.event {
    @apply size-full bg-[#18112E] bg-center bg-no-repeat lg:bg-[url('/assets/images/club-world-cup/bg.png')] lg:pb-[9rem];
    background-size: 100% 100%;
}
.container {
    @apply px-4;
}
.loading-container {
    @apply absolute left-0 top-0 flex h-full w-full items-center justify-center bg-[#18112E];
}
.loader {
    @apply h-16 w-16 animate-spin rounded-full border-8 border-solid border-[#ffb800] border-t-transparent;
}
</style>
