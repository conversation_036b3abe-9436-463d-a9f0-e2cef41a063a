<template>
    <div class="p-4 lg:px-0 lg:py-8">
        <component
            v-if="megaData?.component"
            :is="megaData?.component"
            :token="megaData?.meta.token"
            :login="SITE_URL + '/login'"
            :color="megaData.color || null"
        ></component>
    </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '~/composables/use-window'
import { useMegaPower } from '~/composables/use-mega-power'
import type { MegaPower } from '~/interfaces/game'

const SITE_URL = useRuntimeConfig().public.SITE_URL
const { items, getMegaPower } = useMegaPower()
const { isDeskTop } = useWindowSize()
const megaData = computed(() => {
    return isDeskTop.value ? items.value.url : items.value.url_mobile
})
onMounted(() => {
    nextTick(async () => {
        await getMegaPower('vingame','power655')
    })
})
watch(
    () => megaData?.value,
    (scriptUrl: MegaPower) => {
        if (scriptUrl) {
            useHead({
                script: [
                    {
                        src: scriptUrl.script,
                        crossorigin: '',
                        type: 'module',
                    },
                ],
            })
        }
    }
)
</script>

<style scoped></style>
