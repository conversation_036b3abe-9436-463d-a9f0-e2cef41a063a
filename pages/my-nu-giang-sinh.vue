<template>
    <div class="freespin relative overflow-hidden">
        <FreeSpinBanner />
        <div
            class="h-full w-full bg-[url('/assets/images/free-spin/bg-mb.png')] bg-cover bg-center bg-no-repeat pb-5 pt-4 lg:bg-[url('/assets/images/free-spin/bg-pc.png')] lg:pb-[8rem] lg:pt-8"
        >
            <div class="container">
                <FreeSpinRewards />
                <FreeSpinRules />
            </div>
        </div>

        <div class="absolute top-0">
            <div v-for="i in 180" :key="i" class="snow"></div>
        </div>
    </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.container {
    @apply px-[15px] lg:px-4;
}

@function random_range($min, $max) {
    $rand: random();
    $random_range: $min + floor($rand * (($max - $min) + 1));
    @return $random_range;
}
.snow {
    $total: 180;
    position: absolute;
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    @for $i from 1 through $total {
        $random-x: random(1000000) * 0.0001vw;
        $random-offset: random_range(-100000, 100000) * 0.0001vw;
        $random-x-end: $random-x + $random-offset;
        $random-x-end-yoyo: $random-x + ($random-offset / 2);
        $random-yoyo-time: random_range(30000, 80000) / 100000;
        $random-yoyo-y: $random-yoyo-time * 100vh;
        $random-scale: random(10000) * 0.0001;
        $fall-duration: random_range(30, 50) * 1s;
        $fall-delay: random(30) * -1s;
        &:nth-child(#{$i}) {
            opacity: random(10000) * 0.0001;
            transform: translate($random-x, -10px) scale($random-scale);
            animation: fall-#{$i} $fall-duration $fall-delay linear infinite;
        }
        @keyframes fall-#{$i} {
            #{percentage($random-yoyo-time)} {
                transform: translate($random-x-end, $random-yoyo-y)
                    scale($random-scale);
            }
            to {
                transform: translate($random-x-end-yoyo, 200vh)
                    scale($random-scale);
            }
        }
    }
}
@keyframes snowfall {
    0% {
        transform: translate3d(var(--left-ini), 0, 0);
    }
    100% {
        transform: translate3d(var(--left-end), 200vh, 0);
    }
}
</style>
