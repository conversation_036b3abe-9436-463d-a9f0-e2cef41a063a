<template>
    <div class="event">
        <TopEventBanner />
        <div class="container">
            <TopEventRanks class="hidden lg:block" v-if="isDeskTop" />
            <TopEventRanksMb class="block lg:hidden" v-else />
            <TopEventCommonListSport />
            <TopEventHotmatch />
            <TopEventRule />
        </div>
    </div>
</template>

<script setup lang="ts">
const { isDeskTop } = useWindowSize()
</script>
<style lang="scss" scoped>
.event {
    @apply size-full bg-[#18112E] bg-center bg-no-repeat pb-[3.75rem] lg:bg-[url('/assets/images/top-event/bg.jpg')] lg:pb-[9rem];
    background-size: 100% 100%;
}
.container {
    @apply px-4;
}
</style>
