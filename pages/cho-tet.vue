<script setup lang="ts"></script>
<template>
    <div class="event-tet">
        <ChoTetBanner />
        <ChoTetRules />
        <ChoTetTerms />
        <div class="pointer-events-none fixed left-0 top-0 size-full">
            <div class="firework"></div>
            <div class="firework"></div>
            <div class="firework"></div>
            <div class="firework"></div>
            <div class="firework"></div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.event-tet {
    background: url('/assets/images/cho-tet/bg.jpg') center top no-repeat;
    background-size: 100% 100%;
    @apply relative pb-[32px] lg:pb-[50px];
    @media (max-width: 992px) {
        background: url('/assets/images/cho-tet/bg-mb.jpg') center top no-repeat;
        background-size: 100%;
    }
}
@keyframes firework {
    0% {
        transform: translate(var(--x), var(--initialY));
        width: var(--initialSize);
        opacity: 0;
    }
    50% {
        width: 0.5vmin;
        opacity: 1;
    }
    100% {
        width: var(--finalSize);
        opacity: 0;
    }
}
.firework,
.firework::before,
.firework::after {
    --initialSize: 0.5vmin;
    --finalSize: 45vmin;
    --particleSize: 0.2vmin;
    --color1: yellow;
    --color2: khaki;
    --color3: white;
    --color4: lime;
    --color5: gold;
    --color6: mediumseagreen;
    --y: -30vmin;
    --x: -50%;
    --initialY: 60vmin;
    content: '';
    animation: firework 3s infinite;
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translate(-50%, var(--y));
    width: var(--initialSize);
    aspect-ratio: 1;
    background: radial-gradient(
                circle,
                var(--color1) var(--particleSize),
                #0000 0
            )
            50% 0%,
        radial-gradient(circle, var(--color2) var(--particleSize), #0000 0) 100%
            50%,
        radial-gradient(circle, var(--color3) var(--particleSize), #0000 0) 50%
            100%,
        radial-gradient(circle, var(--color4) var(--particleSize), #0000 0) 0%
            50%,
        radial-gradient(circle, var(--color5) var(--particleSize), #0000 0) 80%
            90%,
        radial-gradient(circle, var(--color6) var(--particleSize), #0000 0) 95%
            90%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 90%
            70%,
        radial-gradient(circle, var(--color2) var(--particleSize), #0000 0) 100%
            60%,
        radial-gradient(circle, var(--color3) var(--particleSize), #0000 0) 55%
            80%,
        radial-gradient(circle, var(--color4) var(--particleSize), #0000 0) 70%
            77%,
        radial-gradient(circle, var(--color5) var(--particleSize), #0000 0) 22%
            90%,
        radial-gradient(circle, var(--color6) var(--particleSize), #0000 0) 45%
            90%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 33%
            70%,
        radial-gradient(circle, var(--color2) var(--particleSize), #0000 0) 10%
            60%,
        radial-gradient(circle, var(--color3) var(--particleSize), #0000 0) 31%
            80%,
        radial-gradient(circle, var(--color4) var(--particleSize), #0000 0) 28%
            77%,
        radial-gradient(circle, var(--color5) var(--particleSize), #0000 0) 13%
            72%,
        radial-gradient(circle, var(--color6) var(--particleSize), #0000 0) 80%
            10%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 95%
            14%,
        radial-gradient(circle, var(--color2) var(--particleSize), #0000 0) 90%
            23%,
        radial-gradient(circle, var(--color3) var(--particleSize), #0000 0) 100%
            43%,
        radial-gradient(circle, var(--color4) var(--particleSize), #0000 0) 85%
            27%,
        radial-gradient(circle, var(--color5) var(--particleSize), #0000 0) 77%
            37%,
        radial-gradient(circle, var(--color6) var(--particleSize), #0000 0) 60%
            7%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 22%
            14%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 45%
            20%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 33%
            34%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 10%
            29%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 31%
            37%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 28%
            7%,
        radial-gradient(circle, var(--color1) var(--particleSize), #0000 0) 13%
            42%;
    background-size: var(--initialSize) var(--initialSize);
    background-repeat: no-repeat;
}

.firework::before {
    --x: -50%;
    --y: -50%;
    --initialY: -50%;
    transform: translate(-50%, -50%) rotate(40deg) scale(1.3) rotateY(40deg);
}

.firework::after {
    --x: -50%;
    --y: -50%;
    --initialY: -50%;
    transform: translate(-50%, -50%) rotate(170deg) scale(1.15) rotateY(-30deg);
}

.firework:nth-child(2) {
    --x: 30vmin;
}

.firework:nth-child(2),
.firework:nth-child(2)::before,
.firework:nth-child(2)::after {
    --color1: pink;
    --color2: violet;
    --color3: fuchsia;
    --color4: orchid;
    --color5: plum;
    --color6: lavender;
    --finalSize: 40vmin;
    left: 30%;
    top: 60%;
    animation-delay: -0.5s;
}

.firework:nth-child(3) {
    --x: -30vmin;
    --y: -50vmin;
}

.firework:nth-child(3),
.firework:nth-child(3)::before,
.firework:nth-child(3)::after {
    --color1: #ffe8b2;
    --color2: #ffdd93;
    --color3: #ffdd91;
    --color4: #ffffa4;
    --color5: SkyBlue;
    --color6: lavender;
    --finalSize: 50vmin;
    left: 80%;
    top: 50%;
    animation-delay: -1s;
}
.firework:nth-child(4) {
    --x: -30vmin;
    --y: -50vmin;
}

.firework:nth-child(4),
.firework:nth-child(4)::before,
.firework:nth-child(4)::after {
    --color1: yellow;
    --color2: khaki;
    --color3: white;
    --color4: lime;
    --color5: gold;
    --color6: mediumseagreen;
    --finalSize: 35vmin;
    left: 80%;
    top: 100%;
    animation-delay: -1.5s;
}
.firework:nth-child(5) {
    --x: -20vmin;
    --y: -30vmin;
}

.firework:nth-child(5),
.firework:nth-child(5)::before,
.firework:nth-child(5)::after {
    --color1: yellow;
    --color2: khaki;
    --color3: white;
    --color4: lime;
    --color5: gold;
    --color6: mediumseagreen;
    --finalSize: 45vmin;
    left: 50%;
    top: 90%;
    animation-delay: -2s;
}
</style>
