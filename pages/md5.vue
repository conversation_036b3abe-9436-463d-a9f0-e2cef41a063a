<template>
    <div>
        <c2-ldmd5 :token="user?.tp_token?user?.tp_token: ''" color="#004b9b" :login="SITE_URL + '/login'" />
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useUserStore } from '~/composables/use-user'
const SITE_URL = useRuntimeConfig().public.SITE_URL
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)
useHead({
    script: [
        {
            src: 'https://assets.vgjt.info/js/ldmd5.js',
            crossorigin: '',
            type: 'module',
        },
    ],
})
</script>

<style scoped></style>
