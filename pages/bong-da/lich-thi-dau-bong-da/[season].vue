<template>
    <ScheduleFootball
        match
        :leagues="leagues"
        :selectedLeague="selectedLeague"
        @select-league="handleChangeLeague"
    />
</template>
<script setup lang="ts">
import type { ILeague } from '~/composables/schedules/interface'

const emit = defineEmits<{
    (e: 'select-league', league: ILeague): void
}>()
const handleChangeLeague = (league: ILeague) => {
    emit('select-league', league)
}
const props = defineProps<{
    leagues: ILeague[]
    selectedLeague: ILeague
}>()
</script>
