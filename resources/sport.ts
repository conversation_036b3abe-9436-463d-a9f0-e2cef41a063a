import { SPORT_TYPE } from '~/constants/sport'
import type { ISport } from '~/interfaces/sport'

export const SPORT_LIST: ISport[] = [
    {
        type: SPORT_TYPE.K_SPORT,
        title: 'sportList.k_sport',
        imgPc: '/assets/images/sport/k.png',
        imgMb: '/assets/images/sport/k-mb.png',
        url: 'the-thao-ksport',
        apiUrl: 'ksportsUrl',
    },
    {
        type: SPORT_TYPE.A_SPORT,
        title: 'sportList.saba_sport',
        imgPc: '/assets/images/sport/saba.png',
        imgMb: '/assets/images/sport/saba-mb.png',
        url: 'the-thao-saba',
        apiUrl: 'athenaUrl',
    },
    {
        type: SPORT_TYPE.E_SPORT,
        title: 'sportList.e_sport',
        imgPc: 'assets/images/sport/bti.webp',
        imgMb: 'assets/images/sport/bti-mb.webp',
        url: 'bti',
        apiUrl: 'ssportUrl',
    },
    {
        type: SPORT_TYPE.IM_SPORT,
        title: 'sportList.im_sport',
        imgPc: 'assets/images/sport/im.webp',
        imgMb: 'assets/images/sport/im-mb.webp',
        url: 'im-sport?p=im&gId=sports',
        apiUrl: 'gameUrl?p=im&gId=sport',
    },
]
export const SUB_SPORT_LIST: ISport[] = [
    {
        type: SPORT_TYPE.A_SPORT,
        title: 'sportList.pp_sport',
        imgPc: '/assets/images/sport/sub-item/pp-sport.png',
        providerIcon: '/assets/images/sport/sub-item/p-pp.svg',
        url: 'pp-sport?p=pragmatic&gId=vpfh3',
        apiUrl: 'gameUrl?p=pragmatic&gId=vpfh3',
        loginRequired: false,
    },
    {
        type: SPORT_TYPE.K_SPORT,
        title: 'sportList.saba_sport',
        providerIcon: '/assets/images/sport/sub-item/p-saba.svg',
        imgPc: '/assets/images/sport/sub-item/betradar.png',
        url: 'the-thao-saba-virtual',
        apiUrl: 'athenaVsUrl',
        loginRequired: false,
    },
    {
        type: SPORT_TYPE.E_SPORT,
        title: 'sportList.im_sport',
        providerIcon: '/assets/images/sport/sub-item/p-im.svg',
        imgPc: '/assets/images/sport/sub-item/im-sport.png',
        url: 'im-sport-virtual',
        apiUrl: 'gameUrl?p=im&gId=virtualsport',
        loginRequired: false,
    },
    {
        type: SPORT_TYPE.IM_SPORT,
        title: 'sportList.k_sport',
        providerIcon: '/assets/images/sport/sub-item/p-k.svg',
        imgPc: '/assets/images/sport/sub-item/k-sport.png',
        url: 'the-thao-ksport?sportType=1_8',
        apiUrl: 'ksportsUrl?sportType=1_8',
        loginRequired: false,
    },
]

export const SPORT_LIST_INSURANCE: ISport[] = [
    {
        type: SPORT_TYPE.A_SPORT,
        title: 'sportList.aSport',
        imgPc: '/assets/images/insurance-sport/a_sport.png',
        imgMb: '/assets/images/insurance-sport/a_sport_mb.png',
        url: 'the-thao-asport',
        apiUrl: 'athenaUrl',
    },
    {
        type: SPORT_TYPE.K_SPORT,
        title: 'sportList.kSport',
        imgPc: '/assets/images/insurance-sport/k_sport.png',
        imgMb: '/assets/images/insurance-sport/k_sport_mb.png',
        url: 'the-thao-ksport',
        apiUrl: 'ksportsUrl',
    },
    {
        type: SPORT_TYPE.E_SPORT,
        title: 'sportList.eSport',
        imgPc: '/assets/images/insurance-sport/e_sport.png',
        imgMb: '/assets/images/insurance-sport/e_sport_mb.png',
        url: 'the-thao-esport',
        apiUrl: 'ssportUrl',
    },
    {
        type: SPORT_TYPE.L_SPORT,
        title: 'sportList.lSport',
        imgPc: '/assets/images/insurance-sport/l_sport.png',
        imgMb: '/assets/images/insurance-sport/l_sport_mb.png',
        url: 'the-thao-lsport',
        apiUrl: 'lsportUrl',
    },
]

export const SPORT_LIST_EVENTS = [
    {
        type: SPORT_TYPE.K_SPORT,
        title: 'sportList.k_sport',
        imgPc: '/assets/images/top-event/sports/k-sport-pc.png',
        imgMb: '/assets/images/top-event/sports/k-sport-mb.png',
        url: 'the-thao-ksport',
        apiUrl: 'ksportsUrl',
    },
    {
        type: SPORT_TYPE.A_SPORT,
        title: 'sportList.saba_sport',
        imgPc: '/assets/images/top-event/sports/saba-sport-pc.png',
        imgMb: '/assets/images/top-event/sports/saba-sport-mb.png',
        url: 'the-thao-saba',
        apiUrl: 'athenaUrl',
    },
    {
        type: SPORT_TYPE.E_SPORT,
        title: 'sportList.e_sport',
        imgPc: '/assets/images/top-event/sports/bti-sport-pc.png',
        imgMb: '/assets/images/top-event/sports/bti-sport-mb.png',
        url: 'bti',
        apiUrl: 'ssportUrl',
    },
    {
        type: SPORT_TYPE.IM_SPORT,
        title: 'sportList.im_sport',
        imgPc: '/assets/images/top-event/sports/im-sport-pc.png',
        imgMb: '/assets/images/top-event/sports/im-sport-mb.png',
        url: 'im-sport?p=im&gId=sports',
        apiUrl: 'gameUrl?p=im&gId=sport',
    },
    {
        type: SPORT_TYPE.A_SPORT,
        title: 'sportList.pp_sport',
        imgPc: '/assets/images/top-event/sports/pp-esport-pc.png',
        imgMb: '/assets/images/top-event/sports/pp-esport-mb.png',
        url: 'pp-sport?p=pragmatic&gId=vpfh3',
        apiUrl: 'gameUrl?p=pragmatic&gId=vpfh3',
        loginRequired: true,
    },
    {
        type: SPORT_TYPE.K_SPORT,
        title: 'sportList.saba_sport',
        imgPc: '/assets/images/top-event/sports/saba-esport-pc.png',
        imgMb: '/assets/images/top-event/sports/saba-esport-mb.png',
        url: 'the-thao-saba-virtual',
        apiUrl: 'athenaVsUrl',
        loginRequired: true,
    },
    {
        type: SPORT_TYPE.E_SPORT,
        title: 'sportList.im_sport',
        imgPc: '/assets/images/top-event/sports/k-esport-pc.png',
        imgMb: '/assets/images/top-event/sports/k-esport-mb.png',
        url: '/the-thao-ksport?sportType=1_8',
        apiUrl: 'ksportsUrl?sportType=1_8',
        loginRequired: true,
    },
    {
        type: SPORT_TYPE.IM_SPORT,
        title: 'sportList.im_sport',
        imgPc: '/assets/images/top-event/sports/im-esport-pc.png',
        imgMb: '/assets/images/top-event/sports/im-esport-mb.png',
        url: 'im-sport-virtual',
        apiUrl: 'gameUrl?p=im&gId=virtualsport',
        loginRequired: true,
    },
]
