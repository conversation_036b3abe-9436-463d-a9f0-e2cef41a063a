import { METHOD_DEPOSIT_MAP } from '~/constants/deposit'
import { METHOD_WITHDRAW_MAP } from '~/constants/withdraw'

export const useBehaviorRedirect = (
    action: 'DEPOSIT' | 'WITHDRAW',
    method: string
) => {
    const getUrlDeposit = (method: string) => {
        return (
            METHOD_DEPOSIT_MAP[method as keyof typeof METHOD_DEPOSIT_MAP] ||
            '/user/deposit?type=codepay'
        )
    }
    const getUrlWithdraw = (method: string) => {
        return (
            METHOD_WITHDRAW_MAP[method as keyof typeof METHOD_WITHDRAW_MAP] ||
            '/user/withdraw?type=bank'
        )
    }

    const link = computed(() =>
        action === 'DEPOSIT' ? getUrlDeposit(method) : getUrlWithdraw(method)
    )

    return { link }
}
