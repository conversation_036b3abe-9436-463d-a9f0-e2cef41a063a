import useCustomFetch from '~/composables/use-custom-fetch'
import { ENDPOINT } from '~/constants/api-endpoint'
import { OK } from '~/constants/api-status'
import type { ResMegaPower } from '~/interfaces/game'


export const useMegaPower = () => {

    const fetcher = useCustomFetch()
    const items = ref<ResMegaPower[]>([])
    const getMegaPower = async (p: string, gId: string) => {
        try {
            const { data, error } = await fetcher.get(
                ENDPOINT.GAME_URL,
                {
                    p: p,
                    gId: gId
                }
            )
            if (error.value) {
                throw error
            }
            if (data?.value?.status === OK) {
                items.value = data.value.data
            }
            return { data, error }
        } catch (error) {
            throw error
        }
    }

    return {
        items,
        getMegaPower
    }
}
