import { ref } from 'vue'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import isBetween from 'dayjs/plugin/isBetween'
import { useServerTime } from '~/composables/common/time-server'
import { ENDPOINT } from '~/constants/api-endpoint'
import { OK, ERROR } from '~/constants/api-status'
import useCustomFetch from '~/composables/use-custom-fetch'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import type { LuckyDrawData, LuckyDrawAward, LuckyDrawDataInfo, DayInfo } from '~/interfaces/event'
import { DATE_TIME_FORMAT } from '~/constants/common'
dayjs.extend(customParseFormat)
dayjs.extend(isBetween)



export const useEventDanhDeStore = defineStore(
    'eventDanhDeStore',
    () => {

    const useUserStoreInstance = useUserStore()
    const { user } = storeToRefs(useUserStoreInstance)
    const useModalStoreInstance = useModalStore()
    const { showBetSuccessModal, dateBetMB, luckyDrawDateBetApi } = storeToRefs(useModalStoreInstance)
    const useServerTimeInstance = useServerTime()
    const { getServerTime } = useServerTimeInstance

    const { $alert } = useNuxtApp()
    const fetcher = useCustomFetch()

    const serverTimestamp = ref()
    const luckyDrawResultsMB = ref<string>('')
    const luckyDraw = ref<LuckyDrawData[]>([])
    const luckyDrawInfo = ref<LuckyDrawDataInfo[]>([])
    const luckyDrawAward = ref<LuckyDrawAward[]>([])
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const isLoadingResult = ref(false)
    const isLoadingInfo = ref(false)
    const isLoadingLuckyDraw = ref(false)

    const selectedDayIndex = ref(0)
    const selectedDate = ref('')
    const sevenDays = ref<DayInfo[]>([])
    const swiperInstances = ref<{
        banner: any,
        winnersList: any
    }>({
        banner: null,
        winnersList: null
    })

    const initializeSelectedDate = async () => {
        await getTimeServer()
        const today = dayjs.unix(serverTimestamp.value).tz('Asia/Ho_Chi_Minh').format('DD/MM/YY')
        const todayIndex = sevenDays.value.findIndex(day => day.date === today)
        
        if (todayIndex !== -1) {
            selectedDayIndex.value = todayIndex
            selectedDate.value = today
        }
        
        try {
            await getLuckyDrawAward(today)
        } catch (error) {
            console.error('Error initializing lucky draw award:', error)
        }
    }

    const updateSevenDays = (timestamp: number) => {
        sevenDays.value = getSevenDays(timestamp)
        
        if (!selectedDate.value) {
            const today = dayjs.unix(timestamp).tz('Asia/Ho_Chi_Minh').format('DD/MM/YY')
            const todayIndex = sevenDays.value.findIndex(day => day.date === today)
            if (todayIndex !== -1) {
                selectedDayIndex.value = todayIndex
                selectedDate.value = today
            }
        }
    }

    const registerSwiperInstance = (type: 'banner' | 'winnersList', instance: any) => {
        swiperInstances.value[type] = instance
    }

    const handleDayClick = async (index: number, date: string) => {
        selectedDayIndex.value = index
        selectedDate.value = date
        
        Object.values(swiperInstances.value).forEach(swiper => {
            if (swiper && swiper.slideTo) {
                swiper.slideTo(index)
            }
        })
        
        try {
            await getLuckyDrawAward(date)
        } catch (error) {
            console.error('Error fetching lucky draw award:', error)
        }
    }

    const getTimeServer = async () => {
        const time = await getServerTime()
        if (time) {
            serverTimestamp.value = time
        }
        return time
    }

    const getCountdown = (timestamp: number) => {
        const now = dayjs.unix(timestamp).tz('Asia/Ho_Chi_Minh')
        const target = now.hour(18).minute(0).second(0).millisecond(0)
        const nextTarget = now.isAfter(target) ? target.add(1, 'day') : target
        const diff = nextTarget.diff(now)

        return {
            hours: String(Math.floor(diff / (1000 * 60 * 60))).padStart(2, '0'),
            minutes: String(Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))).padStart(2, '0'),
            seconds: String(Math.floor((diff % (1000 * 60)) / 1000)).padStart(2, '0'),
            totalMilliseconds: diff,
        }
    }

    const getSevenDays = (timestamp: number) => {
        const now = dayjs.unix(timestamp).tz('Asia/Ho_Chi_Minh')
        const target = now.hour(18).minute(0).second(0).millisecond(0)
        const isAfterTarget = now.isAfter(target)
        
        const startDate = isAfterTarget ? now.add(1, 'day') : now
        dateBetMB.value = startDate.format(DATE_TIME_FORMAT.DATE_FORMAT_DDMMYY)
        
        const days = []
        for (let i = 6; i >= 0; i--) {
            const date = startDate.subtract(i, 'day')
            days.push({
                date: date.format(DATE_TIME_FORMAT.DATE_FORMAT_DDMMYY),
                isActive: date.isSame(now, 'day')
            })
        }
        
        return days
    }

    const checkIsAfterTarget = (selectedDate: string) => {
        if (!serverTimestamp.value) return false
        const now = dayjs.unix(serverTimestamp.value).tz('Asia/Ho_Chi_Minh')
        const target = now.hour(18).minute(0).second(0).millisecond(0)
        const isAfterTarget = now.isAfter(target)
        const tomorrow = now.add(1, 'day').format(DATE_TIME_FORMAT.DATE_FORMAT_DDMMYY)
        
        if (selectedDate === now.format(DATE_TIME_FORMAT.DATE_FORMAT_DDMMYY)) {
            return isAfterTarget
        }
        if (selectedDate === tomorrow && isAfterTarget) {
            return false
        }
        return true
    }

    const splitResult = (result: string) => {
        if (!result) return Array(5).fill('-')
        return result.toString().split('').map(Number)
    }

    const getLuckyDrawStatus = async () => {
        try {
            isLoadingLuckyDraw.value = false
            const { data } = await fetcher.getPromotion(
                ENDPOINT.LUCKY_DRAW_STATUS, {}
            )
            if (data.value?.status === OK && data.value?.data) {
                luckyDraw.value = data.value?.data
            }
        } catch (error) {
            console.error(error)
        } finally {
            isLoadingLuckyDraw.value = true
        }
    }

    const getLuckyDrawInfo = async () => {
        try {
            isLoadingInfo.value = false
            const { data } = await fetcher.getPromotion(
                ENDPOINT.LUCKY_DRAW_INFO, {}
            )
            if (data.value?.status === OK && data.value?.data) {
                luckyDrawInfo.value = data.value?.data
            }
        } catch (error) {
            console.error(error)
        } finally {
            isLoadingInfo.value = true
        }
    }

    const getLuckyDrawAward = async (date: string) => {
        try {
            isLoading.value = false
            const { data } = await fetcher.getPromotion(
                ENDPOINT.LUCKY_DRAW_AWARD, {
                    date: dayjs(date, DATE_TIME_FORMAT.DATE_FORMAT_DDMMYY).format(DATE_TIME_FORMAT.DATE_FORMAT_YYYYMMDD),
                }
            )
            if (data.value?.status === OK && data.value?.data) {
                luckyDrawAward.value = data.value?.data?.list || []
                luckyDrawResultsMB.value = data.value?.data?.kqxs || ''
            }
        } catch (error) {
            console.error(error)
        } finally {
            isLoading.value = true
        }
    }

    const betLuckyDraw = async (lucky_number_1: string, lucky_number_2: string) => {
        try {
            if (isProcessing.value) { 
                return;
            }
            isProcessing.value = true;
            const { data } = await fetcher.postPromotion(
                ENDPOINT.BET_LUCKY_DRAW, {
                    lucky_number_1,
                    lucky_number_2,
                }
            )
            if (data.value?.status === ERROR && data.value?.message) {
                $alert(data.value.message)
                return false
            }
            if (data.value?.status === OK) {
                luckyDrawDateBetApi.value = data.value?.data?.apply_date
                showBetSuccessModal.value = true
                await getLuckyDrawStatus();
            }
        } catch (error) {
            $alert(error?._value?.data?.message || 'Vui lòng nạp tiền để tham gia chương trình.')            
            console.error(error)
        } finally {
            isProcessing.value = false
        }
    }

    const getLuckyNumbers = (luckyDraw: string | undefined) => {
        if (typeof luckyDraw !== 'string') return false
        return luckyDraw.split(',').slice(0, 3).map((num: string) => num.trim())
    }
    const getAmountLucky = (numberTxt: string, number: any) => {
        if (numberTxt) {
            const n1 = Number(numberTxt.replace(/,/g, ''))
            return NumberUtils.formatNumberWithoutDecimal(n1)
        }

        return NumberUtils.formatNumberWithoutDecimal(
            number || 0
        )
    }

    const isAccountPromotion = computed(() => {
        return user.value?.package_id && user.value.package_id !== 1
    })

    return {
        getTimeServer,
        serverTimestamp,
        getCountdown,
        getSevenDays,
        luckyDrawResultsMB,
        checkIsAfterTarget,
        splitResult,
        getLuckyDrawStatus,
        luckyDraw,
        getLuckyDrawAward,
        luckyDrawAward,
        isLoading,
        betLuckyDraw,
        getLuckyDrawInfo,
        luckyDrawInfo,
        getLuckyNumbers,
        isLoadingResult,
        isLoadingInfo,
        isLoadingLuckyDraw,
        getAmountLucky,
        isAccountPromotion,
        selectedDayIndex: readonly(selectedDayIndex),
        selectedDate: readonly(selectedDate),
        sevenDays: readonly(sevenDays),
        registerSwiperInstance,
        handleDayClick,
        initializeSelectedDate,
        updateSevenDays
    }
})
