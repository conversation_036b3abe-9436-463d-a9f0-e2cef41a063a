import { ref, onBeforeUnmount } from 'vue'

export const useScrollLock = () => {
  const isLocked = ref(false)
  const originalStyles = ref({
    overflow: '',
    position: '',
    width: ''
  })

  /**
   * Disable page scroll by setting body styles
   */
  const lockScroll = () => {
    if (isLocked.value) return

    // Store original styles before modifying
    originalStyles.value = {
      overflow: document.body.style.overflow,
      position: document.body.style.position,
      width: document.body.style.width
    }

    // Apply lock styles
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    
    isLocked.value = true
  }

  /**
   * Re-enable page scroll by restoring original body styles
   */
  const unlockScroll = () => {
    if (!isLocked.value) return

    // Restore original styles
    document.body.style.overflow = originalStyles.value.overflow
    document.body.style.position = originalStyles.value.position
    document.body.style.width = originalStyles.value.width
    
    isLocked.value = false
  }

  /**
   * Toggle scroll lock state
   */
  const toggleScrollLock = () => {
    if (isLocked.value) {
      unlockScroll()
    } else {
      lockScroll()
    }
  }

  // Cleanup: Ensure scroll is unlocked when component unmounts
  onBeforeUnmount(() => {
    if (isLocked.value) {
      unlockScroll()
    }
  })

  return {
    isLocked: readonly(isLocked),
    lockScroll,
    unlockScroll,
    toggleScrollLock
  }
}