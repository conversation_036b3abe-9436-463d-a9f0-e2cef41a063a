import { ENDPOINT } from '~/constants/api-endpoint'
import type { FavoritePayload } from './common/interface'
import type { IGame } from '~/interfaces/game'
import { storeToRefs } from 'pinia'
import { ERROR, OK } from '~/constants/api-status'
import { useDebounceFn } from '@vueuse/core'

export const useFavorite = () => {
    const { $alert } = useNuxtApp()
    const fetcher = useCustomFetch()
    const useUserStoreInstance = useUserStore()
    const { isLogged } = storeToRefs(useUserStoreInstance)
    const useModalStoreInstance = useModalStore()
    const { showLoginModal } = storeToRefs(useModalStoreInstance)

    const useGameStoreInstance = useGameStore()
    const { queryParams: queryParamsGame } = storeToRefs(useGameStoreInstance)
    const { getGamesPartner, fetchGames, getGames } = useGameStoreInstance
    const useLiveCasinoStoreInstance = useLiveCasinoStore()
    const { queryParams: queryParamsCasino } = storeToRefs(
        useLiveCasinoStoreInstance
    )
    const { getCasinoProvider, fetchGamesCasino, getGames: getGamesCasino } = useLiveCasinoStoreInstance

    const isLoading = ref(false)
    const favoriteGame = useDebounceFn(async (payload: FavoritePayload) => {
        try {
            if (!isLoading.value) {
                isLoading.value = true
                const endPoint =
                    payload.type === 'game'
                        ? ENDPOINT.FAVORITE_GAME
                        : ENDPOINT.FAVORITE_CASINO
                const { data } = await fetcher.post(endPoint, {
                    gId: payload.gId,
                    name: payload.name,
                    p: payload.p,
                })
                if (data.value?.status === ERROR && data.value?.message) {
                    $alert(data.value.message)
                    return false
                }
                if (data.value?.code === 200 && data.value?.status === OK) {
                    return true
                }
                return false
            }
            return false
        } catch (error) {
            console.error(error)
            return false
        } finally {
            isLoading.value = false
        }
    }, 500)
    const unFavoriteGame = useDebounceFn(async (payload: FavoritePayload) => {
        try {
            if (!isLoading.value) {
                isLoading.value = true
                const endPoint =
                    payload.type === 'game'
                        ? ENDPOINT.UN_FAVORITE_GAME
                        : ENDPOINT.UN_FAVORITE_CASINO
                const { data } = await fetcher.post(endPoint, {
                    gId: payload.gId,
                    name: payload.name,
                    p: payload.p,
                })
                if (data.value?.status === ERROR && data.value?.message) {
                    $alert(data.value.message)
                    return false
                }
                if (data.value?.code === 200 && data.value?.status === OK) {
                    return true
                }
                return false
            }
            return false
        } catch (error) {
            console.error(error)
            return false
        } finally {
            isLoading.value = false
        }
    }, 500)

    const updateGames = (games: IGame[], item: IGame, isBookMark: boolean) => {
        if (!games?.length) return
        const gameIndex = games.findIndex(
            (e) =>
                e.partner === item.partner &&
                e.partner_game_id === item.partner_game_id &&
                e.name === item.name
        )

        games[gameIndex].is_favorite = isBookMark
    }

    const handleBookMark = async (
        item: IGame,
        games: IGame[],
        type: string,
        isFavorite?: boolean
    ) => {
        if (!isLogged.value) {
            showLoginModal.value = true
            return
        }
        const payload = {
            p: item?.partner || '',
            gId: item?.partner_game_id || '',
            name: item?.name || '',
            type,
            ...(item?.table_id && { tId: item.table_id })
        }
        const bookMark = item.is_favorite
            ? await unFavoriteGame(payload)
            : await favoriteGame(payload)
        if (bookMark) {
            console.error('isFavorite', isFavorite)
            updateGames(games, item, !item.is_favorite)
            if (isFavorite) {
                const payload = {
                    limit: 20,
                    page: 1,
                    sort: 'favorite'
                }
                if (type !== 'game') {
                    getCasinoProvider('favorite')
                } else {
                    getGamesPartner('favorite')
                }
                if (type === 'game') {
                    queryParamsGame.value.partner = ''
                    queryParamsGame.value.page = 1
                    await getGames(ENDPOINT.GAME_SEARCH, payload)
                } else {
                    queryParamsCasino.value.partner = ''
                    queryParamsGame.value.page = 1
                    await getGamesCasino(payload)
                }
            }
        }
    }

    return {
        handleBookMark,
    }
}
