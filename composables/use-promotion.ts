import { ENDPOINT } from '~/constants/api-endpoint'
import { OK } from '~/constants/api-status'
import { PROMOTION_POPUP_METHOD } from '~/constants/common'
import type { ResParamsPromotionPopup } from '~/interfaces/promotion'
import { vipAccountUtils } from '~/utils'
import { useModalStore, useLiveChat } from '~/stores'

export const usePromotion = defineStore('promotionStore', () => {
    const { t } = useI18n()
    const { $alertHtmlConfirm, $alert, $notify } = useNuxtApp()
    const config = useRuntimeConfig()
    const UEFA_CHAMPION_LEAGUE_METHOD = (config.public.UEFA_CHAMPION_LEAGUE_METHOD as string) || ''
    const FIFA_CLUB_METHOD = (config.public.FIFA_CLUB_METHOD as string) || ''
    const useUserStoreInstance = useUserStore()
    const { user } = storeToRefs(useUserStoreInstance)
    const fetcher = useCustomFetch()
    const useModalStoreInstance = useModalStore()
    const useLiveChatInstance = useLiveChat()
    const { onClickLiveChat } = useLiveChatInstance
    const { queueModal } = useModalStoreInstance
    const modalInsuranceAmountId = ref(null)
    const modalInsuranceNotifyId = ref(null)
    const modalSVipId = ref(null)
    const modalCasinoRefundId = ref<number | null>(null)
    const modalFreeSpinId = ref<number | null>(null)
    const modalFreeTetSpinId = ref<number | null>(null)
    const modalRaceTopId = ref<number | null>(null)
    const modalWorldCupId = ref<number | null>(null)
    // const modalGoldenStarWarriosId = ref<number | null>(null)
    const insuranceAmount = ref(0)
    const eventStageReward = ref()
    const currentRoundReward = ref()
    const raceTopData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const clubWorldCupData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    // const goldenStarWarriosData = ref<ResParamsPromotionPopup>(
    //     {} as ResParamsPromotionPopup
    // )
    const svipData = ref({
        vip: 0,
        vip_info: '',
        vip_time: '',
        amount: 0,
        ranking: '',
        total_deposit_this_month: 0,
        rankingVip: '',
    })
    const affDataPopup = ref({})
    const uefaChampionDataPopup = ref({})
    const fifaClubDataPopup = ref({})
    const casinoRefundData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const freeSpinData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const freeSpinTetData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )

    const luckyDrawAward = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const luckyDrawAward1 = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const luckyDrawAward2 = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    
    const closePopup = async (id: string | number | null) => {

        if (id) {
            try {
                const { data, error } = await fetcher.postPromotion(
                    `${ENDPOINT.PROMOTION_CLOSE_POPUP}/${id}`,
                    {}
                )

                if (error.value) {
                    throw error
                }

                return { data, error }
            } catch (error) {
                console.error(error)
            }
        }
    }

    const closeInsuranceAmount = async () => {
        if (modalInsuranceAmountId.value) {
            try {
                await closePopup(modalInsuranceAmountId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalInsuranceAmountId.value = null
            }
        }
    }

    const closeInsuranceNotify = async () => {
        if (modalInsuranceNotifyId.value) {
            try {
                await closePopup(modalInsuranceNotifyId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalInsuranceNotifyId.value = null
            }
        }
    }

    const closeSvipModal = async () => {
        if (modalSVipId.value) {
            try {
                await closePopup(modalSVipId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalSVipId.value = null
            }
        }
    }
    const closeCasinoRefundModal = async () => {
        if (modalCasinoRefundId.value) {
            try {
                await closePopup(`${modalCasinoRefundId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalCasinoRefundId.value = null
            }
        }
    }
    const closeFreeSpinModal = async () => {
        if (modalFreeSpinId.value) {
            try {
                await closePopup(`${modalFreeSpinId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalFreeSpinId.value = null
            }
        }
    }
    const closeRaceTopModal = async () => {
        if (modalRaceTopId.value) {
            try {
                await closePopup(`${modalRaceTopId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalRaceTopId.value = null
            }
        }
    }
    const closeWorldCupModal = async () => {
        if (modalWorldCupId.value) {
            try {
                await closePopup(`${modalWorldCupId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalWorldCupId.value = null
            }
        }
    }


    // const closeGoldenStarWarriosModal = async () => {
    //     if (modalGoldenStarWarriosId.value) {
    //         try {
    //             await closePopup(`${modalGoldenStarWarriosId.value}`)
    //         } catch (error) {
    //             console.error(error)
    //         } finally {
    //             modalRaceTopId.value = null
    //         }
    //     }
    // }
    const closeFreeSpinTetModal = async () => {
        if (modalFreeTetSpinId.value) {
            try {
                await closePopup(`${modalFreeTetSpinId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalFreeTetSpinId.value = null
            }
        }
    }

    const getDataSVip = (data: any) => {
        const ranks = data?.vip_info?.split('_')
        const ranking = ranks?.length ? ranks[ranks.length - 1] : ''
        svipData.value = {
            ...data,
            ranking: ranking,
            amount: data?.amount || 0,
            rankingVip: vipAccountUtils.convertName(data?.vip),
        }
    }

    const getPromotionPopup = async () => {
        try {
            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.PROMOTION_POPUP,
                {}
            )

            if (error.value) {
                throw error
            }

            const popupRes = data?.value?.data

            if (data?.value?.status === OK && !!popupRes.length) {
                const eventSVip = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.SVIP
                )
                const popupAmountStatus = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT
                )
                const popupNotifyStatus = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY
                )
                casinoRefundData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method ===
                            PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND
                    ) || ({} as ResParamsPromotionPopup)
                freeSpinData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.FREE_SPIN
                    ) || ({} as ResParamsPromotionPopup)
                freeSpinTetData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.FREE_SPIN_TET
                    ) || ({} as ResParamsPromotionPopup)
                raceTopData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.RACE_TOP
                    ) || ({} as ResParamsPromotionPopup)
                clubWorldCupData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.CLUB_WORLD_CUP_TOP
                    ) || ({} as ResParamsPromotionPopup)
                luckyDrawAward1.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.LUCKY_DRAW_1
                    ) || ({} as ResParamsPromotionPopup)
                luckyDrawAward2.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.LUCKY_DRAW_2
                    ) || ({} as ResParamsPromotionPopup)
                // goldenStarWarriosData.value =
                //     popupRes.find(
                //         (e: { method: string }) =>
                //             e?.method === PROMOTION_POPUP_METHOD.GOLDEN_STAR_WARRIORS
                //     ) || ({} as ResParamsPromotionPopup)
                affDataPopup.value = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.ALL_IN_AFF
                )
                uefaChampionDataPopup.value = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === UEFA_CHAMPION_LEAGUE_METHOD
                )
                fifaClubDataPopup.value = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === FIFA_CLUB_METHOD
                )
                // Show modal insurance amount
                if (popupAmountStatus && popupAmountStatus.is_show) {
                    modalInsuranceAmountId.value = popupAmountStatus.id
                    insuranceAmount.value = +popupAmountStatus.amount
                    queueModal(PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT)
                }
                // Show modal insurance notify
                if (popupNotifyStatus && popupNotifyStatus.is_show) {
                    modalInsuranceNotifyId.value = popupNotifyStatus.id
                    queueModal(PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY)
                }

                // Show modal event SVIP
                if (
                    eventSVip?.is_show &&
                    eventSVip?.id &&
                    eventSVip?.vip_info &&
                    eventSVip?.vip
                ) {
                    modalSVipId.value = eventSVip.id
                    getDataSVip(eventSVip)
                    queueModal(PROMOTION_POPUP_METHOD.SVIP)
                }

                // Show modal event race top
                if (raceTopData.value?.is_show && raceTopData.value?.ranking) {
                    modalRaceTopId.value = raceTopData.value?.id
                    queueModal(PROMOTION_POPUP_METHOD.RACE_TOP)
                }
                if (clubWorldCupData.value?.is_show && clubWorldCupData.value?.ranking) {
                    modalWorldCupId.value = clubWorldCupData.value?.id
                    queueModal(PROMOTION_POPUP_METHOD.CLUB_WORLD_CUP_TOP)
                }

                // Show modal lucky draw award
                const luckyDrawChecks = [
                    { award: luckyDrawAward1.value, method: PROMOTION_POPUP_METHOD.LUCKY_DRAW_1 },
                    { award: luckyDrawAward2.value, method: PROMOTION_POPUP_METHOD.LUCKY_DRAW_2 }
                ]

                const activeAward = luckyDrawChecks.find(check => check.award && check.award?.is_show)
                if (activeAward) {
                    luckyDrawAward.value = activeAward.award
                    queueModal(activeAward.method)
                }

                // Show modal event golden star warrios
                // if (goldenStarWarriosData.value?.is_show && goldenStarWarriosData.value?.ranking) {
                //     modalGoldenStarWarriosId.value = goldenStarWarriosData.value?.id
                //     queueModal(PROMOTION_POPUP_METHOD.GOLDEN_STAR_WARRIORS as string)
                // }

                // Show modal casino refund
                if (casinoRefundData.value && casinoRefundData.value.is_show) {
                    modalCasinoRefundId.value = casinoRefundData.value.id
                    queueModal(PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND)
                }
                // Show modal free spin
                if (freeSpinData.value && freeSpinData.value.is_show) {
                    modalFreeSpinId.value = freeSpinData.value.id
                    queueModal(PROMOTION_POPUP_METHOD.FREE_SPIN)
                }
                // Show modal Aff Cup
                if (affDataPopup.value && affDataPopup.value.is_show) {
                    queueModal(PROMOTION_POPUP_METHOD.ALL_IN_AFF)
                }
                if (uefaChampionDataPopup.value && uefaChampionDataPopup.value.is_show) {
                    queueModal(UEFA_CHAMPION_LEAGUE_METHOD as string)
                }
                if (fifaClubDataPopup.value && fifaClubDataPopup.value.is_show) {
                    queueModal(FIFA_CLUB_METHOD as string)
                }
                if (freeSpinTetData.value && freeSpinTetData.value.is_show) {
                    modalFreeTetSpinId.value = freeSpinTetData.value.id
                    queueModal(PROMOTION_POPUP_METHOD.FREE_SPIN_TET)
                }
            }

            return { data, error }
        } catch (error) {
            throw error
        }
    }

    const cancelPromotionStandard = async () => {
        $alertHtmlConfirm(
            t('modal.alert.title.notify'),
            t('modal.alert.content.confirm_cancel_promotion'),
            {
                closeOnClickOutside: true,
                allowOutsideClick: true,
            }
        ).then(async (result) => {
            if (result.isDismissed) {
                return false
            }
            if (result.isConfirmed) {
                const { data } = await fetcher.post(
                    ENDPOINT.CANCEL_PROMOTION,
                    {}
                )
                if (data.value && data.value.status === OK) {
                    $alert(t('modal.alert.content.cancel_promotion_success'))
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                } else if (data.value && data.value.message) {
                    $notify({
                        title: t('modal.alert.content.confirm_cancel_promotion_title'),
                        html: `
                    <div class="text-sm mx-auto">${t(
                            'modal.alert.content.confirm_cancel_promotion_subtitle')}</div>
                            `,
                        cancelButtonFunc: async () => {
                            onClickLiveChat()
                        },
                        customClass: {
                            container: 'alert alert--type-1',
                        },
                        showCloseButton: true,
                        showCancelButton: true,
                        showConfirmButton: true,
                        confirmButtonText: t('modal.alert.content.confirm_cancel_promotion_cancel'),
                        cancelButtonText: t('modal.alert.content.confirm_cancel_promotion_livechat'),
                        closeOnClickOutside: true,
                        allowOutsideClick: true,
                    })
                    return false
                } else {
                    $alert(t('modal.alert.content.cancel_promotion_fail'))
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                    return false
                }
            }
        })
    }
    const cancelPromotion = async () => {
        if (user.value?.package_id && user.value.package_id !== 1) {
            $alertHtmlConfirm(
                t('modal.alert.title.notify'),
                t('modal.alert.content.confirm_cancel_promotion_checking'),
                {}
            ).then(async (result) => {
                if (result.isConfirmed) {
                    cancelPromotionStandard()
                }
            })
            return false
        }
        return true
    }

    const getDataStageReward = async () => {
        try {
            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.STAGE_REWARD,
                {}
            )

            if (error.value) {
                throw error
            }

            if (data?.value?.status === OK && !!data?.value?.data) {
                eventStageReward.value = data?.value?.data
            }
        } catch (error) {
            throw error
        }
    }

    const getStageRewardByRoundId = (roundId: string) => {
        // currentRoundReward.value = eventStageReward.value[roundId]
        const reward = {
            id: roundId,
            ...eventStageReward.value[roundId],
        }
        currentRoundReward.value = reward
        return reward
    }
    const closeAffModal = async (id: string) => {
        if (id) {
            try {
                await closePopup(id)
            } catch (error) {
                console.error(error)
            }
        }
    }
    const closeUefaModal = async (id: string) => {
        if (id) {
            try {
                await closePopup(id)
            } catch (error) {
                console.error(error)
            }
        }
    }
    const closeFifaClubModal = async (id: string) => {
        if (id) {
            try {
                await closePopup(id)
            } catch (error) {
                console.error(error)
            }
        }
    }

    return {
        modalInsuranceAmountId,
        modalInsuranceNotifyId,
        modalSVipId,
        modalCasinoRefundId,
        insuranceAmount,
        svipData,
        casinoRefundData,
        eventStageReward,
        currentRoundReward,
        raceTopData,
        clubWorldCupData,
        // goldenStarWarriosData,
        freeSpinData,
        affDataPopup,
        uefaChampionDataPopup,
        freeSpinTetData,
        luckyDrawAward,
        fifaClubDataPopup,
        luckyDrawAward1,
        luckyDrawAward2,
        closePopup,
        closeInsuranceAmount,
        closeInsuranceNotify,
        closeSvipModal,
        closeCasinoRefundModal,
        closeFreeSpinModal,
        closeRaceTopModal,
        closeWorldCupModal,
        getPromotionPopup,
        cancelPromotion,
        cancelPromotionStandard,
        getDataStageReward,
        getStageRewardByRoundId,
        closeAffModal,
        closeUefaModal,
        closeFreeSpinTetModal,
        closeFifaClubModal,
    }
})
