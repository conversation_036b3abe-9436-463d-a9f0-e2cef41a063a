import { ref, computed, onMounted, watch } from 'vue'
import dayjs from 'dayjs'

export function useCountdownEvent(startDay: string, endDay: string) {
    const startTime = ref(startDay)
    const endTime = ref(endDay)

    const cDays = ref(0)
    const cHours = ref(0)
    const cMinutes = ref(0)
    const cSeconds = ref(0)
    const isFinish = ref(false)
    const isStartDate = ref(false)
    const countdownTime = ref<string | null>(null)

    const isBeforeStartDate = computed(() =>
        dayjs().isBefore(startTime.value, 'second')
    )
    const isAfterStartDate = computed(() =>
        dayjs().isAfter(startTime.value, 'second')
    )
    const isAfterEndDate = computed(() =>
        dayjs().isAfter(endTime.value, 'second')
    )

    const formattedDays = computed(() =>
        cDays.value.toString().padStart(2, '0')
    )
    const formattedHours = computed(() =>
        cHours.value.toString().padStart(2, '0')
    )
    const formattedMinutes = computed(() =>
        cMinutes.value.toString().padStart(2, '0')
    )

    const countdownStartTime = (dateEnd: number) => {
        const count = setInterval(() => {
            const now = new Date(
                new Date().toLocaleString('en-US', {
                    timeZone: 'Asia/Ho_Chi_Minh',
                })
            ).getTime()

            const distance = dateEnd - now
            convertTimeCountdown(distance)
            isStartDate.value = dayjs().isAfter(startTime.value, 'second')
            if (distance <= 0) {
                if (isBeforeStartDate.value) {
                    countdownTime.value = endTime.value
                }

                if (isAfterStartDate.value) {
                    countdownTime.value = null
                    convertTimeCountdown(distance)
                    isFinish.value = true
                }

                clearInterval(count)
            }
        }, 1000)
    }

    const convertTimeCountdown = (distance: number) => {
        cDays.value = Math.max(0, Math.floor(distance / (1000 * 60 * 60 * 24)))
        cHours.value = Math.max(
            0,
            Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        )
        cMinutes.value = Math.max(
            0,
            Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
        )
        cSeconds.value = Math.max(
            0,
            Math.floor((distance % (1000 * 60)) / 1000)
        )
    }

    watch(
        () => countdownTime.value,
        (val) => {
            if (val) {
                countdownStartTime(dayjs(val).valueOf())
            }
        },
        { immediate: true }
    )

    onMounted(() => {
        isStartDate.value = !dayjs().isBefore(dayjs(startTime.value))
        const endDate = isStartDate.value
            ? new Date(endTime.value)
            : new Date(startTime.value)
        countdownTime.value = isBeforeStartDate.value
            ? startTime.value
            : endTime.value
        countdownStartTime(endDate.getTime())
    })

    return {
        cDays,
        cHours,
        cMinutes,
        cSeconds,
        isFinish,
        isStartDate,
        isBeforeStartDate,
        isAfterStartDate,
        isAfterEndDate,
        formattedDays,
        formattedHours,
        formattedMinutes,
    }
}
