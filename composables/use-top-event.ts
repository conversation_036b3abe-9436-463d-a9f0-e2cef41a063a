import { ref, computed, watch, onMounted } from 'vue'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import timezone from 'dayjs/plugin/timezone'
import { ENDPOINT } from '~/constants/api-endpoint'
import { OK } from '~/constants/api-status'
import { DATE_TIME_FORMAT } from '~/constants/common'
import dataRankings from '~/resources/olympic-2024/fake-data-ranking.json'

dayjs.extend(isBetween)
dayjs.extend(timezone)

export const useTopEvent = () => {
    const fetcher = useCustomFetch()
    const useUserStoreInstance = useUserStore()
    const { user } = storeToRefs(useUserStoreInstance)
    const {
        TOP_EVENT_START_DAY,
        TOP_EVENT_END_DAY,
        TOP_EVENT_ROUND_DURATION_DAYS,
    } = useRuntimeConfig().public
    const TOTAL_ROUNDS = 8
    const winLostUsers = ref<any>([])
    const isLoadingWinLost = ref(false)
    const activeFilter = ref(1)
    const topStake = ref(0)
    const currentDate = ref(
        dayjs().format(DATE_TIME_FORMAT.DATE_FORMAT_YYYYMMDD)
    )
    const startTimeEvent = ref(TOP_EVENT_START_DAY || '')
    const endTimeEvent = ref(TOP_EVENT_END_DAY || '')
    const userPosition = ref({})
    const isBeforeStartEvent = ref(
        dayjs().isBefore(TOP_EVENT_START_DAY, 'second')
    )
    const isBetweenDate = ref(
        dayjs().isBetween(
            dayjs(TOP_EVENT_START_DAY),
            dayjs(TOP_EVENT_END_DAY),
            'day',
            '[]'
        )
    )
    const isReCallApi = ref(false)

    const roundsData = {
        1: { ranking: dataRankings.round1, total: dataRankings?.total1 },
        2: { ranking: dataRankings.round2, total: dataRankings?.total2 },
        3: { ranking: dataRankings.round3, total: dataRankings?.total3 },
        4: { ranking: dataRankings.round4, total: dataRankings?.total4 },
        5: { ranking: dataRankings.round5, total: dataRankings?.total5 },
        6: { ranking: dataRankings.round6, total: dataRankings?.total6 },
        7: { ranking: dataRankings.round7, total: dataRankings?.total7 },
        8: { ranking: dataRankings.round8, total: dataRankings?.total8 },
    }

    const filterDropdownListDate = computed(() => {
        const rounds = []
        for (let i = 0; i < TOTAL_ROUNDS; i++) {
            const roundStart = dayjs(TOP_EVENT_START_DAY).add(
                i * Number(TOP_EVENT_ROUND_DURATION_DAYS),
                'day'
            )
            let roundEnd = roundStart
                .add(Number(TOP_EVENT_ROUND_DURATION_DAYS) - 1, 'day')
                .endOf('day')

            if (roundEnd.isAfter(TOP_EVENT_END_DAY)) {
                roundEnd = dayjs(TOP_EVENT_END_DAY)
            }

            rounds.push({
                number: i + 1,
                date: {
                    startTime: roundStart.format('DD/MM/YYYY HH:mm'),
                    endTime: roundEnd.format('DD/MM/YYYY HH:mm'),
                    startDate: roundStart.format('YYYY/MM/DD HH:mm'),
                    endDate: roundEnd.format('YYYY/MM/DD HH:mm'),
                },
                ranking: roundsData[i + 1]?.ranking || [],
                total: roundsData[i + 1]?.total || 0,
            })
        }
        return rounds
    })

    const filteredWeekendList = computed(() =>
        filterDropdownListDate.value.map((item) => {
            const start = dayjs(item.date.startDate).format(
                DATE_TIME_FORMAT.DATE_MONTH_FORMAT
            )
            const end = dayjs(item.date.endDate).format(
                DATE_TIME_FORMAT.DATE_MONTH_FORMAT
            )
            return {
                ...item,
                isEnd: dayjs(currentDate.value).isAfter(
                    item.date.endDate,
                    'second'
                ),
                title: `Đợt ${item.number}: ${start} - ${end}`,
            }
        })
    )

    const selectedFilterWeekend = computed(() =>
        filteredWeekendList.value.find(
            (item) => item.number === activeFilter.value
        )
    )

    const week = computed(() => selectedFilterWeekend.value?.number)
    const startTime = computed(
        () => selectedFilterWeekend.value?.date?.startDate || ''
    )
    const endTime = computed(
        () => selectedFilterWeekend.value?.date?.endDate || ''
    )

    const isChangeStartEndDate = computed(
        () => startTime.value && endTime.value
    )
    const isBeforeStartDate = computed(() =>
        dayjs(currentDate.value).isBefore(startTime.value, 'second')
    )
    const isBeforeEndDate = computed(() =>
        dayjs(currentDate.value).isBefore(endTime.value, 'second')
    )
    const isAfterEndDate = computed(() =>
        dayjs(currentDate.value).isAfter(endTime.value, 'second')
    )

    const fillUsersWinLost = computed(() => {
        if (winLostUsers.value.length === 0) {
            return []
        }
        if (winLostUsers.value.length < 20) {
            const length = 20 - winLostUsers.value.length
            const userWinLostRemains = Array(length)
                .fill(1)
                .map(() => ({
                    username: '---------------',
                    winlost: '---------',
                    game: '----------------',
                    is_null: true,
                }))
            return [...winLostUsers.value, ...userWinLostRemains]
        }
        return winLostUsers.value
    })

    watch(
        () => isChangeStartEndDate.value,
        () => {
            nextTick(() => {
                isLoadingWinLost.value = false
                winLostUsers.value = []
                getUsersWin()
                if (user.value) {
                    getUserPosition()
                }
            })
        },
        { immediate: true }
    )

    onMounted(() => {
        filterDropdownListDate.value.forEach((item) => {
            if (isBetweenDateFilter(item?.date?.startDate, item.date.endDate)) {
                activeFilter.value = item.number
                startTimeEvent.value = item.date.startDate
                endTimeEvent.value = item.date.endDate
                return true
            }
        })
        getUsersWin()
        if (user.value) {
            getUserPosition()
        }
    })

    const isBetweenDateFilter = (
        startDate: string,
        endDate: string
    ): boolean => {
        return dayjs().isBetween(startDate, dayjs(endDate), 'day', '[]')
    }

    const handleFilterWeekend = (data: any) => {
        activeFilter.value = data.number
        startTimeEvent.value = data.date.startDate
        endTimeEvent.value = data.date.endDate
    }

    const handleFinish = () => {
        isBeforeStartEvent.value = dayjs().isBefore(
            TOP_EVENT_START_DAY,
            'second'
        )
        isBetweenDate.value = dayjs().isBetween(
            dayjs(startTime.value),
            dayjs(endTime.value),
            'day',
            '[]'
        )
        getUsersWin()
        if (user.value) {
            getUserPosition()
        }
    }

    const getUserPosition = async () => {
        try {
            const payload = {
                startTime: startTime.value,
                endTime: endTime.value,
            }

            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.TOP_EURO_POSITION,
                payload
            )
            if (error.value) {
                throw error
            }

            if (data?.value?.status === OK && data?.value?.data) {
                userPosition.value = data.value.data || null
            }
        } catch (e) {
            console.error(e)
        }
    }

    const getUsersWin = async () => {
        try {
            isLoadingWinLost.value = true
            const payload = {
                startTime: startTime.value,
                endTime: endTime.value,
            }

            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.TOP_EURO_WIN,
                payload
            )
            if (error.value) {
                throw error
            }

            if (data?.value?.status === OK && data?.value?.data?.list?.length) {
                winLostUsers.value = data.value.data.list
                topStake.value = data?.value.data.total || ''
            } else {
                fakeDataRankings()
            }
        } catch (e) {
            console.error(e)
            fakeDataRankings()
        } finally {
            isLoadingWinLost.value = false
        }
    }

    const fakeDataRankings = () => {
        const indexDropdownDate = filterDropdownListDate.value.findIndex(
            (e) => e.date.startTime === startTime.value
        )
        if (indexDropdownDate >= 0) {
            winLostUsers.value =
                filterDropdownListDate.value[indexDropdownDate].ranking
            topStake.value =
                (filterDropdownListDate.value[indexDropdownDate].total || 0) *
                1000
        }
    }

    return {
        winLostUsers,
        isLoadingWinLost,
        activeFilter,
        topStake,
        currentDate,
        startTimeEvent,
        endTimeEvent,
        userPosition,
        isBeforeStartEvent,
        isBetweenDate,
        filterDropdownListDate,
        selectedFilterWeekend,
        week,
        startTime,
        endTime,
        isChangeStartEndDate,
        isBeforeStartDate,
        isBeforeEndDate,
        isAfterEndDate,
        filteredWeekendList,
        fillUsersWinLost,
        handleFilterWeekend,
        handleFinish,
        getUserPosition,
        getUsersWin,
    }
}
