import { ENDPOINT } from '~/constants/api-endpoint'
import { OK } from '~/constants/api-status'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
dayjs.extend(timezone)
dayjs.tz.setDefault('Asia/Ho_Chi_Minh')
export const useServerTime = defineStore('serverTime', () => {
    const fetcher = useCustomFetch()
    const serverTimestamp = ref()
    const serverDate = ref()
    const isLoading = ref(false)
    const getServerTime = async () => {
        try {
            isLoading.value = true
            const { data } = await fetcher.get(ENDPOINT.TIMES)
            if (data.value?.status === OK && data.value?.data) {
                serverDate.value = data.value.data
                serverTimestamp.value = dayjs.tz(data.value.data).unix()
                return dayjs.tz(data.value.data).unix()
            }
        } catch (error) {
            console.error(error)
        }
    }

    return {
        getServerTime,
        serverTimestamp,
        serverDate,
    }
})
