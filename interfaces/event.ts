export interface LuckyDrawData {
    deposit_required: number;
    deposit: number;
    lucky_number_1: boolean | number;
    lucky_number_2: boolean | number;
}

export interface LuckyDrawAward {
    username: string;
    type: string;
    lucky_draw : string;
    amount: number;
    amount_txt: string;
}

export interface LuckyDrawPopularItem {
    _id: boolean | number | string
    count: number
}

export interface LuckyDrawDataInfo {
    lucky_number_1: number
    lucky_number_1_txt: string
    lucky_number_2: number
    lucky_number_2_txt: string
    popular: LuckyDrawPopularItem[]
}

export interface DayInfo {
    date: string;
    isActive: boolean;
}

export interface BetFormValues {
    numberOne: string;
    numberTwo: string;
    numberThree: string;
    numberFour: string;
    numberFive: string;
}

export enum BetType {
  REGULAR = 1,
  LUCKY_NUMBER_1 = 2,
  LUCKY_NUMBER_2 = 3,
}