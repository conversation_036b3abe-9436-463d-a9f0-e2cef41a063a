<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_22003_105610)">
<circle cx="17.991" cy="17.9939" r="14.2222" fill="#AF4100"/>
</g>
<circle opacity="0.8" cx="17.9969" cy="17.9941" r="16" fill="url(#paint0_linear_22003_105610)"/>
<g filter="url(#filter1_i_22003_105610)">
<circle cx="18.0021" cy="17.9967" r="13.3333" fill="url(#paint1_radial_22003_105610)"/>
</g>
<g filter="url(#filter2_f_22003_105610)">
<ellipse cx="18.0017" cy="11.7744" rx="10.6667" ry="7.11111" fill="url(#paint2_radial_22003_105610)"/>
</g>
<g filter="url(#filter3_f_22003_105610)">
<ellipse cx="3.84516" cy="0.635254" rx="3.84516" ry="0.635254" transform="matrix(1 0 0 -1 14.445 6.82324)" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.0245 14.2243C23.3717 13.8771 23.3717 13.3141 23.0245 12.9669C22.6773 12.6197 22.1143 12.6197 21.7671 12.9669L17.9957 16.7384L14.2243 12.9671C13.8771 12.6199 13.3142 12.6199 12.967 12.9671C12.6198 13.3143 12.6198 13.8772 12.967 14.2244L16.7383 17.9958L12.9678 21.7662C12.6206 22.1134 12.6206 22.6764 12.9678 23.0236C13.315 23.3708 13.878 23.3708 14.2252 23.0236L17.9957 19.2531L21.7663 23.0237C22.1135 23.3709 22.6764 23.3709 23.0236 23.0237C23.3708 22.6765 23.3708 22.1136 23.0236 21.7664L19.253 17.9958L23.0245 14.2243Z" fill="#4E0000"/>
<defs>
<filter id="filter0_i_22003_105610" x="3.7688" y="3.77173" width="28.4444" height="29.4443" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.861751 0 0 0 0 0.351702 0 0 0 0 0.143007 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_22003_105610"/>
</filter>
<filter id="filter1_i_22003_105610" x="4.66879" y="3.66333" width="26.6667" height="27.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.84723 0 0 0 0 0.416699 0 0 0 0 0.157013 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_22003_105610"/>
</filter>
<filter id="filter2_f_22003_105610" x="6.33505" y="3.66333" width="23.3333" height="16.2222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_22003_105610"/>
</filter>
<filter id="filter3_f_22003_105610" x="12.145" y="3.25273" width="12.2903" height="5.87051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.15" result="effect1_foregroundBlur_22003_105610"/>
</filter>
<linearGradient id="paint0_linear_22003_105610" x1="11.9016" y1="6.94652" x2="27.5253" y2="30.1939" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEE4BF"/>
<stop offset="0.596347" stop-color="#E35C2A"/>
<stop offset="1" stop-color="#FFDFB1"/>
</linearGradient>
<radialGradient id="paint1_radial_22003_105610" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.0021 17.9967) rotate(90) scale(14.4444)">
<stop offset="0.55" stop-color="#FDC765"/>
<stop offset="1" stop-color="#FF8E5D"/>
</radialGradient>
<radialGradient id="paint2_radial_22003_105610" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.0017 4.93683) rotate(90) scale(13.9487 20.9231)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FDC765" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
