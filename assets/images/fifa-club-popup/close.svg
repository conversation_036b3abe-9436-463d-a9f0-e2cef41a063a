<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_22131_1713)">
<circle cx="15.9957" cy="15.9954" r="14.2222" fill="#AF4100"/>
</g>
<circle opacity="0.8" cx="16" cy="15.9961" r="16" fill="url(#paint0_linear_22131_1713)"/>
<g filter="url(#filter1_i_22131_1713)">
<circle cx="16.0052" cy="15.9969" r="13.3333" fill="url(#paint1_radial_22131_1713)"/>
</g>
<g filter="url(#filter2_f_22131_1713)">
<ellipse cx="16.0026" cy="9.77468" rx="10.6667" ry="7.11111" fill="url(#paint2_radial_22131_1713)"/>
</g>
<g filter="url(#filter3_f_22131_1713)">
<ellipse cx="3.84516" cy="0.635254" rx="3.84516" ry="0.635254" transform="matrix(1 0 0 -1 12.4453 4.82324)" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.2312 10.9655C11.884 10.6183 11.3211 10.6183 10.9739 10.9655C10.6267 11.3127 10.6267 11.8756 10.9739 12.2228L14.7445 15.9934L10.9729 19.765C10.6257 20.1122 10.6257 20.6751 10.9729 21.0224C11.3201 21.3696 11.883 21.3696 12.2302 21.0224L16.0018 17.2508L19.7732 21.0221C20.1204 21.3693 20.6833 21.3693 21.0305 21.0221C21.3777 20.6749 21.3777 20.112 21.0305 19.7648L17.2592 15.9934L21.0296 12.223C21.3768 11.8758 21.3768 11.3129 21.0296 10.9657C20.6824 10.6185 20.1194 10.6185 19.7722 10.9657L16.0018 14.7361L12.2312 10.9655Z" fill="#4E0000"/>
<defs>
<filter id="filter0_i_22131_1713" x="1.77344" y="1.77319" width="28.4445" height="29.4443" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.861751 0 0 0 0 0.351702 0 0 0 0 0.143007 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_22131_1713"/>
</filter>
<filter id="filter1_i_22131_1713" x="2.67188" y="1.66357" width="26.6666" height="27.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.84723 0 0 0 0 0.416699 0 0 0 0 0.157013 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_22131_1713"/>
</filter>
<filter id="filter2_f_22131_1713" x="4.33594" y="1.66357" width="23.3334" height="16.2222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_22131_1713"/>
</filter>
<filter id="filter3_f_22131_1713" x="10.1453" y="1.25273" width="12.2903" height="5.87051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.15" result="effect1_foregroundBlur_22131_1713"/>
</filter>
<linearGradient id="paint0_linear_22131_1713" x1="9.90476" y1="4.94847" x2="25.5284" y2="28.1958" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEE4BF"/>
<stop offset="0.596347" stop-color="#FFEAC1"/>
<stop offset="1" stop-color="#FFDFB1"/>
</linearGradient>
<radialGradient id="paint1_radial_22131_1713" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16.0052 15.9969) rotate(90) scale(14.4444)">
<stop offset="0.55" stop-color="#FDC765"/>
<stop offset="1" stop-color="#FF8E5D"/>
</radialGradient>
<radialGradient id="paint2_radial_22131_1713" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16.0026 2.93708) rotate(90) scale(13.9487 20.9231)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FDC765" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
