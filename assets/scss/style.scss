@import 'function';
@import 'base/animation';
@import 'base/effect';
@import 'base/alert';
@import 'base/button';
@import 'component/jackpot';
@import 'component/header-account';
@import 'component/menu-mobile';
@import 'component/page-top';
@import 'component/footer';
@import 'component/contact';
@import 'component/news';
@import 'variables/variables';
@import 'mixins';
@font-face {
    font-family: 'SVN-VT Redzone Classic';
    src: url('fonts/SVN-VTRedzone-Classic.woff2') format('woff2'),
    url('fonts/SVN-VTRedzone-Classic.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
.font-svn-vt {
    font-family: 'SVN-VT Redzone Classic';
}
html {
    @include tablet() {
        font-size: clamp(1px, 1.111vw, 16px);
    }
    @include mb() {
        font-size: 4.267vw;
    }
}
.base-image {
    position: relative;
    .v-lazy-image {
        object-fit: cover;
        transition: all 0.3s;
    }
}
body {
    font-family: $font-primary;
    font-size: 0.875rem;
    background: $bg-primary;
    color: $color-primary;
    line-height: 1.5;
    &.modal-open {
        // position: fixed;
        // left: 0;
        // width: 100%;
        // height: 100%;
        overflow: hidden;
    }
    &::-webkit-scrollbar-track {
        border-radius: 0;
        background: #5a5a5a;
    }
    &::-webkit-scrollbar {
        width: 5px;
        background-color: #5a5a5a;
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: linear-gradient(270deg, #a8a8a8, #a8a8a8);
        border: 1px solid #a8a8a8;
    }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type='number'] {
    -moz-appearance: textfield;
}
input:focus {
    outline-width: 0;
    outline: none;
    @apply ring-0;
}
.nuxt-icon svg {
    margin-bottom: 0 !important;
}
.font-primary {
    font-family: $font-primary;
}
.font-open {
    font-family: $font-open;
}
/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scroll::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scroll {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}
.desktop {
    display: initial !important;
}

.mobile {
    display: none !important;
}

.color-primary {
    color: $color-primary;
}

.text-medium {
    font-size: 20px;
    font-weight: 700;
    line-height: 40px;
    letter-spacing: 1.25px;
}

.modal-dialog {
    .modal-body {
        .noti-title {
            letter-spacing: 1px;
            line-height: 30px;
        }
    }
}

@media (max-width: 768px) {
    .desktop {
        display: none !important;
    }

    .mobile {
        display: initial !important;
    }
}

.section-portgame {
    @apply bg-[#1d1d1d] pb-5 lg:bg-[#161312];
    @media (max-width: 992px) {
        .container {
            @apply pl-0 pr-0;
            .section-wrapper__row {
                @apply pt-0;
            }
        }
    }
    div[id^='go_'] :is(iframe[name^='h5live-'], video[id^='h5live-go_']),
    div[id^='sunwin_'] :is(iframe[name^='h5live-'], video[id^='h5live-sunwin_']),
    div[id^='vingame_xd'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_xd']),
    div[id^='vingame_sb'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_sb']),
    div[id^='vingame_bacca'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_bacca']),
    div[id^='rik'] :is(iframe[name^='h5live-'], video[id^='h5live-rik']),
    div[id^='b52'] :is(iframe[name^='h5live-'], video[id^='h5live-b52']) {
        @apply -translate-x-1/2 #{!important};
    }

    iframe[name^='h5live-']{
       @apply top-1/2 left-1/2 #{!important};
    }

    div[id^='rik'] :is(iframe[name^='h5live-'], video[id^='h5live-rik']) {
        @apply -translate-y-[56%] scale-[1.4] #{!important};
    }

    div[id^='rik_vgmn_111'] :is(iframe[name^='h5live-'], video[id^='h5live-rik_vgmn_111']) {
        @apply -translate-y-[55%] scale-[1.4] #{!important};
    }

    div[id^='rik_vgmn_108'] :is(iframe[name^='h5live-'], video[id^='h5live-rik_vgmn_108']) {
        @apply -translate-y-[60%] scale-[1.5] #{!important};
    }

    div[id^='go_'] :is(iframe[name^='h5live-'], video[id^='h5live-go_']) {
        @apply -translate-y-[55%] scale-[1.4] #{!important};
    }

    div[id^='sunwin_'] :is(iframe[name^='h5live-'], video[id^='h5live-sunwin_']) {
        @apply -translate-y-[54%] scale-[1.5] #{!important};
    }

    div[id^='vingame_bc'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_bc']) {
        @apply -translate-x-[50%] -translate-y-[28%] scale-[2.1] #{!important};
    }

    div[id^='vingame_xd'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_xd']) {
        @apply -translate-y-[30%] scale-[1.8] #{!important};
    }

    div[id^='vingame_sb'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_sb']) {
        @apply -translate-y-[34%] scale-[2.2] #{!important};
    }

    div[id^='vingame_sb_77783'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_sb_77783']) {
        @apply -translate-y-[22%] scale-[2.2] #{!important};
    }

    div[id^='vingame_bacca'] :is(iframe[name^='h5live-'], video[id^='h5live-vingame_bacca']) {
        @apply -translate-y-[35%] scale-[1.9] #{!important};
    }

    div[id^='b52'] :is(iframe[name^='h5live-'], video[id^='h5live-b52']) {
        @apply -translate-y-[56%] scale-[1.4] #{!important};
    }

    div[id^='b52_vgmn_110'] :is(iframe[name^='h5live-'], video[id^='h5live-b52_vgmn_110']) {
        @apply -translate-y-[64.5%] scale-[1.6] #{!important};
    }

    div[id^='b52_vgmn_109'] :is(iframe[name^='h5live-'], video[id^='h5live-b52_vgmn_109']) {
        @apply -translate-y-[64%] scale-[1.6] #{!important};
    }

    div[id^='b52_vgmn_108'] :is(iframe[name^='h5live-'], video[id^='h5live-b52_vgmn_108']) {
        @apply -translate-y-[60.5%] scale-[1.5] #{!important};
    }

    div[id^='789club'] :is(iframe[name^='h5live-'], video[id^='h5live-789club']) {
        @apply -translate-x-[54%] -translate-y-[54%] scale-[1.5] #{!important};
    }

    div[id^='789club_G1X_306'] :is(iframe[name^='h5live-'], video[id^='h5live-789club_G1X_306']) {
        @apply -translate-x-[54%] -translate-y-[48%] scale-[1.9] #{!important};
    }
}
.wrapper-content {
    @apply p-4 pb-[1rem] lg:px-[1.9375rem] lg:py-8;
    &__filter-mobile {
        @apply mb-2 grid grid-cols-2 gap-2;
        @media (min-width: 1200px) {
            @apply hidden;
        }
    }
    &.has-sticky {
        .wrapper-content__data {
            @apply mt-[1.3125rem];
        }
    }
}
.wrapper-content__data {
    @apply relative z-0 min-h-[35vh] lg:min-h-[600px];
    > .row {
        @apply grid grid-cols-5 gap-x-6 gap-y-6;
        @media (max-width: 1414px) {
            @apply grid-cols-4 gap-4;
        }
        @media (max-width: 767px) {
            @apply grid-cols-2 gap-x-[0.6875rem] gap-y-4;
        }
    }
    .game__item__content {
        @apply relative mt-1;
        .game-name {
            @apply w-full max-w-[calc(100%_-_2.5rem)] cursor-pointer font-montserrat;
            .name-item {
                @apply truncate text-xs font-medium capitalize text-white md:max-w-[90%] lg:text-sm;
            }
            .partner-item {
                @apply mt-1 text-[0.625rem] font-normal capitalize leading-[16/12] text-z-platinum lg:text-xs;
            }
        }
        .favorite-item {
            @apply absolute right-0.5 top-0 z-[100] flex h-6 w-6 cursor-pointer items-center justify-center;
            .nuxt-icon {
                svg {
                    @apply h-full w-full;
                }
            }
        }
    }
    .hunt-money {
        @apply absolute left-1 top-1 flex max-w-[8rem] items-center gap-x-1 overflow-hidden rounded-[100px] bg-[#1D1D1D99] px-1.5 py-0.5 [backdrop-filter:blur(4px)] md:max-w-[8rem] lg:max-w-[9rem];

        img {
            @apply translate-y-[0.5px];
        }
        &__value {
            span {
                @apply flex translate-y-[0.5px] justify-center text-xs font-medium leading-[1.4] text-[#FFCB41] lg:text-sm;
            }
        }
    }
    .hunt-money-video {
        @apply absolute left-1 bottom-1.5 flex max-w-[8rem] items-center gap-x-1 overflow-hidden rounded-[100px] bg-[#1D1D1D99] px-1.5 py-0.5 [backdrop-filter:blur(4px)] md:max-w-[8rem] lg:max-w-[9rem];

        img {
            @apply translate-y-[0.5px];
        }
        &__value {
            span {
                @apply flex translate-y-[0.5px] justify-center text-xs font-medium leading-[1.4] text-[#FFCB41] lg:text-sm;
            }
        }
    }
    .effect-hover {
        @apply relative;
        .game__item__thumb {
            @apply relative;
        }
        .btn-play {
            @apply hidden;
        }
        &:hover {
            .game__item__thumb {
                &::before {
                    @apply absolute bottom-0 left-0 z-[1] h-full w-full rounded-xl lg:content-[''];
                    background: linear-gradient(
                        180deg,
                        rgba(20, 20, 20, 0) 0%,
                        #141414 100%
                    );
                }
                .btn-play {
                    @apply absolute bottom-2/4 left-2/4 z-10 flex -translate-x-2/4 translate-y-2/4 animate-[0.3s_ease-in-out_0s_scrollUp];
                }
            }
        }
    }
}
@keyframes scrollUp {
    0% {
        opacity: 0;
        bottom: 30%;
    }
    50% {
        opacity: 0.5;
        bottom: 40%;
    }
    100% {
        opacity: 1;
        bottom: 50%;
    }
}
.wrapper-content__data__loading {
    @apply mt-24 text-center;
    img {
        @apply m-auto w-[41px];
    }
}
.wrapper-content__data .game__item__thumb {
    @apply flex aspect-[166/115] w-full rounded-xl lg:aspect-[250/174];
    .base-image {
        @apply flex aspect-[166/115] size-full items-center justify-center  lg:aspect-[250/174];
        img {
            @apply rounded-xl object-contain transition-none;
        }
    }
}
.wrapper-content__data .game__item img {
    @apply w-full rounded-t-[6px];
}
.wrapper-content__data .game__item img[lazy='loading'] {
    @apply absolute left-1/2 top-1/2 m-auto w-[12%] -translate-x-1/2 -translate-y-1/2 transform;
}

@keyframes ping {
    0% {
        transform: scale(1);
    }
    12.5% {
        transform: scale(1.2);
    }
    
    25% {
        transform: scale(1);
    }
    27.5% {
        transform: scale(1);
    }
    50% {
        transform: scale(1);
    }
    62.5% {
        transform: scale(1);
    }
    75% {
        transform: scale(1);
    }
    87.5% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}
// mini game
.mini-game-icon {
    &.drag-draggable {
        @apply fixed;
    }
    .ping {
        animation: ping 5s cubic-bezier(.15, .55, .15, .50) infinite;

        &.game-text {
            animation-delay: 800ms;
        }
    }
    @apply fixed right-4 top-[80px] z-20 flex h-[46.14px] w-[80px] cursor-pointer items-center md:top-[60vh] lg:h-[57.65px] lg:w-[100px];
    @media (max-width: 992px) {
        height: 60px !important;
        inset: auto !important;
        bottom: 0 !important;
        right: 21% !important;
        width: 18% !important;
        opacity: 0;

        &.fake-minigame {
            @apply hidden;
        }
    }
}
.mini-game-ct {
    @media (max-width: 992px) {
        > div > div[draggable="true"] {
            height: 60px !important;
            inset: auto !important;
            bottom: 0 !important;
            right: 21% !important;
            width: 18% !important;
            opacity: 0;
        }
    }
}
.mini-game-icon__title {
    @apply absolute left-[10%] top-[12%] h-full w-4/5 animate-[0.5s_ease-in-out_0s_infinite_alternate_none_running_jackpot];
    text-shadow: rgb(189, 11, 28) 1px 1px 1px;
}

@keyframes jackpot {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    100% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
}

.bottom-bar {
    @apply fixed bottom-[-1px] left-0 z-[99] block w-full px-2.5 pb-[0.938rem] pt-[0.4375rem] shadow-[0_-4px_10px_rgba(29,29,29,0.3),inset_0_10px_20px_#2b2b2b,inset_0_-10px_20px_#2b2b2b];
    background: linear-gradient(180deg, #141212, #2b2727);
}

.main-nav-item {
    .label-new {
        @apply absolute right-[-5px] top-[-1px]  rounded-br-xl rounded-tl-xl bg-[#059e0a] bg-no-repeat px-2.5 py-[0.188rem] text-[0.5rem] leading-[1.125] text-white;
        font-family: Roboto, sans-serif;
        &.hot {
            @apply bg-[#EC0B0B];
        }
    }

    &:hover .sub-menu {
        @apply visible z-10 opacity-100;
    }
    .menu-item {
        @apply translate-y-px before:invisible before:absolute before:bottom-px before:left-2/4 before:z-[1] before:h-0.5 before:w-0 before:bg-white before:bg-none before:opacity-0 before:transition-all before:duration-[0.3s] before:content-[''];
    }
    .menu-item.active:before,
    .menu-item:hover:before {
        @apply visible left-0 w-full opacity-100;
    }
    .submenu-item.active span,
    .submenu-item:hover span {
        @apply translate-y-1;
    }
    .submenu-item.active .thumb,
    .submenu-item:hover .thumb {
        @apply scale-[1.13];
    }
    .submenu-item.active:after,
    .submenu-item:hover:after {
        @apply scale-105 opacity-100;
    }
    .submenu-item:after {
        @apply absolute left-auto top-[-1.875rem] z-[-1] h-[12.5rem] w-40 opacity-0 transition-all duration-[0.3s] content-[''];
        background: url(/assets/images/header/bg.webp) center no-repeat;
        background-size: cover;
    }
    .sub-menu {
        @apply z-[-1] backdrop-blur-[4.5px];
        background: linear-gradient(
            0deg,
            rgba(22, 14, 10, 0.93) -15.26%,
            rgba(39, 12, 18, 0.98) 70.21%,
            #522209 104.69%
        );
    }
}

.main-nav {
    background: linear-gradient(89.93deg, #f04735, #f7921e);
}
.header-ticky {
    @apply static top-0 z-[9999] w-full animate-[smoothScroll_0.3s_forwards] lg:fixed;
}

.filter-sticky {
    @apply fixed left-0 top-[3rem] z-[99] w-full bg-z-dynamic-black px-4 pt-4;
}

.header {
    @media (min-width: 993px) {
        @apply min-h-[7.625rem];
    }
    @apply shadow-[0_4px_4px_rgba(0,0,0,0.25)] lg:shadow-none;
}

.hot-match {
    @apply relative rounded-lg border border-double border-transparent bg-[url(/assets/images/home/<USER>/bg-hotmatch.webp)] bg-origin-border;
    background-clip: content-box, border-box;
    .info {
        @apply flex items-center justify-between rounded bg-[#414141] px-1.5 py-[1px] font-normal leading-[1.2] text-white lg:rounded-md lg:px-3 lg:py-[4.1px];
        span {
            @apply text-xs text-[#838383];
        }
        p {
            @apply text-xs font-medium text-[#2E9BFF];
        }
        .minus {
            @apply font-medium text-[#FF3434];
        }
    }
    .team-info {
        @include pc() {
            &:hover {
                .tool-tip {
                    display: block;
                }
            }
        }
        .tool-tip {
            @apply absolute top-full z-100 mt-2 hidden whitespace-nowrap rounded bg-white px-[15px] py-2 font-semibold text-z-red-dit;
            .arrow {
                @apply absolute bottom-[100%] left-1/2 block h-[0.4rem] w-[0.8rem] -translate-x-1/2 border-x-[0.4rem] border-b-[0.4rem] border-solid border-x-transparent border-b-white before:absolute before:content-[""];
            }
            &.top {
                @apply -top-full mt-3 -translate-y-full;
                .arrow {
                    @apply absolute left-[40%] top-[calc(100%_-_2px)] block h-[0.4rem] w-[0.8rem] -translate-x-1/2 border-x-[0.4rem] border-b-[0.4rem] border-solid border-x-transparent border-b-white before:absolute before:content-[""];
                    rotate: 180deg;
                }
            }
        }
    }

    .label-promotion {
        @apply absolute left-0 top-0 flex w-[9.375rem] items-center justify-center pl-4 text-[0.5rem] font-bold uppercase tracking-[.5px] text-white md:w-[11.875rem] md:pl-8 md:text-xs;
        animation: brightness 1s infinite;
        letter-spacing: 0.75px;
        box-shadow: 1px -3px 10px rgba(0, 0, 0, 0.2);
        background: linear-gradient(
            90.73deg,
            rgba(7, 165, 13, 0) 2.27%,
            #07a50d 26.54%,
            rgba(14, 132, 19, 0.44) 62.08%,
            rgba(14, 132, 19, 0) 96.87%
        );
        height: 23px;
    }
    .bg {
        background-color: #2d2d2d;
        background-size: cover;
    }
}

.total-jackpot {
    @apply relative w-full flex-shrink-0 rounded-[10px] bg-[url('/assets/images/home/<USER>/jackpot-mb.webp')] pt-[28.1%] md:h-[10.2rem] md:rounded-[32px] md:bg-[url('/assets/images/home/<USER>/jackpot-mb.webp')] md:pt-0 lg:h-[19.2rem] lg:w-[21.625rem] lg:rounded-[10px_0_0_10px] lg:bg-[url('/assets/images/home/<USER>/jackpot.webp')];
    background-repeat: no-repeat;
    background-size: cover;
    .title {
        @apply text-center text-lg font-semibold leading-[normal] text-white md:text-xl lg:mb-2;
        text-shadow: 0 4.178px 4.178px rgba(0, 0, 0, 0.25);
    }
    .number {
        @apply text-center text-2xl md:text-4xl lg:text-2xl lg:font-bold lg:leading-[1.3616];
        background: linear-gradient(
            180deg,
            #ffdc99 20.31%,
            #ffdc98 36.05%,
            #ffdc97 44.36%,
            #fdc352 58.02%,
            #edb547 79.69%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        strong {
            @apply ml-0 font-bold;
        }
    }
}

.list-winner {
    @apply rounded-[10px] pr-0 pt-3 lg:rounded-[0_10px_10px_0] lg:pb-5 lg:pl-6 lg:pr-2.5 lg:pt-5;
    background: linear-gradient(239.46deg, #2d1b5f 26.59%, #1c0b50 80.44%);
    @media (max-width: 991px) {
        background: none;
    }
    &__scroll {
        &::-webkit-scrollbar {
            @apply hidden;
        }
    }
    .list {
        @apply flex w-max gap-2 overflow-x-auto lg:block lg:w-auto lg:overflow-y-auto;
        li {
            @apply min-w-[295px] lg:min-w-[100%];
        }
        &::-webkit-scrollbar {
            border-radius: 2px;
            width: 5px;
            background: rgba(255, 255, 255, 0.13);
        }
        &::-webkit-scrollbar-track {
            border-radius: 2px;
            background: rgba(255, 255, 255, 0.13);
        }
        &::-webkit-scrollbar-thumb {
            background: #7d7498;
            border-radius: 18px;
            border: 1px solid transparent;
        }
    }
    .title {
        @apply mb-[0.875rem] text-base font-semibold uppercase leading-6 text-white lg:text-left lg:text-xl lg:font-semibold lg:normal-case;
        @include mba() {
            font-family: $font-primary;
        }
    }
}

.card-top-win {
    background-size: 100% 100%;
    .title {
        @apply mb-0 pt-0 text-center text-[11px] font-normal leading-7 sm:text-xl md:mb-11 md:pt-4 lg:mb-[12%] lg:pt-[4%];
        @include mb() {
            @apply text-[10px];
        }
    }
    .username {
        @apply text-[10px] font-semibold leading-[14px] tracking-[-0.01em] md:text-xl md:leading-[28.17px];
    }
    .game-title {
        @apply mt-1 max-w-[94%] text-xs font-normal leading-4 md:text-[22px] md:leading-[32.19px] lg:mt-2;
        text-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);
        word-break: break-word;
    }
    .btn-submit {
        @apply mt-2.5 h-6 w-full max-w-[74%] rounded-md border border-solid border-[#FFFFFF33] bg-[rgba(255,255,255,0.06)] text-xs font-semibold leading-4 md:h-12 md:text-lg md:leading-[32.19px] md:hover:border-[#ff4700] md:hover:bg-[#ff4700] lg:mt-8 lg:max-w-[250px] lg:rounded-lg lg:border-2;
        transition: all 0.3s ease-in;
    }
    .number {
        @apply mb-1 bg-clip-text text-center text-2xl font-bold md:mb-3;
        text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
        span {
            background: linear-gradient(
                180deg,
                #ffdc99 20.31%,
                #ffdc98 36.05%,
                #ffdc97 44.36%,
                #fdc352 58.02%,
                #edb547 79.69%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        strong {
            @apply font-bold;
        }
    }
    &.blue {
        @apply bg-[url('/assets/images/home/<USER>/blue-mb.webp')] md:bg-[url('/assets/images/home/<USER>/blue.webp')];
    }
    &.orange {
        @apply bg-[url('/assets/images/home/<USER>/orange-mb.webp')] md:bg-[url('/assets/images/home/<USER>/orange.webp')];
    }
}

.top-win-item {
    .name {
        @apply max-w-[112px] overflow-hidden text-ellipsis whitespace-nowrap text-xs font-normal leading-4 text-white;
    }
    .amount {
        @apply text-sm font-bold leading-5 text-[#FCF99A];
    }
    .status {
        @apply w-full px-0 text-[10px] font-normal leading-[14px] text-[#BEBEBE];
    }
}

.icon-plus {
    @apply translate-y-[-0.3125rem];
}

.top-ranking {
    text-shadow: 0 0.882px 0.882px rgba(0, 0, 0, 0.25), 0 0.4px 0 #505050;
}

.banner-game {
    @apply relative cursor-pointer overflow-hidden bg-[url('/assets/images/home/<USER>/game-card/banner.webp')];
    background-size: 100% 100%;
    &__action {
        @apply absolute bottom-[-10%] left-2/4 w-[19%] -translate-x-2/4 -translate-y-2/4 animate-[1s_zoomInDown_forwards] lg:w-[15%];
    }
    &__money {
        @apply absolute left-[60%] top-0 z-[2] w-[8%] animate-[1s_pulse_infinite];
    }
    &__model {
        @apply absolute bottom-0 left-[5%] w-[14%] animate-[1s_bounceInLeft_forwards];
    }
    &__face {
        @apply relative animate-[2s_headShake_infinite];
    }
    &__face-first {
        @apply absolute bottom-0 z-[1];
    }
    &__face-second {
        @apply absolute bottom-0 left-[60%] z-[2];
    }
    &__logo {
        @apply absolute left-2/4 top-[35%] w-[35%] -translate-x-2/4 -translate-y-2/4;
    }
    &__list {
        @apply absolute right-[6%] top-0 w-1/4 lg:w-1/4;
    }
    .game-list {
        &__inner {
            @apply grid grid-cols-[1fr_1fr] gap-2 md:gap-[1rem_1rem];
            animation-duration: 5s;
            animation-fill-mode: both;
            animation-iteration-count: infinite;
        }
        &__inner--first {
            -webkit-animation-name: animationFistList;
            animation-name: animationFistList;
        }
        &__inner--last {
            @apply -translate-y-full;
            -webkit-animation-name: animationLastList;
            animation-name: animationLastList;
        }
    }
}
.responsive-banner {
    @apply bg-[url('/assets/images/home/<USER>/game-card/banner-mobile.webp')] lg:bg-[url('/assets/images/home/<USER>/game-card/banner.webp')];
    background-size: 100% 100%;
    .banner-game {
        &__action {
            @apply bottom-[-10%] left-2/4 w-[30%] -translate-x-2/4 -translate-y-2/4 animate-[1s_zoomInDown_forwards] md:bottom-[-10%] lg:w-[15%];
        }
        &__money {
            @apply left-[70%] top-[5%] z-[2] w-[13%] animate-[1s_pulse_infinite] lg:left-[60%] lg:top-0 lg:w-[8%];
        }
        &__model {
            @apply bottom-0 left-[5%] w-full animate-[1s_bounceInLeft_forwards] lg:w-[14%];
        }

        &__face-first {
            @apply bottom-0 left-[-4%] z-[1] w-[24%] lg:left-0 lg:w-auto;
        }
        &__face-second {
            @apply bottom-0 left-[68%] z-[2] w-[24%] lg:left-[60%] lg:w-auto;
        }
        &__logo {
            @apply left-2/4 top-[30%] w-[60%] -translate-x-2/4 -translate-y-2/4 lg:top-[35%] lg:w-[30%];
        }
        &__list {
            @apply left-1/2 top-[76%] w-1/2 -translate-x-2/4 -translate-y-2/4 lg:left-auto lg:right-[6%] lg:top-[10%] lg:w-1/5 lg:translate-x-[unset] lg:translate-y-[unset];
        }
    }
    .game-list {
        &__inner {
            @apply grid grid-cols-[1fr_1fr_1fr_1fr] gap-2 md:gap-[1rem_1rem] lg:grid-cols-[1fr_1fr];
        }
    }
}

.home-hero-banner {
    .swiper-pagination-bullets.swiper-pagination-horizontal {
        @apply bottom-2 left-0 flex w-full max-w-[1440px] translate-x-0 justify-end pl-4 pr-4 lg:bottom-3 lg:ml-[6.667%]  xl:left-1/2 xl:ml-0 xl:-translate-x-1/2 xl:text-left;

        .swiper-pagination-bullet {
            @apply mx-0.5 my-0 inline-block size-2 rounded-[50px] p-0 lg:mx-[5px] lg:size-3;
            background: rgba(247, 114, 64, 0.4);
            opacity: 1;
            box-shadow: 0 16px 16px rgba(16, 14, 13, 0.25),
                0 8px 8px rgba(16, 14, 13, 0.25),
                0 4px 4px rgba(16, 14, 13, 0.25),
                0 2px 2px rgba(16, 14, 13, 0.25),
                0 1px 1px rgba(16, 14, 13, 0.25);
            &.swiper-pagination-bullet-active {
                @apply w-4 bg-[#f67240] lg:w-8;
            }
        }
    }
}
.banner-keno {
    @apply cursor-pointer bg-[url('/assets/images/home/<USER>/keno/banner-mobile.webp')] lg:bg-[url('/assets/images/home/<USER>/keno/banner.webp')];
    background-size: 100% 100%;
    &__model {
        @apply absolute bottom-0 left-0 w-[32%] lg:left-[16%] lg:w-[18%];
    }
    &__contain {
        @apply absolute left-1/4 top-0 h-full w-[74%] lg:left-[40%] lg:w-[37%];
    }
    &__thumb {
        @apply absolute left-0 top-0 h-full w-full;
    }
    &__action {
        @apply absolute bottom-0 left-2/4 w-6/12 -translate-x-2/4 -translate-y-2/4 animate-[2s_zoomInDown_forwards] cursor-pointer lg:bottom-[-6%];
    }
}

.banner-keno__jackpot {
    @apply absolute inset-x-0 top-[20.5%] bg-clip-text text-center font-bold tracking-[0.045em] lg:top-[22%];
    font-size: vw(28, 1920);
    @include mba() {
        font-size: vw(13);
    }
    span {
        font-size: vw(28, 1920);
        @include mba() {
            font-size: vw(13);
        }
    }
    background: linear-gradient(183.37deg, #ffffff 30.93%, #ffe7a6 92.45%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    text-shadow: 1px 0 0 #ffe7a6, 2px 1px 5px rgba(255, 255, 255, 0.08);
    filter: drop-shadow(0 2px 2px #443d3d);
}

.home-promotion-list {
    @apply grid grid-cols-[repeat(4,1fr)] gap-x-2.5 lg:min-h-[7.6875rem] lg:gap-x-4;
    @media screen and (max-width: 991px) {
        @apply -mr-[0.938rem] pr-[0.938rem];
        &::-webkit-scrollbar {
            @apply hidden;
        }
    }
    //&.has-promotion {
    //    @apply grid-cols-[repeat(2,1fr)] gap-y-3 md:grid-cols-[repeat(4,1fr)];
    //}
    //&.has-promotion .promotion-item .logo {
    //    @apply md:w-[5rem];
    //}
    .promotion-item {
        @apply relative cursor-pointer gap-x-[0.5rem] rounded-lg bg-[100%_100%] py-2 transition-all duration-[0.3s] lg:flex lg:min-h-[7.6875rem] lg:items-center lg:pl-6 lg:py-0;
        background: linear-gradient(154.72deg, #292929 9.56%, #1F1F1F 83.96%);
        @media screen and (min-width: 992px) {
            &:hover {
                background: linear-gradient(154.72deg, #3B3B3B 9.56%, #4C4C4C 83.96%);
            }
        }
        p {
            @apply mb-0 text-[0.625rem] font-bold leading-normal text-white lg:text-xs;
        }
        .logo {
            @apply mx-auto mb-1 object-contain transition-all duration-[0.3s] lg:mx-0 lg:mb-0 size-[3.75rem];
            transition: all 0.3s;
        }
        .icon {
            @apply mb-1 h-7 w-auto md:h-[38px] lg:mb-0 lg:mt-1 lg:h-[42px];
        }
        @media screen and (max-width: 991px) {
            @apply pt-0 w-[94px] pb-0.5;
            .content {
                @apply -mt-[9px];
            }
            p {
                @apply text-[10px] font-medium;
            }
            .logo {
                @apply size-[60px] -translate-y-2.5 mb-[1px];
            }
            .icon {
                @apply h-7 px-[14px] -mt-[3px] mb-0;
            }
        }
    }
}

.countdown {
    @apply inline-flex items-center justify-center whitespace-nowrap;
    padding: clamp(1px, 0.76655vw, 11px) clamp(1px, 1.0453vw, 15px);
    @apply border border-[hsla(0,0%,100%,0.11)] bg-[rgba(3,24,98,0.71)];
    backdrop-filter: blur(2.5px);
    @apply h-[clamp(1px,4.18118vw,60px)] w-fit rounded-[8px];

    @media (max-width: 768px) {
        @apply h-[9.06667vw] rounded-[1.06667vw] p-[1.06667vw_2.93333vw];
    }
    p {
        @apply mb-0 font-medium leading-[1.55];
        font-size: clamp(1px, 1.25436vw, 18px);
        @media (max-width: 768px) {
            @apply text-[2.13333vw];
        }
    }
    p.label {
        margin-left: clamp(1px, 0.27875vw, 4px);
    }
    .countdown-title {
        margin-right: clamp(1px, 0.69686vw, 10px);
    }

    .countdown-time {
        @apply flex items-center justify-center;
        gap: clamp(1px, 0.4878vw, 7px);

        @media (max-width: 768px) {
            @apply gap-[2.13333vw];
        }
    }

    .countdown-time__item {
        @apply flex items-center justify-center;
        gap: clamp(1px, 0.4878vw, 7px);

        @media (max-width: 768px) {
            @apply gap-[1.06667vw];
        }

        p {
            @apply text-[hsla(0,0%,100%,0.67)];
        }

        .time {
            @apply flex items-center justify-center;
            @apply rounded-[5px] bg-[rgba(0,0,0,0.79)] font-bold text-white;
            @apply leading-[1.25];
            text-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
            font-size: clamp(1px, 1.39373vw, 20px);
            width: clamp(1px, 2.64808vw, 38px);
            height: clamp(1px, 2.64808vw, 38px);

            @media (max-width: 768px) {
                @apply h-[6.66667vw] w-[6.66667vw] rounded-[1.06667vw] text-[3.2vw];
            }
        }
    }
}

.countdown-finished {
    @apply absolute bottom-[47.5%] left-[16%] inline-flex h-auto w-auto items-center justify-center whitespace-nowrap rounded-[6px] border-[1px] border-solid border-[hsla(0,0%,100%,0.11)] bg-[rgba(3,_24,_98,_0.71)] px-[30px] py-[11px] text-base font-extrabold uppercase leading-[20px] backdrop-blur-[2.5px] backdrop-filter;
}

.text-gold-gradient {
    @apply bg-gradient-to-r from-[#FDE9BF] to-[#E2BE73] bg-clip-text text-sm font-medium text-transparent lg:text-base lg:font-normal;
}

.text-shadow-custom {
    background: linear-gradient(0deg, #726349 -68.18%, #ffdea5 86.44%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    filter: drop-shadow(0px 0.84px 1.68px 0px #2d2825);
    position: relative;
}

.custom-number {
    background: linear-gradient(
        158deg,
        #ffec8f 0.21%,
        #fffdd2 43.18%,
        #ffe343 86.42%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #ffec8f;

    @apply whitespace-nowrap text-base md:text-[1.5rem] lg:text-[2rem] lg:leading-[1.2];
}

.hammer-icon {
    display: inline-block;
    transform-origin: center center;
    animation: hammer 0.6s ease-in-out infinite;
}
.hammer-icon2 {
    display: inline-block;
    transform-origin: center center;
    animation: hammer2 0.6s ease-in-out infinite;
}
@keyframes hammer {
    0% {
        transform: rotate(0);
    }
    20% {
        transform: rotate(-10deg);
    }
    40% {
        transform: rotate(15deg);
    }
    60% {
        transform: rotate(-10deg);
    }
    100% {
        transform: rotate(0);
    }
}
@keyframes hammer2 {
    0% {
        transform: rotate(0);
    }
    20% {
        transform: rotate(-5deg);
    }
    40% {
        transform: rotate(10deg);
    }
    60% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0);
    }
}

@keyframes circleLoading {
    0% {
        transform: rotate(0);
    }
    25% {
        transform: rotate(90deg);
    }
    50% {
        transform: rotate(180deg);
    }
    75% {
        transform: rotate(270deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
video[id^="h5live-"], iframe[id^="h5live-"], div[id^="middleView-"] {
    @apply pointer-events-none;
}
video[id^="h5live-vingame_"] {
    transform: translate(-48%, -25%) scaleX(1.6) scaleY(1.6) rotate(0deg) !important;
}
.livestream-home {
    div[id^="vingame"] {
        iframe[name^="h5live-"] {
            @apply left-1/2 top-1/2 #{!important};
            transform: translate(-48%, -25%) scaleX(1.6) scaleY(1.6) rotate(0deg) !important;
        }

        &.game__item__thumb[id^="vingame_xd_77786"] {
            iframe[name^="h5live-"] {
                transform: translate(-50%, -30%) scaleX(1.5) scaleY(1.5) rotate(0deg) !important;
            }
        }
    }

    video[id^="h5live-vingame_xd_77786"] {
        transform: translate(-50%, -30%) scaleX(1.5) scaleY(1.5) rotate(0deg) !important;
    }
}
div[id^="middleView-"] {
    @apply z-[6] #{!important};
    svg {
        @apply hidden;
    }
    &::before {
        @apply absolute inset-0 z-[1] h-8 w-8 content-[url('/assets/images/live-casino/ic-loading.svg')];
        animation: circleLoading 0.3s linear infinite;
    }
}
:root {
    color-scheme: light !important;
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
.no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}


/* reCAPTCHA Styles */
.grecaptcha-badge {
    visibility: hidden !important;
    opacity: 0 !important;
    display: none !important;
}
  
.recaptcha-v2-container {
    @apply my-2.5 flex min-h-fit items-center justify-center;
}
  
.g-recaptcha {
    margin: 10px 0;
    display: flex;
    justify-content: center;
}
  
.grecaptcha-badge .grecaptcha-badge-text {
    display: none !important;
}
  
.g-recaptcha > div {
    margin: 0 auto;
}
  
@media (max-width: 768px) {
    .g-recaptcha {
      transform: scale(0.9);
      transform-origin: center;
    }

    div.g-recaptcha-bubble-arrow + div {
        // &:has(> iframe[sandbox="allow-scripts allow-same-origin"]){
            &:not(&:has(> iframe[role="presentation"])) {
                @apply fixed top-0 left-0 z-[9999] #{!important};
            // }
        }
    }
}

