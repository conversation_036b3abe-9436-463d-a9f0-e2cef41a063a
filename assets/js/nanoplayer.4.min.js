/*!
 * @license
 * nanoStream H5Live Player 4.22.1
 * Build from Thu Sep 28 2023 10:46:14 GMT+0200 (GMT+02:00)
 * Copyright (c) 2014-2023 nanocosmos IT GmbH. All rights reserved.
 * http://www.nanocosmos.de
 * <EMAIL>
 *
 * LEGAL NOTICE:
 * This material is subject to the terms and conditions defined in
 * separate license conditions ('LICENSE.txt')
 * All information contained herein is, and remains the property
 * of nanocosmos GmbH and its suppliers if any. The intellectual and technical concepts
 * contained herein are proprietary to nanocosmos GmbH, and are protected by trade secret
 * or copyright law. Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained from nanocosmos.
 * All modifications will remain property of nanocosmos.
 */

/*!
 * EventEmitter v4.2.11 - git.io/ee
 * Unlicense - http://unlicense.org/
 * <PERSON> - http://oli.me.uk/
 * @preserve
 */

/*!
* @license
* SWFObject v2.3.20130521 <http://github.com/swfobject/swfobject> is released under the MIT License <http://www.opensource.org/licenses/mit-license.php>
*/

!function(){var e={4216:function(e,t,n){var r,i;r=[n(8478),n(8197),n(2459),n(2437),n(9470),n(7884),n(3293)],void 0===(i=function(e,t,n,r,i,a,o){"use strict";function s(i){var a=!1,s=i.source.group,u="https://bintu.nanocosmos.de";return new Promise((function(d,l){if(!i.source.bintu||i.source.entries&&i.source.entries.length||(i.source.entries=[{index:0,bintu:t.copy(i.source.bintu)}],i.source.h5live&&(i.source.entries[0].h5live=t.copy(i.source.h5live),delete i.source.h5live),i.source.hls&&(i.source.entries[0].hls=t.copy(i.source.hls),delete i.source.hls)),"object"==typeof s&&s.id)s.apiurl||(s.apiurl=u),new o(s.apiurl,null,null,"player").getStreamgroup(s.id).then(function(t,a){try{var o,u=JSON.parse(a.responseText),c=u.id,f=u.playout,E=f.rtmp,p=f.hls,h=f.h5live,g=u.state;if(E&&!E.length&&!h||h&&!h.length)l(new e(r.SETUP.BINTU_STREAM_NOT_LIVE.replace("$streamid$",c),n.SETUP.BINTU_STREAM_NOT_LIVE));else{i.source.entries=[];var b=i.source.entries;if(h&&h.length)for(var _=0;_<h.length;_+=1){if(b[_]=b[_]||{},p&&p.length){var T=p[_].url;b[_].hls=T}if(b[_].h5live=b[_].h5live||{},!b[_].h5live.server&&(b[_].h5live.server={}))for(o in h[_].server)Object.prototype.hasOwnProperty.call(h[_].server,o)&&(b[_].h5live.server[o]=h[_].server[o].replace("bintu-stream","bintu-h5live"));b[_].index=h[_].index||0;var y={width:0,height:0,bitrate:0,framerate:0},S={width:"width",height:"height",bitrate:"bitrate",framerate:"fps"};for(o in b[_].info=b[_].info||{},y)h[_].info&&Object.keys(h[_].info).length&&void 0!==h[_].info[S[o]]?b[_].info[o]=h[_].info[S[o]]:b[_].info[o]=y[o];h[_].label&&(b[_].label=h[_].label),b[_].h5live.rtmp={},b[_].h5live.rtmp.url=h[_].rtmp.url,b[_].h5live.rtmp.streamname=h[_].rtmp.streamname,s.security&&s.security.jwtoken&&"string"==typeof s.security.jwtoken&&(b[_].h5live.security=b[_].h5live.security||{},b[_].h5live.security.jwtoken=s.security.jwtoken)}}if(g&&(i.source.group.state=g),b.length){var A={high:0,"medium-high":Math.round(1*(b.length-1)/5),medium:Math.round(1*(b.length-1)/2),"medium-low":Math.round(4*(b.length-1)/5),low:b.length-1};Object.prototype.hasOwnProperty.call(i.source.group,"startQuality")&&"string"==typeof i.source.group.startQuality&&Object.prototype.hasOwnProperty.call(A,i.source.group.startQuality)&&(i.source.startIndex=A[i.source.group.startQuality])}(!i.source.options||i.source.options&&!i.source.options.adaption||i.source.options&&i.source.options.adaption&&!i.source.options.adaption.rule||i.source.options&&i.source.options.adaption&&i.source.options.adaption.rule&&"none"!==i.source.options.adaption.rule&&"deviationOfMean"!==i.source.options.adaption.rule)&&(i.source.options=i.source.options||{},i.source.options.adaption=i.source.options.adaption||{},i.source.options.adaption.rule="deviationOfMean2"),s=!1,d(i)}catch(e){m(e)}}.bind(this,void 0)).catch(m);else if(i.source.entries&&i.source.entries.length){for(var c=i.source.entries,f=0;f<c.length;f+=1){var E=c[f];if("object"==typeof E.bintu){if(!E.bintu.streamid){if(E.h5live&&(!E.h5live||E.h5live.rtmp))continue;l(new e(r.SETUP.BINTU_NO_STREAM_ID,n.SETUP.BINTU_NO_STREAM_ID))}E.bintu.apiurl||(E.bintu.apiurl=u),(a=a||{})[f]=new o(E.bintu.apiurl,null,null,"player"),E.bintu.streamid&&a[f].getStream(E.bintu.streamid).then(p.bind(this,f)).catch(m)}}function p(t,o){try{var s,u=JSON.parse(o.responseText),f=u.id,E=u.playout,p=E.rtmp,h=E.hls,g=E.h5live,b=u.state;if(p&&!p.length&&!g||g&&!g.length)l(new e(r.SETUP.BINTU_STREAM_NOT_LIVE.replace("$streamid$",f),n.SETUP.BINTU_STREAM_NOT_LIVE));else{if(h&&h.length){var _=h[0].url;c[t].hls=_}if(g&&g.length){if(c[t].h5live=c[t].h5live||{},!c[t].h5live.server&&(c[t].h5live.server={}))for(s in g[0].server)Object.prototype.hasOwnProperty.call(g[0].server,s)&&(c[t].h5live.server[s]=g[0].server[s].replace("bintu-stream","bintu-h5live"));c[t].h5live.rtmp={},c[t].h5live.rtmp.url=g[0].rtmp.url,c[t].h5live.rtmp.streamname=g[0].rtmp.streamname}else p&&p.length&&(c[t].h5live=i.source.h5live||{},c[t].h5live.rtmp={},c[t].h5live.rtmp.url=p[0].url,c[t].h5live.rtmp.streamname=p[0].streamname);b&&(c[t].bintu.state=b)}a[t]=!1;var T=!0;for(s in a)if(Object.prototype.hasOwnProperty.call(a,s)&&!1!==a[s]){T=!1;break}T&&d(i)}catch(e){m(e)}}a||d(i)}else d(i);function m(t){"onstatuserror"===t.error?l(new e(r.SETUP.BINTU_STATUS_ERROR.replace("$status$",t.request.status),n.SETUP.BINTU_STATUS_ERROR)):t.error?l(new e(t.error)):t.message&&l(new e(t.message))}}))}return{setup:s,updateSource:function(e){return s({source:e})},emptyConfig:i,validConfig:a}}.apply(t,r))||(e.exports=i)},9470:function(e,t){var n;void 0===(n=function(){return{create:function(){return{source:{bintu:{apiurl:"",streamid:""}}}}}}.apply(t,[]))||(e.exports=n)},7884:function(e,t){var n;void 0===(n=function(){return{create:function(){return{source:{bintu:{apiurl:"string",streamid:"string"}}}}}}.apply(t,[]))||(e.exports=n)},2459:function(e,t){var n;void 0===(n=function(){return Object.freeze({SETUP:{BINTU_STREAM_NOT_LIVE:5101,BINTU_NO_STREAM_ID:5102,BINTU_STATUS_ERROR:5103}})}.apply(t,[]))||(e.exports=n)},2437:function(e,t){var n;void 0===(n=function(){return Object.freeze({SETUP:{BINTU_STREAM_NOT_LIVE:"Could not find bintu stream with id $streamid$. The stream is not live.",BINTU_NO_STREAM_ID:"No bintu stream id passed.",BINTU_STATUS_ERROR:"The bintu service rejected with http status $status$."}})}.apply(t,[]))||(e.exports=n)},3293:function(e,t){var n;(function(){"use strict";var t=function(e,t,n,r){if(!(e.length>0&&("string"==typeof e||e instanceof String)))throw new Error('The param "apiUrl" must be of type "string" and also may not be empty string.');if(this.apiUrl=e,t&&("string"==typeof t||t instanceof String)&&(this.apiKey=t),n&&("string"==typeof n||n instanceof String)&&(this.playerKey=n),r&&("string"==typeof r||r instanceof String)){if("api"!==r&&"player"!==r)throw new Error('The param "keyMode" must be "api", "player" or undefined');this.keyMode=r}},r=t.prototype,i=function(e,t,n,r){this.method=e||"GET",this.url=t||"http://localhost:8088",this.header=n||{},this.async=r||!0,this.Send=function(e){return new Promise(function(t,n){var r=new XMLHttpRequest;for(var i in r.open(this.method,this.url,this.async),this.header)Object.prototype.hasOwnProperty.call(this.header,i)&&r.setRequestHeader(i,this.header[i]);if(r.onreadystatechange=function(){return 4===r.readyState&&200===r.status?t(r):4===r.readyState&&200!==r.status?n({error:"onstatuserror",request:r}):void 0},r.onabort=function(){return n({error:"onabort",request:r})},r.onerror=function(){return n({error:"onerror",request:r})},r.ontimeout=function(){return n({error:"ontimeout",request:r})},void 0!==e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){return e=null,n({error:"invalid json string",request:r})}if("object"==typeof e)try{e=JSON.stringify(e),r.send(e)}catch(e){return n({error:"invalid json object",request:r})}}else r.send()}.bind(this))}};r.apiKey=null,r.apiUrl=null,r.playerKey=null,r.keyMode="api",r.createStream=function(e,t,n){return new Promise(function(r,a){if(!this.apiUrl)return a({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey)return a({error:"no api key set",request:{responseText:"no response error"}});if("api"!==this.keyMode)return a({error:"wrong key mode set",request:{responseText:"no response error"}});var o=new i("POST",this.apiUrl+"/stream");o.header={Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey};var s={webrtc:!!e,transcoding:!!t};return"object"==typeof n&&"function"==typeof n.push&&n.length>0&&("string"==typeof n[0]||n[0]instanceof String)&&(s.tags=n),o.Send(s).then(r).catch(a)}.bind(this))},r.getStream=function(e){return new Promise(function(t,n){if(!this.apiUrl)return n({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey&&"api"===this.keyMode)return n({error:"no api key set",request:{responseText:"no response error"}});var r=new i("GET",this.apiUrl+"/stream/"+e);return r.header="api"===this.keyMode&&this.apiKey?{Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey}:{Accept:"application/json","Content-Type":"application/json; charset=utf-8"},r.Send().then(t).catch(n)}.bind(this))},r.getStreamgroup=function(e){return new Promise(function(t,n){if(!this.apiUrl)return n({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey&&"api"===this.keyMode)return n({error:"no api key set",request:{responseText:"no response error"}});var r=new i("GET",this.apiUrl+"/stream/"+e+"/group");return r.header="api"===this.keyMode&&this.apiKey?{Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey}:{Accept:"application/json","Content-Type":"application/json; charset=utf-8"},r.Send().then(t).catch(n)}.bind(this))},r.getStreams=function(e){return new Promise(function(n,r){if(!this.apiUrl)return r({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey&&"api"===this.keyMode)return r({error:"no api key set",request:{responseText:"no response error"}});if(!this.playerKey&&"player"===this.keyMode)return r({error:"no player key set",request:{responseText:"no response error"}});var a=this.apiUrl+"/stream";if(e instanceof t.StreamFilter){if(0===e.tags.length&&"player"===this.keyMode)return r({error:"no tags set",request:{responseText:"no response error"}});a+=e.getQueryString()}var o=new i("GET",a);return o.header="api"===this.keyMode?{Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey}:{Accept:"application/json","Content-Type":"application/json","X-BINTU-PLAYERKEY":this.playerKey},o.Send().then(n).catch(r)}.bind(this))},r.tagStream=function(e,t){return new Promise(function(n,r){if(!this.apiUrl)return r({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey)return r({error:"no api key set",request:{responseText:"no response error"}});if("api"!==this.keyMode)return r({error:"wrong key mode set",request:{responseText:"no response error"}});if(!e)return r({error:"no stream id set"});var a=new i("PUT",this.apiUrl+"/stream/"+e+"/tag");return a.header={Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey},"object"==typeof t&&"function"==typeof t.push&&t.length>0&&("string"==typeof t[0]||t[0]instanceof String)?a.Send({tags:t}).then(n).catch(r):a.Send().then(n).catch(r)}.bind(this))},r.pushStream=function(e,t){return new Promise(function(n,r){if(!this.apiUrl)return r({error:"no api url set",request:{responseText:"no response error"}});if(!this.apiKey)return r({error:"no api key set",request:{responseText:"no response error"}});if("api"!==this.keyMode)return r({error:"wrong key mode set",request:{responseText:"no response error"}});if(!e)return r({error:"no stream id set"});var a=new i("PUT",this.apiUrl+"/stream/"+e+"/push_url");return a.header={Accept:"application/json","Content-Type":"application/json","X-BINTU-APIKEY":this.apiKey},a.Send({url:t}).then(n).catch(r)}.bind(this))},r=(t.StreamFilter=function(){this.state=this.setState(t.StreamFilter.STATE.ALL),this.tags=[]}).prototype,t.StreamFilter.STATE=Object.create({LIVE:"live",CREATED:"created",ENDED:"ended",ALL:null}),r.setState=function(e){var n;for(var r in t.StreamFilter.STATE)t.StreamFilter.STATE[r]===e&&(n=t.StreamFilter.STATE[r]);if(void 0===n)throw new Error('The param "state" must be of type "Bintu.StreamFilter.STATE"');return this.state=n,this},r.addTag=function(e){if(!(e.length>0&&("string"==typeof e||e instanceof String)))throw new Error('The param "tag" must be of type "string" and also may not be empty string.');return this.tags.push(e),this.tags=this._reduceDuplicates(this.tags),this},r.addTags=function(e){if("object"!=typeof e||"function"!=typeof e.push||!(0===e.length||e.length>0&&("string"==typeof e[0]||e[0]instanceof String)))throw new Error('The param "tags" must be of type "string array"');return this.tags=this.tags.concat(e),this.tags=this._reduceDuplicates(this.tags),this},r.getQueryString=function(){var e="";if("object"==typeof this.tags&&"function"==typeof this.tags.push&&this.tags.length>0)for(var t=0;t<this.tags.length;t+=1)"string"==typeof this.tags[t]&&(e+=0===t?"?":"&",e+="tags[]="+this.tags[t]);return"string"==typeof this.state&&this.state.length>0&&(e+=-1===e.indexOf("?")?"?":"&",e+="state="+this.state),e},r._reduceDuplicates=function(e){if("object"!=typeof e||"function"!=typeof e.push||!(0===e.length||e.length>0&&("string"==typeof e[0]||e[0]instanceof String)))throw new Error('The param "tags" must be of type "string array"');return e.reduce((function(e,t){return e.indexOf(t)<0&&e.push(t),e}),[])},void 0===(n=function(){return t}.apply(this,[]))||(e.exports=n)}).call(this)},6965:function(e,t){var n;void 0===(n=function(){return{create:function(){return{playback:{autoplay:!0,automute:!1,muted:!1,metadata:!1,forceTech:"",flashplayer:"",videoId:"",keepConnection:!1,allowSafariHlsFallback:!1,crossOrigin:"not-set",mediaErrorRecoveries:3,metadataLowDelay:!0,reconnect:{minDelay:2,maxDelay:10,delaySteps:10,maxRetries:10},timeouts:{loading:20,buffering:20,connecting:5},latencyControlMode:"classic",faststart:!1},source:{hls:"",h5live:{server:{websocket:"",progressive:"",hls:""},token:"",rtmp:{url:"",streamname:""},security:{token:"",jwtoken:"",expires:"",options:"",tag:""},params:{}},bintu:{apiurl:"",streamid:"",state:""},startIndex:0,options:{switch:{},adaption:{}},entries:[],defaults:{},group:{apiurl:"",id:"",startQuality:"",state:"",security:{jwtoken:""}},general:{}},events:{onReady:void 0,onPlay:void 0,onPause:void 0,onLoading:void 0,onStartBuffering:void 0,onStopBuffering:void 0,onError:void 0,onMetaData:void 0,onStats:void 0,onMute:void 0,onUnmute:void 0,onVolumeChange:void 0,onServerInfo:void 0,onStreamInfo:void 0,onStreamInfoUpdate:void 0,onWarning:void 0,onDestroy:void 0,onUpdateSourceInit:void 0,onUpdateSourceSuccess:void 0,onUpdateSourceFail:void 0,onUpdateSourceAbort:void 0,onSwitchStreamInit:void 0,onSwitchStreamSuccess:void 0,onSwitchStreamFail:void 0,onSwitchStreamAbort:void 0,onActiveVideoElementChange:void 0},tweaks:{buffer:{min:void 0,start:void 0,max:void 0,target:void 0,limit:void 0},bufferDynamic:{offsetThreshold:void 0,offsetStep:void 0,cooldownTime:void 0}}}}}}.apply(t,[]))||(e.exports=n)},1493:function(e,t){var n;void 0===(n=function(){return{create:function(){return{index:0,label:"high",tag:"high res",info:{bitrate:0,width:0,height:0,framerate:0},hls:"",h5live:{server:{websocket:"",progressive:"",hls:""},token:"",rtmp:{url:"",streamname:""},params:{},security:{token:"",jwtoken:"",expires:"",options:"",tag:""}},bintu:{apiurl:"",streamid:"",state:""}}}}}.apply(t,[]))||(e.exports=n)},828:function(e,t){var n;void 0===(n=function(){return{create:function(){return{index:"number",label:"string",tag:"string",info:{bitrate:"number",width:"number",height:"number",framerate:"number"},hls:"string",h5live:{server:{websocket:"string",progressive:"string",hls:"string"},token:"string",rtmp:{url:"string",streamname:"string"},params:"object",security:{token:"string",jwtoken:"string",expires:"string",options:"string",tag:"string"}},bintu:{apiurl:"string",streamid:"string",state:"string"}}}}}.apply(t,[]))||(e.exports=n)},5055:function(e,t,n){var r,i;r=[n(6637)],void 0===(i=function(e){return{create:function(t,n,r){var i,a={};function o(e){a[e.name]&&a[e.name].forEach((function(t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i]);n.emit(t,r)}))}return i=[],r.forEach((function(e){i.push({type:e.from,listener:o}),a[e.from]||(a[e.from]=[]),a[e.from].push(e.to)})),e.add({target:t,listeners:i}),{destroy:function(){e.remove({target:t,listeners:i}),i=null,a=null}}}}}.apply(t,r))||(e.exports=i)},1969:function(e,t,n){var r,i;r=[n(9658)],void 0===(i=function(e){"use strict";function t(){this.version=void 0,this.type=void 0}var n=t.prototype=Object.create(e.prototype);return n.setup=function(){throw new Error('"setup" must be implemented or player not initialized')},n.destroy=function(){throw new Error('"destroy" must be implemented or player not initialized')},n.play=function(){throw new Error('"play" must be implemented or player not initialized by call setup')},n.pause=function(){throw new Error('"pause" must be implemented or player not initialized by call setup')},n.mute=function(){throw new Error('"mute" must be implemented or player not initialized by call setup')},n.unmute=function(){throw new Error('"unmute" must be implemented or player not initialized by call setup')},n.setVolume=function(){throw new Error('"setVolume" must be implemented or player not initialized by call setup')},n.updateSource=function(){throw new Error('"updateSource" must be implemented or player not initialized by call setup')},n.switchStream=function(){throw new Error('"switchStream" must be implemented or player not initialized by call setup')},n.setAdaption=function(){throw new Error('"setAdaption" must be implemented or player not initialized by call setup')},t.isSupported=function(){return!1},t.supportedTechniques=[],t}.apply(t,r))||(e.exports=i)},6637:function(e,t,n){var r,i;r=[n(939)],void 0===(i=function(e){"use strict";var t=["addEventListener","addListener","on"],n=["removeEventListener","removeListener","off"];function r(e,t){for(var n=0;n<t.length;++n)if("function"==typeof e[t[n]])return e[t[n]];return null}function i(i,a){!function(i){if(!i.target)throw new Error(e.NO_TARGET);if(!i.listeners||!i.listeners.length)throw new Error(e.NO_LISTENERS);if(!r(i.target,t)||!r(i.target,n))throw new Error(e.NOT_DISPATCHER);for(var a=0;a<i.listeners.length;++a)if(!i.listeners[a].type&&"string"!=typeof i.listeners[a].type||0!==i.listeners[a].type.length){if(!i.listeners[a].type)throw new Error(e.MISSING_TYPE.replace("%index%",a));if("function"!=typeof i.listeners[a].listener)throw new Error(e.MISSING_LISTENER.replace("%index%",a))}else i.listeners.splice(a,1),a--}(a),function(e,t,n){for(var r=0;r<n.length;++r)e.call(t,n[r].type,n[r].listener)}(r(a.target,i),a.target,a.listeners)}return{add:i.bind(null,t),remove:i.bind(null,n)}}.apply(t,r))||(e.exports=i)},8544:function(e,t,n){var r,i;r=[n(1933),n(8478),n(9775),n(182),n(3284),n(3778)],void 0===(i=function(e,t,n,r,i,a){return{create:function(e,o){return new Promise((function(s,u){var d=n;try{s(function(e,t){return new(0,e[0])(t)}(d=function(e,n){var i=[],o=e.filter((function(e){i=i.concat(e.supportedTechniques);var t=!1;return e.supportedTechniques.forEach((function(e){var r,i;!1===t&&(t=!n.playback.forceTech||(r=e,i=n.playback.forceTech,-1!==r.indexOf(i)||-1!==i.indexOf(r)))})),t}));if(!o.length)throw new t(r.FORCE_NOT_SUPPORTED.replace("$tech$",n.playback.forceTech),a.SETUP.FORCE_NOT_SUPPORTED);return o}(d=function(e,n){var o={};o[i.H5LIVE_WSS]="h5live",o[i.H5LIVE_HLS]="h5live",o[i.FLASH]="h5live",o[i.NATIVE]="hls";var s=[],u=e.filter((function(e){var t=!1;return e.supportedTechniques.forEach((function(e){s.push(o[e]),!1===t&&(t=Object.prototype.hasOwnProperty.call(n,"source")&&(Object.prototype.hasOwnProperty.call(n.source,o[e])||function(e,t){if(!Object.prototype.hasOwnProperty.call(e.source,"entries")||!e.source.entries.length)return!1;var n=!0;return e.source.entries.forEach((function(e){Object.prototype.hasOwnProperty.call(e,t)||(n=!1)})),n}(n,o[e])))})),t}));if(!u.length)throw new t(r.SOURCE_NOT_SUPPORTED,a.SETUP.SOURCE_NOT_SUPPORTED);return u}(d=function(e){var n=e.filter((function(e){return e.isSupported()}));if(!n.length)throw new t(r.CLIENT_NOT_SUPPORTED,a.SETUP.CLIENT_NOT_SUPPORTED);return n}(d),o),o),e))}catch(e){u(e)}}))},capabilities:(o=[],n.filter((function(e){return e.isSupported()})).forEach((function(e){o=o.concat(e.supportedTechniques)})),o)};var o}.apply(t,r))||(e.exports=i)},9775:function(e,t,n){var r,i;r=[n(1128),n(1128),n(8985),n(6722)],void 0===(i=function(){for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e}.apply(t,r))||(e.exports=i)},5810:function(e,t){var n;void 0===(n=function(){return{create:function(){return{source:{hls:"string",h5live:{server:{websocket:"string",progressive:"string",hls:"string"},token:"string",rtmp:{url:"string",streamname:"string"},params:"object",security:{token:"string",jwtoken:"string",expires:"string",options:"string",tag:"string"}},bintu:{apiurl:"string",streamid:"string",state:"string"},startIndex:"number",options:"object",entries:"object",defaults:"object",group:{apiurl:"string",id:"string",startQuality:"string",state:"string",security:{jwtoken:"string"}},general:"object"},playback:{autoplay:"boolean",automute:"boolean",muted:"boolean",forceTech:"string",metadata:"boolean",flashplayer:"string",videoId:"*",keepConnection:"boolean",allowSafariHlsFallback:"boolean",crossOrigin:"string",mediaErrorRecoveries:"number",metadataLowDelay:"boolean",reconnect:{minDelay:"number",maxDelay:"number",delaySteps:"number",maxRetries:"number"},timeouts:{loading:"number",buffering:"number",connecting:"number"},latencyControlMode:"string",faststart:"boolean"},events:{onReady:"function",onPlay:"function",onPause:"function",onLoading:"function",onStartBuffering:"function",onStopBuffering:"function",onError:"function",onMetaData:"function",onStats:"function",onMute:"function",onUnmute:"function",onVolumeChange:"function",onServerInfo:"function",onStreamInfo:"function",onStreamInfoUpdate:"function",onWarning:"function",onDestroy:"function",onUpdateSourceInit:"function",onUpdateSourceSuccess:"function",onUpdateSourceFail:"function",onUpdateSourceAbort:"function",onSwitchStreamInit:"function",onSwitchStreamSuccess:"function",onSwitchStreamFail:"function",onSwitchStreamAbort:"function",onActiveVideoElementChange:"function"},tweaks:{buffer:{max:"number",min:"number",start:"number",target:"number",limit:"number"},bufferDynamic:{offsetThreshold:"number",offsetStep:"number",cooldownTime:"number"}}}}}}.apply(t,[]))||(e.exports=n)},1313:function(e,t){var n;void 0===(n=function(){return Object.freeze({CONFIG_RTMP:"Configuration error. Could not create player, the rtmp configuration is missing or incomplete. Add an rtmp url and streamname to the configuration.",CONFIG_TOKEN:"Configuration error. Could not create player, with this configuration an security token is required. Add an token to the configuration.",CONFIG_WSS:"Configuration error. Could not create player, the websocket server configuration is missing.",CONFIG_HLS:"Configuration error. Could not create player, the hls server configuration is missing.",CONFIG_METADATA:"Configuration error. Could not create player, the websocket server configuration for metadata is missing.",CONFIG_SOURCE:"The players source configuration is malformed or missing, please check documentation for more information or contact us."})}.apply(t,[]))||(e.exports=n)},3778:function(e,t){var n;void 0===(n=function(){return Object.freeze({PLAYER:{NO_RTMP_URL_SET:1001,NO_SERVER_SET:1002,NOT_CONFIGURED:1003,NOT_PLAYING:1004,INTERACTION_REQUIRED:1005,BUFFER_CONFIG_INVALID:1006,PLAYBACK_SUSPENDED:1007,PLAYBACK_ERROR:1008,VISIBILITY_HIDDEN:1009,INVALID_ENTRY_INDEX:1010},STREAM:{NOT_FOUND:2001,MEDIA_NOT_AVAILABLE:2002,NOT_ENOUGH_DATA:2003,SOURCE_STOPPED:2004,METADATA_STILL_PROCESSING:2014,METADATA_NO_START_INDEX:2013,METADATA_INVALID_JSON:2012,METADATA_WRONG_INDEX:2011},MEDIA:{ABORTED:3001,DOWNLOAD_ERROR:3002,DECODE_ERROR:3003,NOT_SUPPORTED:3004,HLS_VIDEO_DECODE_ERROR:3005,MEDIA_SOURCE_ENDED:3100,HLS_BUFFER_UNDERRUN:3101,UNSPECIFIC_ERROR:3200},NETWORK:{COULD_NOT_ESTABLISH_CONNECTION:4001,CONNECTION_ERROR:4002,MAX_RECONNECTS_REACHED:4003,RECONNECTION_CONFIG_INVALID:4004,SOURCE_STREAM_STOPPED:4005,SOURCE_TIMEOUT:4006},SETUP:{EXCEPTION:5001,CLIENT_NOT_SUPPORTED:5002,FORCE_NOT_SUPPORTED:5003,SOURCE_NOT_SUPPORTED:5004,CONFIG_RTMP:5005,CONFIG_TOKEN:5006,CONFIG_WSS:5007,CONFIG_HLS:5008,CONFIG_METADATA:5009,EMBED_PLAYER:5010}})}.apply(t,[]))||(e.exports=n)},3890:function(e,t){var n;void 0===(n=function(){return Object.freeze({PLAYER:{PLAYBACK_ERROR:"Playback error.",VISIBILITY_HIDDEN:"Playback failed because the player was in visibility state 'hidden' at load start.",INVALID_ENTRY_INDEX:"The given stream entry index is not valid."},STREAM:{NOT_FOUND:"The requested stream can not be found.",MEDIA_NOT_AVAILABLE:"No media available.",NOT_ENOUGH_DATA:"Not enough media data received.",SOURCE_STOPPED:"The source stream has been stopped."},MEDIA:{ABORTED:"A fetching process of the media aborted by user.",DOWNLOAD_ERROR:"An error occurred when downloading media.",DECODE_ERROR:"An error occurred when decoding media.",NOT_SUPPORTED:"The received audio/video is not supported.",HLS_VIDEO_DECODE_ERROR:"An error occurred while hls playback when decoding video.",MEDIA_SOURCE_ENDED:"The media source extension changed the state to 'ended'.",HLS_BUFFER_UNDERRUN:"An error occurred while buffering on hls playback."},NETWORK:{SOURCE_STREAM_STOPPED:"The requested source stream has been stopped.",SOURCE_TIMEOUT:"The source request was aborted by timeout."}})}.apply(t,[]))||(e.exports=n)},9583:function(e,t){var n;void 0===(n=function(){return Object.freeze({READY:"Ready",ERROR:"Error",PAUSE:"Pause",LOADING:"Loading",START_BUFFERING:"StartBuffering",STOP_BUFFERING:"StopBuffering",PLAY:"Play",METADATA:"MetaData",STATS:"Stats",PLAYBACK_FINISHED:"PlaybackFinished",MEDIA:"Media",STREAM_INFO:"StreamInfo",STREAM_INFO_UPDATE:"StreamInfoUpdate",MUTE:"Mute",UNMUTE:"Unmute",VOLUME_CHANGE:"VolumeChange",STATE_CHANGE:"StateChange",WARNING:"Warning",DESTROY:"Destroy",UPDATE_SOURCE_INIT:"UpdateSourceInit",UPDATE_SOURCE_SUCCESS:"UpdateSourceSuccess",UPDATE_SOURCE_FAIL:"UpdateSourceFail",UPDATE_SOURCE_ABORT:"UpdateSourceAbort",SWITCH_STREAM_INIT:"SwitchStreamInit",SWITCH_STREAM_SUCCESS:"SwitchStreamSuccess",SWITCH_STREAM_FAIL:"SwitchStreamFail",SWITCH_STREAM_ABORT:"SwitchStreamAbort",SERVER_INFO:"ServerInfo",ACTIVE_VIDEO_ELEMENT_CHANGE:"ActiveVideoElementChange"})}.apply(t,[]))||(e.exports=n)},182:function(e,t){var n;void 0===(n=function(){return{FORCE_NOT_SUPPORTED:'The forced tech "$tech$" is not supported by your browser.',SOURCE_NOT_SUPPORTED:"The players source configuration is malformed or missing, please check documentation for more information or contact us.",CLIENT_NOT_SUPPORTED:"This browser does not fully support HTML5 and H5Live.            Supported are: Chrome >=54 (Windows, MacOSX, Android), Firefox >=48 (Windows, MacOSX, Android), Microsoft Edge (Windows), Microsoft Internet Explorer 11 (at least Windows 8), Safari (MacOSX & at least iOS 10)."}}.apply(t,[]))||(e.exports=n)},166:function(e,t){var n;void 0===(n=function(){return Object.freeze({EMBED_PLAYER:'Could not embed player. Please check the path to the player in the config param "playback.flashplayer" or copy to base directory.'})}.apply(t,[]))||(e.exports=n)},8656:function(e,t){var n;void 0===(n=function(){return Object.freeze({VIDEO_DISPLAY:{CONNECTION_ERROR:"connectionError",LOADING:"loading",PLAYING:"playing",REWINDING:"rewinding",BUFFERING:"buffering",STOPPED:"stopped",DISCONNECTED:"disconnected"},NET_STREAM:{PLAY:{RESET:"NetStream.Play.Reset",UNPUBLISH_NOTIFY:"NetStream.Play.UnpublishNotify",PUBLISH_NOTIFY:"NetStream.Play.PublishNotify",PLAY:"NetStream.Play.Start",STOP:"NetStream.Play.Stop",STREAM_NOT_FOUND:"NetStream.Play.StreamNotFound"}}})}.apply(t,[]))||(e.exports=n)},9474:function(e,t){var n;void 0===(n=function(){return Object.freeze({CLASSIC:"classic",FAST_ADAPTIVE:"fastadaptive",BALANCED_ADAPTIVE:"balancedadaptive"})}.apply(t,[]))||(e.exports=n)},939:function(e,t){var n;void 0===(n=function(){return Object.freeze({NO_TARGET:"No target in config",NO_LISTENERS:"No listeners in config",NOT_DISPATCHER:"Target is not an event dispatcher",MISSING_TYPE:"Missing type in listener #%index%",MISSING_LISTENER:"Missing/invalid listener in listener #%index%"})}.apply(t,[]))||(e.exports=n)},8730:function(e,t){var n;void 0===(n=function(){return Object.freeze({MP4:"video/mp4",MP4_MS:'video/mp4; codecs="avc1.42E01E, mp4a.40.2"',HLS:"application/vnd.apple.mpegURL"})}.apply(t,[]))||(e.exports=n)},8405:function(e,t){var n;void 0===(n=function(){return Object.freeze({UNINITIALIZED:1,CONNECTING:2,OPEN:3,CLOSING:4,CLOSED:5})}.apply(t,[]))||(e.exports=n)},5493:function(e,t){var n;void 0===(n=function(){return Object.freeze({NORMAL:"normal",BUFFER:"buffer",CONNECTION_CLOSE:"connectionclose",SERVER_NOT_FOUND:"servernotfound",STREAM_NOT_FOUND:"streamnotfound",INTERACTION_REQUIRED:"interactionrequired",PLAYBACK_SUSPENDED:"playbacksuspended",PLAYBACK_ERROR:"playbackerror",RECONNECTION_IMMINENT:"reconnectionimminent",DESTROY:"destroy",PLAYBACK_RESTART:"playbackrestart",VISIBILITY_HIDDEN:"visibilityhidden",NOT_ENOUGH_DATA:"notenoughdata",SOURCE_STREAM_STOPPED:"sourcestreamstopped"})}.apply(t,[]))||(e.exports=n)},492:function(e,t){var n;void 0===(n=function(){return Object.freeze({UNINITIALIZED:1,IDLE:2,READY:3,LOADING:4,PLAYING:5,PAUSED:6,BUFFERING:7,UNKNOWN:8,PLAYBACK_NOT_STARTED:9,PLAYBACK_SUSPENDED:10,PAUSING:11,PLAYBACK_ERROR:12,RECONNECTION_IMMINENT:13,CONNECTION_ERROR:14,DESTROYING:15,PLAYBACK_RESTARTING:16,VISIBILITY_HIDDEN:17,NOT_ENOUGH_DATA:18,SOURCE_STREAM_STOPPED:19})}.apply(t,[]))||(e.exports=n)},7605:function(e,t){var n;void 0===(n=function(){return Object.freeze({HAVE_VIDEO:"haveVideo",HAVE_AUDIO:"haveAudio",VIDEO_INFO:"videoInfo",AUDIO_INFO:"audioInfo",PREROLL_DURATION:"prerollDuration",MIME_TYPE:"mimeType"})}.apply(t,[]))||(e.exports=n)},3284:function(e,t){var n;void 0===(n=function(){return Object.freeze({FLASH:"flash",NATIVE:"hls.native",H5LIVE_WSS:"h5live",H5LIVE_HLS:"h5live.hls"})}.apply(t,[]))||(e.exports=n)},2682:function(e,t){var n;void 0===(n=function(){return{CORE:"4.22.1"}}.apply(t,[]))||(e.exports=n)},728:function(e,t){var n;void 0===(n=function(){return Object.freeze({1e3:"Normal closure, meaning that the purpose for which the connection was established has been fulfilled.",1001:'An endpoint is "going away", such as a server going down or a browser having navigated away from a page.',1002:"An endpoint is terminating the connection due to a protocol error",1003:"An endpoint is terminating the connection because it has received a type of data it cannot accept (e.g., an endpoint that understands only text data MAY send this if it receives a binary message).",1004:"Reserved. The specific meaning might be defined in the future.",1005:"No status code was actually present.",1006:"Maybe no network, wrong url or server down.",1007:"An endpoint is terminating the connection because it has received data within a message that was not consistent with the type of the message (e.g., non-UTF-8 [http://tools.ietf.org/html/rfc3629] data within a text message).",1008:'An endpoint is terminating the connection because it has received a message that "violates its policy". This reason is given either if there is no other sutible reason, or if there is a need to hide specific details about the policy.',1009:"An endpoint is terminating the connection because it has received a message that is too big for it to process.",1011:"A server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request.",1015:"The connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified).",4005:"The requested source stream has been stopped.",4400:"Bad request. Maybe stream parameters are missing or malformed.",4403:"Access denied. The authentication token is missing or invalid.",4500:"The connection has been rejected due an internal server error.",4503:"The requested service is currently unavailable.",4900:"The security service has been rejected due an internal server error.",4901:"The security service denied access. The authentication token is invalid.",4903:"The security service denied access. The url is expired or a token parameter is missing (expires, token, or options).",4904:"The security service can not be found."})}.apply(t,[]))||(e.exports=n)},9374:function(e,t,n){var r,i;r=[n(8197),n(908),n(4341)],void 0===(i=function(e,t,n){return{check:function(r){var i,a;switch(r&&r.source&&r.source.defaults&&r.source.defaults.service&&(i=r.source.defaults.service),i){case t.service:a=t.preset;break;case n.service:a=n.preset}if(a){var o,s,u=r.source;if(!u||!u.h5live||u.entries&&u.entries.length||(u.entries=[{h5live:e.copy(u.h5live)}]),u&&u.entries&&u.entries.length>0)for(var d=0;d<u.entries.length;d+=1)(o=u.entries[d].h5live)&&l(o.security&&"object"==typeof o.security&&null!==o.security?a.secure.source:a.unsecure.source,o)}function l(e,t){if(t.server&&"object"==typeof t.server&&null!==t.server)for(s in e.h5live.server)t.server[s]&&typeof t.server[s]==typeof e.h5live.server[s]&&""!==t.server[s]||(t.server[s]=e.h5live.server[s]);else t.server=e.h5live.server;t.rtmp&&(!t.rtmp.url||"string"!=typeof t.rtmp.url||t.rtmp.url.length<1)&&(t.rtmp.url=e.h5live.rtmp.url)}}}}.apply(t,r))||(e.exports=i)},908:function(e,t){var n;void 0===(n=function(){return{service:"bintu",preset:{unsecure:{source:{h5live:{server:{websocket:"wss://bintu-play.nanocosmos.de:443/h5live/stream/stream.mp4",hls:"https://bintu-play.nanocosmos.de:443/h5live/http/playlist.m3u8",progressive:"https://bintu-play.nanocosmos.de:443/h5live/http/stream.mp4"},rtmp:{url:"rtmp://localhost/play"}}}},secure:{source:{h5live:{server:{websocket:"wss://bintu-play.nanocosmos.de:443/h5live/authstream/stream.mp4",hls:"https://bintu-play.nanocosmos.de:443/h5live/authhttp/playlist.m3u8",progressive:"https://bintu-play.nanocosmos.de:443/h5live/authhttp/stream.mp4"},rtmp:{url:"rtmp://localhost/splay"}}}}}}}.apply(t,[]))||(e.exports=n)},4341:function(e,t){var n;void 0===(n=function(){return{service:"bintu-dev",preset:{unsecure:{source:{h5live:{server:{websocket:"wss://bintu-play-dev.nanocosmos.de:443/h5live/stream/stream.mp4",hls:"https://bintu-play-dev.nanocosmos.de:443/h5live/http/playlist.m3u8",progressive:"https://bintu-play-dev.nanocosmos.de:443/h5live/http/stream.mp4"},rtmp:{url:"rtmp://localhost/play"}}}},secure:{source:{h5live:{server:{websocket:"wss://bintu-play-dev.nanocosmos.de:443/h5live/authstream/stream.mp4",hls:"https://bintu-play-dev.nanocosmos.de:443/h5live/authhttp/playlist.m3u8",progressive:"https://bintu-play-dev.nanocosmos.de:443/h5live/authhttp/stream.mp4"},rtmp:{url:"rtmp://localhost/splay"}}}}}}}.apply(t,[]))||(e.exports=n)},1490:function(e,t,n){var r,i;r=[n(8197),n(9771)],void 0===(i=function(e,t){var n,r,i,a=[];return a.push(t),{apply:function(t){if(n=t.source,r=n.general,!n.h5live||n.entries&&n.entries.length||(n.entries=[{h5live:e.copy(n.h5live)}]),n&&n.entries&&n.entries.length>0&&r&&Object.keys(r).length)for(var o=0;o<n.entries.length;o+=1)if(i=n.entries[o])for(var s=0;s<a.length;s+=1)a[s].apply(r,i)}}}.apply(t,r))||(e.exports=i)},9771:function(e,t){var n;void 0===(n=function(){var e="serverDomain",t=["websocket","hls","progressive"];function n(e,t){var n,r,i;try{(n=e.split("://")).length>1&&(r=n[1].split("/")).length>1&&((i=r[0].split(":"))[0]=t,r[0]=i.join(":"),n[1]=r.join("/"),e=n.join("://"))}catch(e){}return e}return{apply:function(r,i){var a=function(t){var n=!1;return t&&"object"==typeof t&&Object.keys(t).length&&t[e]&&"string"==typeof t[e]&&t[e].length&&(n=t[e]),n}(r);if(a){if(i&&i.h5live&&i.h5live.server&&"object"==typeof i.h5live.server)for(var o=0;o<t.length;o+=1){var s=t[o];Object.prototype.hasOwnProperty.call(i.h5live.server,s)&&(i.h5live.server[s]=n(i.h5live.server[s],a))}i&&i.hls&&(i.hls=n(i.hls,a))}}}}.apply(t,[]))||(e.exports=n)},2556:function(e,t){var n;void 0===(n=function(){return{create:function(){return{metrics:{accountId:void 0,accountKey:void 0,userId:"",eventId:"",statsInterval:10,customField1:void 0,customField2:void 0,customField3:void 0,customField4:void 0,customField5:void 0,customField6:void 0,customField7:void 0,customField8:void 0,customField9:void 0,customField10:void 0}}}}}.apply(t,[]))||(e.exports=n)},7982:function(e,t){var n;void 0===(n=function(){return{create:function(){return{metrics:{accountId:"string",accountKey:"string",userId:"string",eventId:"string",statsInterval:"number",customField1:"*",customField2:"*",customField3:"*",customField4:"*",customField5:"*",customField6:"*",customField7:"*",customField8:"*",customField9:"*",customField10:"*"}}}}}.apply(t,[]))||(e.exports=n)},9914:function(e,t){var n;void 0===(n=function(){return Object.freeze({SETUP:{METRICS_NO_OBJECT:5201,METRICS_NOT_OBJECT:5202,METRICS_EMPTY_OBJECT:5203,METRICS_INDEX_INVALID:5204,METRICS_INDEX_INCORRECT:5205,METRICS_INDEX_RANGE:5206,METRICS_PROPERTY_INVALID:5207,METRICS_NO_CREDENTIALS:5208}})}.apply(t,[]))||(e.exports=n)},5999:function(e,t){var n;void 0===(n=function(){return Object.freeze({METRICS_NO_OBJECT:"Metrics configuration error. No metrics config object passed.",METRICS_NOT_OBJECT:"Metrics configuration error. Metrics config is not from type 'object'.",METRICS_EMPTY_OBJECT:"Metrics configuration error. Metrics config is empty.",METRICS_INDEX_INVALID:"Metrics configuration error. The custom property '$key$' has no valid index number, the range is 1 to 10 e.g. 'customField1'.",METRICS_INDEX_INCORRECT:"Metrics configuration error. The custom property '$key$' is not indexed correctly, the range is 1 to 10 e.g. 'customField1'.",METRICS_INDEX_RANGE:"Metrics configuration error. The custom property '$key$' has an index out of range, the range is 1 to 10 e.g. 'customField1'.",METRICS_PROPERTY_INVALID:"Metrics configuration error. The property '$key$' is not valid.",METRICS_NO_CREDENTIALS:"Metrics configuration error. No credentials passed."})}.apply(t,[]))||(e.exports=n)},27:function(e,t,n){var r,i;r=[n(7080)],void 0===(i=function(e){return{create:function(){var t,n=e.create(),r="statsAdaptive",i={},a={deviationOfMean:{buffer:{delay:{bufferDelaysArithmeticMeans:{calcArrayStats:!0},bufferDelaysStandardDeviations:{calcArrayStats:!0}}}},deviationOfMean2:{buffer:{delay:{bufferDelaysArithmeticMeans:{calcArrayStats:!0},bufferDelaysStandardDeviations:{calcArrayStats:!0}}}}};function o(e){if(Array.isArray(e)&&e.length){for(var t,n=e.length,r=e[0],i=e[0],a=e[0],o=1;o<n;o++)e[o]<r&&(r=e[o]),e[o]>i&&(i=e[o]),a+=e[o];return t=a/n,{min:isNaN(r)?r:r.toFixed(3),max:isNaN(i)?i:i.toFixed(3),avg:isNaN(t)?t:t.toFixed(3),count:n}}return{}}function s(e,t){for(var n in t){if(!e[n])return;Object.prototype.hasOwnProperty.call(t[n],"calcArrayStats")?i[n]=t[n].calcArrayStats?o(e[n]):e[n]:s(e[n],t[n])}}return{setStats:function(e){e&&e.data&&e.data.stats&&e.data.stats.adaptive&&(t=e.data.stats.adaptive)},getStatsFlat:function(){var e=t&&Object.keys(t).length?Object.keys(t)[0]:"";return Object.prototype.hasOwnProperty.call(a,e)?(s(t[e],a[e]),n.get(i,r,5)):n.get(t,r,5)}}}}}.apply(t,r))||(e.exports=i)},3932:function(e,t){var n;void 0===(n=function(){return{create:function(){var e=0,t=0,n=[],r={buffering_duration_current:0,buffering_duration_average:0,buffering_duration_total:0,buffering_count:0};return{start:function(){e=performance.now()},end:function(){t=performance.now()},measure:function(i,a){if(e&&t&&t>e){var o=Math.round(Math.abs(t-e))/1e3;if(t=e=0,o>=1&&o<=a.time_elapsed){for(var s in n.push(o),r.buffering_duration_current=o,r.buffering_duration_total+=o,r.buffering_count+=1,r.buffering_duration_average=Math.round(r.buffering_duration_total/r.buffering_count*1e3)/1e3,r)!/buffering_count/.test(s)&&r[s];return r}}return!1},reset:function(i,a){if(i&&a){for(t=e=0;n.length;)n.pop();var o={};for(var s in r)!/buffering_duration_current/.test(s)&&(o[s]=r[s]),r[s]=0;return o.time_played_total=a.time_played,o.time_elapsed_total=a.time_elapsed,o.time_buffering_total=o.buffering_duration_total,o}}}}}}.apply(t,[]))||(e.exports=n)},4815:function(e,t){var n;void 0===(n=function(){return{create:function(){return{measure:function(e){return{error_code:e.data.code,error_message:e.data.message}}}}}}.apply(t,[]))||(e.exports=n)},4245:function(e,t){var n;void 0===(n=function(){return{create:function(){return{measure:function(e){return{pause_reason:e.data.reason}}}}}}.apply(t,[]))||(e.exports=n)},2247:function(e,t){var n;void 0===(n=function(){return{create:function(){var e=0,t=0,n=0;function r(){e=performance.now()}function i(){t=performance.now(),n+=Math.abs(t-e),e=0,t=0}return{measure:function(e){r();var t,n={connecting:0,connected:0,firstFragmentReceived:0,firstFrameRendered:0,playable:0,playing:0};try{if(e.data&&e.data.stats&&"object"==typeof e.data.stats){var i=e.data.stats;for(t in n)Object.prototype.hasOwnProperty.call(i,t)&&"number"==typeof i[t]&&(n[t]=i[t])}}catch(e){}var a={};return[{start:"connecting",end:"connecting",measurement:"play_connecting",value:0},{start:"connecting",end:"connected",measurement:"play_connected",value:0},{start:"connected",end:"firstFragmentReceived",measurement:"play_first_fragment_received",value:0},{start:"firstFragmentReceived",end:"firstFrameRendered",measurement:"play_first_frame_rendered",value:0},{start:"firstFrameRendered",end:"playable",measurement:"play_playable",value:0},{start:"playable",end:"playing",measurement:"play_playing",value:0}].forEach((function(e){var t=n[e.start],r=n[e.end];r-t>=0&&(e.value=Math.floor(r-t)),a[e.measurement+"_relative"]=e.value,a[e.measurement+"_absolute"]=n[e.end]})),a.play_start_time_total=a.play_playing_absolute,a.play_faststart=e.data.faststart,a.play_preroll_duration=e.data.prerollDuration,a},setTimePlayingStateStart:r,setTimePlayingStateEnd:i,getTimePlayingStateTotal:function(){return e&&!t&&i(),Math.round(n/1e3*100)/100},resetTimePlayingStateSubtotal:function(){n=0}}}}}.apply(t,[]))||(e.exports=n)},7666:function(e,t,n){var r,i;r=[n(7080)],void 0===(i=function(e){return{create:function(){var t=e.create();return{measure:function(e){return t.get(e.data,"ready",10,!0)}}}}}.apply(t,r))||(e.exports=i)},2381:function(e,t,n){var r,i;r=[n(7080)],void 0===(i=function(e){return{create:function(){var t=e.create(),n={};return{measure:function(e){return{server_info_server_application_name:(n=e.data.serverInfo).serverApplicationName,server_info_server_application_version:n.serverApplicationVersion,server_info_hostname:n.hostname}},getFlat:function(){return t.get(n,"serverInfo",5)}}}}}.apply(t,r))||(e.exports=i)},1025:function(e,t,n){var r,i;r=[n(7080),n(8197)],void 0===(i=function(e,t){return{create:function(){var n=e.create(),r={SwitchStreamInit:{removeKeys:["source"]},SwitchStreamSuccess:{removeKeys:["source"],evaluateSwitch:!0},SwitchStreamAbort:{removeKeys:["source"]},SwitchStreamFail:{removeKeys:["source"]},UpdateSourceInit:{removeKeys:["source"]},UpdateSourcAbort:{removeKeys:["source"]},UpdateSourceFail:{removeKeys:["source"]}},i={up:0,down:0,direct:0,recover:0};return{handleEvent:function(e){var a,o;return!!function(e){return e&&e.data&&e.name}(e)&&(a=e.name,o=t.copy(e.data),Object.prototype.hasOwnProperty.call(r,a)&&Object.prototype.hasOwnProperty.call(r[a],"removeKeys")&&r[a].removeKeys.length&&function(e,t){for(var n=0;n<t.length;n++)Object.prototype.hasOwnProperty.call(e,t[n])&&delete e[t[n]]}(o,r[a].removeKeys),Object.prototype.hasOwnProperty.call(r,a)&&Object.prototype.hasOwnProperty.call(r[a],"evaluateSwitch")&&r[a].evaluateSwitch&&function(e){e&&e.type&&Object.keys(i).includes(e.type)&&(i[e.type]+=1)}(o),n.get(o,"e",5,!0))},getSwitchCountFlat:function(){var e=0,t={};return Object.keys(i).forEach((function(n){e+=i[n],t[n]=i[n]})),t.total=e,n.get(t,"switches",2,!0)},resetSwitchCount:function(){Object.keys(i).forEach((function(e){i[e]=0}))}}}}}.apply(t,r))||(e.exports=i)},9555:function(e,t){var n;void 0===(n=function(){return{create:function(e){var t=10;e&&!isNaN(e)&&(e=parseInt(e,10))>=5&&(t=e);var n=10*t,r=0,i=[],a=[],o=[],s=[],u=0,d={stats_buffer_average:0,stats_buffer_min:0,stats_buffer_max:0,stats_bitrate_average:0,stats_bitrate_min:0,stats_bitrate_max:0,stats_frame_rate_average:0,stats_frame_rate_min:0,stats_frame_rate_max:0,stats_playback_rate_average:0,stats_playback_rate_min:0,stats_playback_rate_max:0,stats_buffergoal_real:0,stats_buffergoal_base:0,stats_buffergoal_min:0,stats_buffergoal_max:0,stats_quality_dropped_video_frames:0,stats_quality_dropped_video_frames_current:0,stats_quality_dropped_video_frames_ratio:0,stats_quality_dropped_video_frames_level_max:0,stats_quality_corrupted_video_frames:0,stats_quality_corrupted_video_frames_current:0,stats_quality_creation_time:0,stats_quality_total_video_frames:0,stats_measure_count:0,stats_measure_samples:n};return{measure:function(e){var t={currentTime:0,playout:{start:0,end:0},buffer:{start:0,end:0,delay:{before:0,current:0,avg:0,min:0,max:0,deviation:0},update:{current:0,avg:0,min:0,max:0,deviation:0}},buffergoal:{real:0,base:0,min:0,max:0},quality:{droppedVideoFrames:0,droppedVideoFramesCurrent:0,droppedVideoFramesLevel:0,corruptedVideoFrames:0,corruptedVideoFramesCurrent:0,creationTime:0,totalVideoFrames:0},bitrate:{current:0,avg:0,min:0,max:0,deviation:0},framerate:{current:0,avg:0,min:0,max:0,deviation:0},playbackrate:{current:0,avg:0,min:0,max:0,deviation:0}};e.data.stats&&"object"==typeof e.data.stats&&(t=e.data.stats);try{for(r+=1,i.push(t.buffer.delay.current),a.push(t.bitrate.current),o.push(t.framerate.current),s.push(t.playbackrate.current);i.length>n;)i.shift();for(;a.length>n;)a.shift();for(;o.length>n;)o.shift();for(;s.length>n;)s.shift();if(u=Math.max(u,t.quality.droppedVideoFramesLevel),r%n==0){var l=JSON.parse(JSON.stringify(i)).sort((function(e,t){return e-t}),0);d.stats_buffer_average=Math.round(l.reduce((function(e,t){return e+t}),0)/l.length*1e3)/1e3,d.stats_buffer_min=Math.round(1e3*l[0])/1e3,d.stats_buffer_max=Math.round(1e3*l[l.length-1])/1e3;var c=JSON.parse(JSON.stringify(a)).sort((function(e,t){return e-t}),0);d.stats_bitrate_average=Math.round(c.reduce((function(e,t){return e+t}),0)/c.length/1e3),d.stats_bitrate_min=Math.round(c[0]/1e3),d.stats_bitrate_max=Math.round(c[l.length-1]/1e3);var f=JSON.parse(JSON.stringify(o)).sort((function(e,t){return e-t}),0);d.stats_frame_rate_average=Math.round(f.reduce((function(e,t){return e+t}),0)/f.length*1e3)/1e3,d.stats_frame_rate_min=f[0],d.stats_frame_rate_max=f[f.length-1];var E=JSON.parse(JSON.stringify(s)).sort((function(e,t){return e-t}),0);d.stats_playback_rate_average=Math.round(E.reduce((function(e,t){return e+t}),0)/E.length*1e3)/1e3,d.stats_playback_rate_min=E[0],d.stats_playback_rate_max=E[E.length-1],d.stats_buffergoal_real=t.buffergoal.real,d.stats_buffergoal_base=t.buffergoal.base,d.stats_buffergoal_min=t.buffergoal.min,d.stats_buffergoal_max=t.buffergoal.max,d.stats_measure_count+=1;var p=Math.round((t.quality.droppedVideoFrames-d.stats_quality_dropped_video_frames)/(t.quality.totalVideoFrames-d.stats_quality_total_video_frames)*100)/100;return d.stats_quality_dropped_video_frames=t.quality.droppedVideoFrames,d.stats_quality_dropped_video_frames_current=t.quality.droppedVideoFramesCurrent,d.stats_quality_dropped_video_frames_ratio=p,d.stats_quality_dropped_video_frames_level_max=u,d.stats_quality_corrupted_video_frames=t.quality.corruptedVideoFrames,d.stats_quality_corrupted_video_frames_current=t.quality.corruptedVideoFramesCurrent,d.stats_quality_creation_time=t.quality.creationTime,d.stats_quality_total_video_frames=t.quality.totalVideoFrames,u=0,d}return!1}catch(e){return!1}},reset:function(){for(r=0;i.length;)i.shift();for(;a.length;)a.shift();for(;o.length;)o.shift();for(var e in d)"stats_measure_samples"!==e&&(d[e]=0)}}}}}.apply(t,[]))||(e.exports=n)},4402:function(e,t,n){var r,i;r=[n(7080)],void 0===(i=function(e){return{create:function(){var t=e.create(),n={};return{measure:function(e){return{stream_info_has_audio:(n=e.data.streamInfo).haveAudio,stream_info_has_video:n.haveVideo,stream_info_preroll_duration:n.prerollDuration?n.prerollDuration:0,stream_info_width:n.videoInfo?n.videoInfo.width:0,stream_info_height:n.videoInfo?n.videoInfo.height:0,stream_info_frame_rate:n.videoInfo?n.videoInfo.frameRate:0,stream_info_sample_rate:n.audioInfo?n.audioInfo.sampleRate:0,stream_info_channels:n.audioInfo?n.audioInfo.channels:0,stream_info_bits_per_sample:n.audioInfo?n.audioInfo.bitsPerSample:0,connection_id:n.url.split("&cid=")[1].split("&pid=")[0]}},getFlat:function(){return t.get(n,"streamInfo",5)},reset:function(){n={}},getStreamname:function(){return void((e=n)&&Object.prototype.hasOwnProperty.call(e,"rtmp")&&Object.prototype.hasOwnProperty.call(e.rtmp,"streamname"))?n.rtmp.streamname:"";var e}}}}}.apply(t,r))||(e.exports=i)},6545:function(e,t){var n;void 0===(n=function(){return{create:function(){return{measure:function(e){return{warning_message:e.data.message}}}}}}.apply(t,[]))||(e.exports=n)},2215:function(e,t,n){var r,i;r=[n(1933),n(8478),n(8333),n(8197),n(9583),n(9914),n(5999),n(2556),n(7982),n(2788),n(4402),n(2381),n(2247),n(9555),n(3932),n(4245),n(4815),n(7666),n(1025),n(27),n(6545)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b,_,T){function y(){var u,y,S,A,v,R,O,I,N,C,w,D,U,P,L=s.create().metrics,M={},F=n.create("NanoMetrics"),k=e,x=t,B={},H={},G={},V={},W=0,Y="classic",j=0,K=0,z=0,q=1;function Q(e){try{var t=function(e){var t=V&&V.source&&V.source.entries&&V.source.entries[W]&&V.source.entries[W].h5live?V.source.entries[W].h5live:{},n=t.server||{websocket:"",hls:"",progressive:""},r=t.rtmp&&t.rtmp.url?t.rtmp.url:"",i=function(e){var t=e.split(/\?(.+)/);return 1===t.length?t.push(""):t.pop(),t}(t.rtmp&&t.rtmp.streamname?t.rtmp.streamname:""),a=i[0],o=i[1],s=function(e,t,n){var r,i,a="";return e&&t&&(-1!==e.indexOf("bintu")||n&&n.websocket&&n.websocket.length&&-1!==n.websocket.indexOf("bintu")&&-1!==e.indexOf("localhost"))&&(i=/^[A-Za-z0-9 ]+$/,(r=t.split("-")[0]).length>4&&r.length<=6&&i.test(r))&&(a=t.split("-")[0]),a}(r,a,n),u=void 0!==H.hostname?H.hostname:"";void 0===(P=document.visibilityState)&&(P="unknown"),P=P;var d={application_name:"h5live-player",application_user_id:""+L.userId,application_event_id:""+L.eventId,application_account_id:""+L.accountId,application_account_key:""+L.accountKey,message:e.name.replace(/\b\w/g,(function(e){return e.toLowerCase()})).replace(/([A-Z])/g,"_$1").toUpperCase(),severity:"info",client_ip:"",timestamp_client:(new Date).toISOString(),referrer:document.location.href,os:k.os,os_version:k.osVersion,browser:k.browser,browser_version:k.browserVersion,player_id:y.id,player_type:y.type,player_version:y.version,player_event_name:e.name.replace(/\b\w/g,(function(e){return e.toLowerCase()})).replace(/([A-Z])/g,"_$1").toUpperCase(),server_domain:n.websocket&&n.websocket.length?n.websocket.split("//")[1].split("/")[0]:"",server_hostname:u,rtmp_url:r,rtmp_streamname:a,rtmp_streamparameter:o,connection_id:B&&B.url&&B.url.length?B.url.split("&cid=")[1].split("&pid=")[0]:"",time_played:G.currentTime?Math.round(1e3*G.currentTime)/1e3:0,time_elapsed:j>0?Math.round(performance.now()-j)/1e3:0,bintu_orga_hash:s,latency_control_mode:Y,visibility:P,player_event_count:q};for(var l in M)Object.prototype.hasOwnProperty.call(M,l)&&(d[l]=""+M[l]);return d}(e);switch(e.name){case i.READY:Y=e.data.config.playback.latencyControlMode,t.latency_control_mode=Y,e.data.config.playback.latencyControlMode=V.playback&&V.playback.latencyControlMode?V.playback.latencyControlMode:Y,X(C,"measure",e,t)&&J(t);break;case i.LOADING:O.reset(),R.reset(),J(t);break;case i.STREAM_INFO:case i.STREAM_INFO_UPDATE:B=e.data.streamInfo,X(S,"measure",e,t),J(t);break;case i.PLAY:j=performance.now(),X(v,"measure",e,t),J(t),w.resetSwitchCount();break;case i.STATS:G=e.data.stats;var n=Object.prototype.hasOwnProperty.call(G,"adaptive");n&&D.setStats(e,t),X(R,"measure",e,t)&&(n&&Z(t,D.getStatsFlat()),J(t));break;case i.START_BUFFERING:v.setTimePlayingStateEnd(),O.start();break;case i.STOP_BUFFERING:O.end(),v.setTimePlayingStateStart(),X(O,"measure",e,t)&&(t.message=t.player_event_name="BUFFERING",J(t));break;case i.PAUSE:K=t.time_elapsed,z+=K,v.setTimePlayingStateEnd();var r=v.getTimePlayingStateTotal();X(I,"measure",e,t),X(O,"reset",e,t),Z(t,S.getFlat()),Z(t,{time_playing_state_total:r,time_elapsed_total_sum:z,entries_length:V&&Object.prototype.hasOwnProperty.call(V,"source")&&Object.prototype.hasOwnProperty.call(V.source,"entries")?V.source.entries.length:1}),Z(t,w.getSwitchCountFlat()),J(t),B={},G={},v.resetTimePlayingStateSubtotal(),w.resetSwitchCount(),z=0,j=0;break;case i.ERROR:X(N,"measure",e,t),J(t);break;case i.DESTROY:J(t);break;case i.METADATA:case i.PLAYBACK_FINISHED:case i.MEDIA:case i.MUTE:case i.UNMUTE:case i.VOLUME_CHANGE:case i.STATE_CHANGE:break;case i.WARNING:X(U,"measure",e,t),J(t);break;case i.SWITCH_STREAM_INIT:X(w,"handleEvent",e,t)&&(Z(t,D.getStatsFlat()),J(t));break;case i.SWITCH_STREAM_SUCCESS:te(e,"entry")&&ee(e.data.entry.index),K=t.time_elapsed,z+=K,j=performance.now(),X(w,"handleEvent",e,t)&&(Z(t,S.getFlat()),Z(t,{time_elapsed_total:K,time_elapsed_total_sum:z}),J(t),S.reset());break;case i.SWITCH_STREAM_FAIL:case i.SWITCH_STREAM_ABORT:case i.UPDATE_SOURCE_INIT:X(w,"handleEvent",e,t)&&J(t);break;case i.UPDATE_SOURCE_SUCCESS:te(e,"source")&&((a=e.data.source)&&(V.source=a),ee(e.data.source.startIndex)),K=t.time_elapsed,z+=K,j=performance.now(),X(w,"handleEvent",e,t)&&(Z(t,S.getFlat()),Z(t,{time_elapsed_total:K,time_elapsed_total_sum:z}),J(t),S.reset());break;case i.UPDATE_SOURCE_FAIL:case i.UPDATE_SOURCE_ABORT:X(w,"handleEvent",e,t)&&J(t);break;case i.SERVER_INFO:H=e.data.serverInfo,X(A,"measure",e,t)&&(t.server_hostname=void 0!==H.hostname?H.hostname:"",J(t))}}catch(e){F.debug("metrics event error: "+(e.message?e.message:"exception"))}var a}function X(e,t,n,r){var i=e[t](n,r);if(i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(r[a]=i[a]);return!0}return!1}function J(e){F.debug("send "+e.player_event_name),u.send(e.player_event_name,e),1e5===q?(q=1,F.debug("playerEventCount reached 100000, reset to 1")):q+=1}function $(){if("object"==typeof y&&"function"==typeof y.off)for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&y.off(i[e],Q);u&&(u.destroy(),u=null),S=null,v=null,R=null,O=null,I=null,N=null,C=null,w=null,D=null,U=null}function Z(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(e,n)||(e[n]=t[n])}function ee(e){W=isNaN(e)?0:e}function te(e,t){var n=e&&Object.prototype.hasOwnProperty.call(e,"data");return t?n&&Object.prototype.hasOwnProperty.call(e.data,t):n}return{setup:function(e,t){var n;console.debug("NanoMetrics api version "+this.version),$(),F.debug("setup"),y=t,!(V=r.copy(e)).source.h5live||V.source.entries&&V.source.entries.length||(V.source.entries=[{h5live:r.copy(V.source.h5live)}],delete V.source.h5live);var s=V.metrics;if(!s)throw new x(o.METRICS_NO_OBJECT,a.SETUP.METRICS_NO_OBJECT);if("object"!=typeof s)throw new x(o.METRICS_NOT_OBJECT,a.SETUP.METRICS_NOT_OBJECT);if(!Object.getOwnPropertyNames(s).length)throw new x(o.METRICS_EMPTY_OBJECT,a.SETUP.METRICS_EMPTY_OBJECT);for(n in s)if(!Object.prototype.hasOwnProperty.call(s,n)||!Object.prototype.hasOwnProperty.call(L,n)||(L[n]=s[n],0===n.indexOf("customField"))){var P,k=n.split("customField");if(!(k.length>1&&0===k[0].length))throw new x(o.METRICS_PROPERTY_INVALID.replace("$key$",n),a.SETUP.METRICS_PROPERTY_INVALID);if(P=parseInt(k[1],10),isNaN(P))throw new x(o.METRICS_INDEX_INVALID.replace("$key$",n),a.SETUP.METRICS_INDEX_INVALID);if(P.toString()!==k[1])throw new x(o.METRICS_INDEX_INCORRECT.replace("$key$",n),a.SETUP.METRICS_INDEX_INCORRECT);if(P<1||P>10)throw new x(o.METRICS_INDEX_RANGE.replace("$key$",n),a.SETUP.METRICS_INDEX_RANGE);M["application_custom_field_"+k[1]]=s[n]}if(!L.accountId||!L.accountKey)throw new x(o.METRICS_NO_CREDENTIALS,a.SETUP.METRICS_NO_CREDENTIALS);for(n in s)F.debug("metrics config["+n+"]: "+JSON.stringify(s[n]));for(var B in S=l.create(),A=c.create(),v=f.create(),R=E.create(L.statsInterval),O=p.create(),I=m.create(),N=h.create(),C=g.create(),w=b.create(),D=_.create(),U=T.create(),u=d.create({accountId:L.accountId,accountKey:L.accountKey}),i)Object.prototype.hasOwnProperty.call(i,B)&&y.on(i[B],Q);V.source&&!isNaN(V.source.startIndex)&&(W=V.source.startIndex)},destroy:$}}return y.emptyConfig=s,y.validConfig=u,y}.apply(t,r))||(e.exports=i)},2788:function(e,t,n){var r,i;r=[n(8333)],void 0===(i=function(e){return{create:function(t){var n={accountId:void 0,accountKey:void 0};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&t&&Object.prototype.hasOwnProperty.call(t,r)&&void 0!==t[r]&&(n[r]=t[r]);var i=0,a={},o="https://glog1.nanocosmos.de/gelf/"+n.accountKey,s=e.create("MetricsLogger"),u=!0;function d(e,t,n){void 0!==a[e]&&(s.debug("#"+e+" "+t+" "+n+" "+a[e].status),delete a[e])}return window.addEventListener("unload",(function(){u=!1})),{send:function(e,t){try{i+=1;var n=new XMLHttpRequest;a[i]=n;var r=a[i];if(r.open("POST",o,u),r.onreadystatechange=function(e,t){if(this&&4===this.readyState){var n=this.status>=400?"fail":this.status>=200?"success":"unknown";d.call(null,e,t,n)}}.bind(r,i,e),["abort","error","timeout"].forEach((function(t){r.addEventListener(t,d.bind(null,i,e,t))})),void 0!==t){if("string"==typeof t)try{t=JSON.parse(t)}catch(e){t=null}if("object"==typeof t)try{t=JSON.stringify(t),s.debug("#"+i+" "+e+" send "+t),setTimeout(d.bind(null,i,e,"suspend"),5e3),r.send(t)}catch(e){d.call(null,i,"catch "+e.message)}}else r.send()}catch(e){d.call(null,i,"catch "+e.message)}},destroy:function(){for(var e in a)Object.prototype.hasOwnProperty.call(a,e)&&delete a[e]}}}}}.apply(t,r))||(e.exports=i)},7080:function(e,t){var n;void 0===(n=function(){return{create:function(){var e,t,n={},r={source:"src",options:"opt",config:"cfg",playback:"pb",entries:"entr"};function i(t,r){var a;for(var s in t)a=o(s,r),e&&Array.isArray(t[s])?i(t[s].slice(Math.max(t[s].length-e,0)),a):"object"==typeof t[s]?i(t[s],a):n[a]=t[s];return n}function a(e){return isNaN(e)&&e===e.toUpperCase()}function o(e,n){var i,o=n+"_";t&&Object.prototype.hasOwnProperty.call(r,e)&&(e=r[e]);for(var s=0;s<e.length;s++)a(i=e.charAt(s))&&(o+="_"),o+=i.toLowerCase();return o}function s(){n={}}return{get:function(n,r,a,o){return s(),n&&"object"==typeof n?(r=r?r.toLowerCase():"",e=a,t=o,i(n,r)):{}},reset:s}}}}.apply(t,[]))||(e.exports=n)},8209:function(e,t,n){var r,i;r=[n(1969),n(8544),n(6965),n(5810),n(1493),n(828),n(9583),n(492),n(5493),n(2682),n(3778),n(6028),n(6725),n(2874)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E){"use strict";function p(t){f.validatePlayerDivId(t),this.version=d.CORE,console.debug("NanoCore api version: "+this.version),this.type="default",this.id=Math.round(1e11*Math.random()).toString(),this._playerDivId=t,this._dummyPlayer=new e(this._playerDivId),this._realPlayer=this._dummyPlayer}var m=p.prototype=Object.create(e.prototype);return m.setup=function(e){return new E(function(n,r){var i=e&&e.events&&e.events.onError?e.events.onError:null,a=function(e){e.code||(e.code=l.SETUP.EXCEPTION),e.message||(e.message="An unknown error occured during setup.");var t={name:"Error",data:{code:e.code,message:e.message},player:this._playerDivId,id:this.id,version:this.version,state:s.IDLE};i&&this.on(t.name,i),this.emit(t.name,t),r(e)}.bind(this);try{this._realPlayer&&this._realPlayer!==this._dummyPlayer&&(this._realPlayer.destroy(),this._realPlayer=this._dummyPlayer),t.create(this._playerDivId,e).then(function(t){this.version=t.version,this.type=t.type,this._realPlayer=t,this._realPlayer.id=this.id,this._propagator=c.create(t,o,this),this._realPlayer.setup(e).then(function(e){window.onunload=function(){this._realPlayer.destroy()}.bind(this),function(e){n(e)}(e)}.bind(this),a)}.bind(this),(function(e){a(e)}))}catch(e){a(e)}}.bind(this))},m.destroy=function(){this._realPlayer.destroy()},m.play=function(){this._realPlayer.play()},m.pause=function(){this._realPlayer.pause()},m.mute=function(){this._realPlayer.mute()},m.unmute=function(){this._realPlayer.unmute()},m.setVolume=function(e){this._realPlayer.setVolume(e)},m.updateSource=function(e,t){return this._realPlayer.updateSource(e,t)},m.switchStream=function(e){return this._realPlayer.switchStream(e)},m.setAdaption=function(e){this._realPlayer.setAdaption(e)},p.events=o,p.states=s,p.emptyConfig=n,p.validConfig=r,p.entryEmptyConfig=i,p.entryValidConfig=a,p.pauseReasons=u,p.errorCodes=l,p.version=d.CORE,p.capabilities=t.capabilities,p}.apply(t,r))||(e.exports=i)},9921:function(e,t,n){var r,i;r=[n(130),n(8209),n(1583),n(2215),n(4216),n(9374),n(1490),n(6028),n(1473),n(6725),n(9658),n(8197)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c){"use strict";var f,E=t.events,p=t.states,m=t.emptyConfig,h=t.validConfig,g=t.entryEmptyConfig,b=t.entryValidConfig,_=t.pauseReasons,T=t.errorCodes,y=t.capabilities,S=n.events,A=n.publicEvents,v=n.emptyConfig,R=n.validConfig,O=i.emptyConfig,I=i.validConfig,N=r.emptyConfig,C=r.validConfig,w={};for(f in E)w[f]=E[f];for(f in A)w[f]=A[f];var D={};u.extend(m.create(),D),u.extend(v.create(),D),u.extend(O.create(),D),u.extend(N.create(),D);var U={};u.extend(h.create(),U),u.extend(R.create(),U),u.extend(I.create(),U),u.extend(C.create(),U);var P={};u.extend(g.create(),P);var L={};function M(t){d.validatePlayerDivId(t),this.version=e.NANOPLAYER,console.debug("NanoPlayer api version: "+this.version),this.coreversion="",this.viewversion="",this.type="default",this.id=Math.round(1e11*Math.random()).toString(),this._playerDivId=t,this._core=null,this._view=null,this._metrics=null,this.state=p.IDLE}u.extend(b.create(),L);var F=M.prototype=Object.create(l.prototype);return F.setup=function(e){var n=c.copy(e);return new Promise(function(e,d){try{var l=f&&f.events&&f.events.onError?f.events.onError:null,c=function(e){e.code||(e.code=T.SETUP.EXCEPTION),e.message||(e.message="An unknown error occured during setup.");var t={name:"Error",data:{code:e.code,message:e.message},player:this._playerDivId,id:this.id,version:this.version,state:this.state};l&&this.on(t.name,l),this._emit(t.name,t),f.style.view&&(this._initView(f),null!==this._view&&this._view.handleView(M.states.UNINITIALIZED)),d(e)}.bind(this),f={};u.extend(n,f),this._bintuConfig=O.create(),this._coreConfig=m.create(),this._viewConfig=v.create(),u.merge(f,this._bintuConfig),u.merge(f,this._viewConfig),u.merge(f,this._coreConfig),u.extend(this._viewConfig,f),u.extend(this._coreConfig,f),u.clean(f),this.destroy();var E={};u.extend(n,E),this._checkConfig(E),i.setup(f).then(function(n){try{a.check(n),o.apply(n),(f=n).metrics&&(this._metrics=new r,this._metrics.version=this.version,this._metrics.setup(n,this)),u.merge(f,this._viewConfig),u.merge(f,this._coreConfig),u.extend(this._viewConfig,f),u.extend(this._coreConfig,f),u.clean(f),this._initView(f),this._core=new t(this._playerDivId),this._core.id=this.id,this._corePropagator=s.create(this._core,M.coreEvents,this),this._setPublicListeners(this._coreConfig.events,this),this._connectLayers(),this._core.setup(this._coreConfig).then(function(t){window.removeEventListener("unload",this.destroy.bind(this)),window.addEventListener("unload",this.destroy.bind(this)),this.coreversion=this._core.version,this.type=this._core.type,this._view&&this._view.postSetup(),u.extend(this._bintuConfig,t),u.extend(this._viewConfig,t),u.clean(t),e(t)}.bind(this),function(e){this._view&&this._viewConfig.style&&this._viewConfig.style.view&&this._view.handleView(M.states.UNINITIALIZED),d(e)}.bind(this))}catch(e){c(e)}}.bind(this),c)}catch(e){c(e)}}.bind(this))},F.destroy=function(){this._view&&this._view.destroy(),this._view=null,this._core&&this._core.destroy(),this._core=null,this._metrics&&this._metrics.destroy(),this._metrics=null},F._checkConfig=function(e){var t=[];if(e.source&&e.source.bintu){var n=u.check({source:{bintu:e.source.bintu}},I.create());t=t.concat(n)}if(e.style){var r=u.check({style:e.style},R.create());t=t.concat(r)}if(e.source){delete e.source.bintu,delete e.style,delete e.metrics,delete e.events;var i=u.check(e,h.create()).filter((function(e){return-1!==e.indexOf("valid")}));t=t.concat(i)}if(t.length){var a=e&&e.events&&e.events.onWarning?e.events.onWarning:null;a&&this.on(E.WARNING,a),t.forEach(function(e){var t={name:E.WARNING,data:{message:e},player:this._playerDivId,id:this.id,version:this.version,state:this.state};this._emit(t.name,t)}.bind(this))}},F._initView=function(e){this._view||(this._view=new n,this._view.version=this.version,this.viewversion=this._view.version,this._view.setBaseValues(this._playerDivId),this._view.baseSetup(e),this._viewPropagator=s.create(this._view,M.viewPublicEvents,this),this._setPublicListeners(this._viewConfig.events,this))},F._setPublicListeners=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&"function"==typeof e[n]){var r=n.replace("on",""),i=e[n];t.on(r,i),delete e[n]}},F._connectLayers=function(){var e=[M.coreEvents.MEDIA];function t(e,t,n,r){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&-1===t.indexOf(e[i])&&n.on(e[i],r.bind(this))}t(M.coreEvents,e,this,this._onCoreEvent.bind(this)),this._view&&t(M.viewEvents,[],this._view,this._onViewEvent.bind(this))},F._onCoreEvent=function(e){if(this.state=e.state,this._view&&this._viewConfig.style&&this._viewConfig.style.view){if(e.name===M.coreEvents.STATS)return void this._view.handleStats(e.data.stats);e.name===M.coreEvents.STATE_CHANGE?this._view.update(e):e.name===M.coreEvents.STREAM_INFO?this._view.handleStreamInfo(e):e.name===M.coreEvents.STREAM_INFO_UPDATE?this._view.handleStreamInfoUpdate(e):e.name===M.coreEvents.MUTE||e.name===M.coreEvents.UNMUTE?this._view.handleMute(e.name===M.coreEvents.MUTE):e.name===M.coreEvents.VOLUME_CHANGE?this._view.handleVolume(e.data.volume):e.name===M.coreEvents.READY?this._view.handleConfig(e.data.config):e.name===M.coreEvents.METADATA&&this._view.handleMetaData(e)}},F._onViewEvent=function(e){this._core&&(e.name===M.viewEvents.PLAY?this._core.play():e.name===M.viewEvents.PAUSE?this._core.pause():e.name===M.viewEvents.MUTE?this._core.mute():e.name===M.viewEvents.UNMUTE?this._core.unmute():e.name===M.viewEvents.VOLUME_CHANGE?this._core.setVolume(e.data.volume):e.name===M.viewEvents.FULLSCREEN_CHANGE&&this._emit(M.viewPublicEvents.FULLSCREEN_CHANGE,e.data))},F._emit=function(e,t){var n={};t&&t.name&&t.data?n=t:t?(n.data=t,n.name=e||"unknown"):(n.data={},n.name=e||"unknown"),n.player=this._playerDivId,n.id=this.id,n.version=this.version,n.state=this.state,this.emit(n.name,n)},F._callMethod=function(){if(!this._core)throw new Error('"'+arguments[0]+'" must be implemented or player not initialized by call setup.');return this._core[arguments[0]].apply(this._core,Array.prototype.slice.call(arguments,1))},F._callViewMethod=function(){if(!this._view)throw new Error('"'+arguments[0]+'" must be implemented or player not initialized by call setup.');return this._view[arguments[0]].apply(this._view,Array.prototype.slice.call(arguments,1))},F.play=function(){this._callMethod("play")},F.pause=function(){this._callMethod("pause")},F.mute=function(){this._callMethod("mute")},F.unmute=function(){this._callMethod("unmute")},F.setVolume=function(e){this._callMethod("setVolume",e)},F.updateSource=function(e,t){return new Promise(function(n,r){i.updateSource(e,t).then(function(e){a.check(e),o.apply(e),this._callMethod("updateSource",e.source,t).then((function(e){n(e)}),(function(e){r(e)}))}.bind(this),function(e){var t={name:"Error",data:{code:e.code,message:e.message},player:this._playerDivId,id:this.id,version:this.version,state:this.state};this._emit(t.name,t),r(e)}.bind(this))}.bind(this))},F.switchStream=function(e){return new Promise(function(t,n){this._callMethod("switchStream",e).then((function(e){t(e)}),(function(e){n(e)}))}.bind(this))},F.setAdaption=function(e){this._callMethod("setAdaption",e)},F.requestFullscreen=function(){return this._callViewMethod("requestFullscreen")},F.exitFullscreen=function(){return this._callViewMethod("exitFullscreen")},M.states=p,M.coreEvents=E,M.viewEvents=S,M.viewPublicEvents=A,M.emptyConfig=D,M.validConfig=U,M.entryEmptyConfig=P,M.entryValidConfig=L,M.events=w,M.pauseReasons=_,M.capabilities=y,window.NanoPlayer=M,M}.apply(t,r))||(e.exports=i)},6722:function(e,t,n){var r,i;r=[n(1969),n(3284),n(1313),n(166),n(3778),n(3890),n(2682),n(8053),n(9658),n(9088),n(1473),n(8478),n(8197),n(6965),n(5810),n(9583),n(8656),n(492),n(5493)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b){var _=d.log,T=2e4,y=2e4;function S(e){this.version=o.CORE,this.type="flash",this.config=E.create(),this._volume=1,this._muted=!1,this._mediaElement=null,this._mediaSource=null,this._playing=!1,this._playerDivId=e,this._playerDiv=document.getElementById(this._playerDivId),this._mediaElementId="flash-"+e,this.state=this.STATE.IDLE,this._triggered=0,this._stats={currentTime:0,playout:{start:0,end:0},buffer:{start:0,end:0,delay:{before:0,current:0,avg:0,min:0,max:0,deviation:0},update:{current:0,avg:0,min:0,max:0,deviation:0}},quality:{droppedVideoFrames:0,droppedVideoFramesCurrent:0,corruptedVideoFrames:0,corruptedVideoFramesCurrent:0,creationTime:0,totalVideoFrames:0},bitrate:{current:0,avg:0,min:0,max:0,deviation:0},framerate:{current:0,avg:0,min:0,max:0,deviation:0}},this._intervalEmitStats=0,this._streamInfo=null,this._isSwitchStream=!1,this._isUpdateSource=!1,this._switchCount=0}S.isSupported=function(){return 0!==s.ua.pv[0]||s.ua.pv===[0,0,0]},S.supportedTechniques=[t.FLASH];var A=S.prototype=Object.create(e.prototype);return A.setup=function(e){return new Promise(function(t,r){try{var a=l.check(e,p.create());l.merge(e,this.config),l.clean(this.config),!this.config.source.h5live||this.config.source.entries&&this.config.source.entries.length||(this.config.source.entries=[{h5live:f.copy(this.config.source.h5live)}]);var o=this.config.source.entries[isNaN(this.config.source.startIndex)?0:this.config.source.startIndex].h5live.rtmp,s=this.config.source.entries[isNaN(this.config.source.startIndex)?0:this.config.source.startIndex].h5live.params;if(!(o&&o.url&&o.streamname||s&&s.url&&s.stream))throw new c(n.CONFIG_RTMP,i.SETUP.CONFIG_RTMP);this._emitter=new u,this._setListeners(this.config.events,this),a.forEach(function(e){this._emitWarning(e)}.bind(this)),this._metaDataEnabled=this.config.playback.metadata,this._setupVideoElement(),this._setupFinish().then(t,r)}catch(e){r(e)}}.bind(this))},A.play=function(){this._setMediaElement(),this._mediaElement.Start()},A.pause=function(){this._isSwitchStream&&(this._emit(m.SWITCH_STREAM_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream}),this._isSwitchStream=!1),this._isUpdateSource&&(this._emit(m.UPDATE_SOURCE_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"update",id:this.isUpdateSource}),this._isUpdateSource=!1),this._triggered=arguments.length?arguments[0]:0,clearTimeout(this._bufferTimeout),this._setMediaElement(),this._mediaElement.Stop()},A.mute=function(){this._setMediaElement(),this._muted=!0,this._mediaElement.SetVolume(0),this._emit(m.MUTE)},A.unmute=function(){this._setMediaElement(),this._muted=!1,this._mediaElement.SetVolume(this._volume),this._emit(m.UNMUTE)},A.setVolume=function(e){e<0&&(e=0),e>1&&(e=1),this._setMediaElement(),this._volume=e,this._muted||this._mediaElement.SetVolume(this._volume),this._emit(m.VOLUME_CHANGE,{volume:e})},A.destroy=function(){this._setState(this.STATE.DESTROYING),this._playing&&this.pause(this.state),this._emit(m.DESTROY),this._mediaElement&&(this.pause(),document.getElementById(this._playerDivId).removeChild(this._mediaElement),this._mediaElement=null),clearTimeout(this._bufferTimeout),this.removeAllListeners()},A.updateSource=function(e){return new Promise(function(t,n){try{this._isSwitchStream&&(this._emit(m.SWITCH_STREAM_ABORT,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream,reason:"superseded"}),this._isSwitchStream=!1),this._isUpdateSource&&(this._emit(m.UPDATE_SOURCE_ABORT,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"update",id:this.isUpdateSource,reason:"superseded"}),this._isUpdateSource=!1),this._isUpdateSource=Math.round(1e11*Math.random()),this._switchCount++,setTimeout(this._emit.bind(this,m.UPDATE_SOURCE_INIT,{source:this.config.source,entry:this._getEntry(),rule:"none",options:this.config.source.options?this.config.source.options:{},tag:"",count:this._switchCount,type:"direct",id:this._isUpdateSource}),0);var r=!1;this.state!==this.STATE.PLAYING&&this.state!==this.STATE.LOADING&&this.state!==this.STATE.BUFFERING||(r=!0),this.config.source=e,!this.config.source.h5live||this.config.source.entries&&this.config.source.entries.length||(this.config.source.entries=[{h5live:f.copy(this.config.source.h5live)}]),this._setConnectionConfig(this.config.source.entries[isNaN(this.config.source.startIndex)?0:this.config.source.startIndex].h5live),this.config.source.entries.length>1&&this.config.source.options&&this.config.source.options.adaption&&this.config.source.options.adaption.rule&&"none"!==this.config.source.options.adaption.rule&&this._emitWarning("ABR is not supported with flash!"),r?this.play():(setTimeout(this._emit.bind(this,m.UPDATE_SOURCE_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isUpdateSource}),0),this._isUpdateSource=!1),t(f.copy(this.config))}catch(e){n(e)}}.bind(this))},A.switchStream=function(e){return new Promise(function(t,n){if(isNaN(e)||e<0||e>=this.config.source.entries.length)n(new c(a.PLAYER.INVALID_ENTRY_INDEX,i.PLAYER.INVALID_ENTRY_INDEX));else{this._isSwitchStream&&(this._emit(m.SWITCH_STREAM_ABORT,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream,reason:"superseded"}),this._isSwitchStream=!1),this._isUpdateSource&&(this._emit(m.UPDATE_SOURCE_ABORT,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"update",id:this.isUpdateSource,reason:"superseded"}),this._isUpdateSource=!1),this._isSwitchStream=Math.round(1e11*Math.random()),this._switchCount++,setTimeout(this._emit.bind(this,m.SWITCH_STREAM_INIT,{source:this.config.source,entry:this._getEntry(),rule:"none",options:this.config.source.options?this.config.source.options:{},tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream}),0);var r=!1;this.state!==this.STATE.PLAYING&&this.state!==this.STATE.LOADING&&this.state!==this.STATE.BUFFERING||(r=!0),this._setConnectionConfig(this.config.source.entries[e].h5live),r?this.play():(setTimeout(this._emit.bind(this,m.SWITCH_STREAM_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream}),0),this._isSwitchStream=!1),t(f.copy(this.config))}}.bind(this))},A.setAdaption=function(e){e&&e.rule&&"none"!==e.rule&&this._emitWarning("ABR is not supported with flash!")},A._setupVideoElement=function(){var e="nano.player.swf";void 0!==this.config.playback.flashplayer&&(e=this.config.playback.flashplayer);var t=document.createElement("div");t.setAttribute("id","flashReplace"),this._playerDiv.appendChild(t);var n={};n.id=this._mediaElementId,n.name=this._mediaElementId,n.align="middle",n.wmode="transparent",s.embedSWF(e,"flashReplace","100%","100%","9.0.0",null,{},{quality:"high",bgcolor:"#000000",allowscriptaccess:"always",allowfullscreen:"true",allowfullscreeninteractive:"true",wmode:"transparent"},n)},A._setupFinish=function(){return new Promise(function(e,t){var n=0,a=0;n=setInterval(function(){if(a++,n&&(this._mediaElement=document.getElementById(this._mediaElementId),this._mediaElement&&this._mediaElement.Start)){clearInterval(n);try{if(window._flashPlayers[this._mediaElementId]=this,this._setName(),this._mediaElement.TraceExternalFunctions(),this._setNetStream(!1),this._setConnectionConfig(this.config.source.entries[isNaN(this.config.source.startIndex)?0:this.config.source.startIndex].h5live),this.config.source.entries.length>1&&this.config.source.options&&this.config.source.options.adaption&&this.config.source.options.adaption.rule&&"none"!==this.config.source.options.adaption.rule&&this._emitWarning("ABR is not supported with flash!"),this._setPlayerConfig(this._playerDiv.offsetWidth,this._playerDiv.offsetHeight),this.config.playback.muted&&this.mute(),this.config.playback.timeouts){var o=1e3*this.config.playback.timeouts.loading,s=1e3*this.config.playback.timeouts.buffering;T=Math.max(Math.min(o,6e4),1e4),y=Math.max(Math.min(s,6e4),1e4)}this.config.type=this.type,this._setState(this.STATE.READY),this._emit(m.READY,{config:this.config}),this.config.playback.autoplay&&this.play(),e(f.copy(this.config))}catch(e){t(e)}}a>100&&(clearInterval(n),t(new c(r.EMBED_PLAYER,i.SETUP.EMBED_PLAYER)))}.bind(this),100)}.bind(this))},A._setListeners=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&"function"==typeof e[n]){var r=n.replace("on",""),i=e[n];t.on(r,i)}},A._setState=function(e){this.state=e,_("nanoplayer ("+this._playerDivId+'): set state "'+this._getState()+'"',3),this._emit(m.STATE_CHANGE)},A._getState=function(){for(var e in this.STATE)if(this.STATE[e]===this.state)return e},A._emit=function(e,t){var n={};t&&t.name&&t.data?n=t:t?(n.data=t,n.name=e||"unknown"):(n.data={},n.name=e||"unknown"),n.player=this._playerDivId,n.id=this.id,n.version=this.version,n.state=this.state,"Error"===e&&n.data.code&&n.data.message&&_("nanoplayer ("+this._playerDivId+"): error "+n.data.code+" "+n.data.message,1),this.emit(n.name,n)},A._emitError=function(e,t){this._emit(m.ERROR,{code:e,message:t})},A._emitWarning=function(e){this._emit(m.WARNING,{message:e})},A._setConnectionConfig=function(e){this._setMediaElement();var t=e.rtmp,n=e.params,r=n&&n.url?n.url:t&&t.url?t.url:null,i=n&&n.stream?n.stream:t&&t.streamname?t.streamname:null;r&&i?(this.config.url=r+"/"+i,this._mediaElement.SetServerUrl(r),this._mediaElement.SetStreamName(i)):this.config.url=""},A._setPlayerConfig=function(e,t){this._setMediaElement(),this._mediaElement.SetVideoDisplayResolution(e+","+t),this._mediaElement.SetResize("false"),this._mediaElement.SetOnlyThisResolution(e+","+t)},A._setNetStream=function(e){this._setMediaElement(),this._mediaElement.UseNetstream(e?1:0)},A._setMediaElement=function(){var e=document.getElementById(this._mediaElementId);this._mediaElement=e},A._getPauseReason=function(e){var t="";if(e)switch(this.state){case this.STATE.READY:t=b.SERVER_NOT_FOUND;break;case this.STATE.LOADING:t=b.STREAM_NOT_FOUND;break;case this.STATE.BUFFERING:t=b.BUFFER;break;case this.STATE.UNKNOWN:t=b.UNKNOWN;break;case this.STATE.PLAYING:t=b.NORMAL;break;case this.STATE.DESTROYING:t=b.DESTROY;break;case this.STATE.PLAYBACK_RESTARTING:t=b.PLAYBACK_RESTART;break;default:t=b.NORMAL}else t=b.NORMAL;return t},A._startIntervals=function(){this._stopIntervals(),this._onStats(!0),this._intervalEmitStats=setInterval(this._onStats.bind(this),100)},A._stopIntervals=function(){this._intervalEmitStats&&(clearInterval(this._intervalEmitStats),this._intervalEmitStats=0)},A._getEntry=function(){var e={},t=this._mediaElement.GetStreamName();return this.config.source.entries.forEach((function(n){n.h5live.rtmp.streamname===t&&(e=f.copy(n))})),e},A._onStreamInfo=function(e){_("nanoplayer ("+this._playerDivId+"): onStreamInfo",2),this._setMediaElement(),this._streamInfo=e,this._streamInfo.url="",this._streamInfo.rtmp={url:this._mediaElement.GetServerUrl(),streamname:this._mediaElement.GetStreamName()},this._isSwitchStream&&(this._emit(m.SWITCH_STREAM_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream}),this._isSwitchStream=!1),this._isUpdateSource&&(this._emit(m.UPDATE_SOURCE_SUCCESS,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"update",id:this.isUpdateSource}),this._isUpdateSource=!1),this._emit(m.STREAM_INFO,{streamInfo:this._streamInfo})},A._onStats=function(e){var t=e?0:this._stats.currentTime+.1;this._stats.currentTime=t;var n="stats error";try{n=JSON.stringify(this._stats)}catch(e){}_("nanoplayer ("+this._playerDivId+"): stats - "+n,4),this._emit(m.STATS,{stats:this._stats})},A._onLoading=function(){this._isSwitchStream||this._isUpdateSource||(this._playing=!0,this._setState(this.STATE.LOADING),this._emit(m.LOADING)),this._loadingTimeout=setTimeout(function(){this._isSwitchStream&&(this._emit(m.SWITCH_STREAM_FAIL,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"direct",id:this._isSwitchStream,code:i.STREAM.NOT_FOUND,message:"The requested stream can not be found."}),this._isSwitchStream=!1),this._isUpdateSource&&(this._emit(m.UPDATE_SOURCE_FAIL,{source:this.config.source,entry:this._getEntry(),rule:"none",tag:"",count:this._switchCount,type:"update",id:this.isUpdateSource,code:i.STREAM.NOT_FOUND,message:"The requested stream can not be found."}),this._isUpdateSource=!1),this._emitError(i.STREAM.NOT_FOUND,"The requested stream can not be found."),this.pause(this.state)}.bind(this),T)},A._onPlaying=function(){clearTimeout(this._loadingTimeout),clearTimeout(this._bufferTimeout),this.state===this.STATE.LOADING&&(this._startPlayingTimeout&&clearTimeout(this._startPlayingTimeout),this._startPlayingTimeout=setTimeout(function(){clearTimeout(this._startPlayingTimeout),this._startPlayingTimeout=0,this._setState(this.STATE.PLAYING),this._playing=!0,this.setVolume(this._volume),!this._intervalEmitStats&&this._emit(m.PLAY,{stats:{connecting:0,connected:0,firstFragmentReceived:0,firstFrameRendered:0,playable:0,playing:0}}),!this._intervalEmitStats&&this._startIntervals()}.bind(this),1e3))},A._onStartBuffering=function(){this.state<this.STATE.PLAYING||this._isSwitchStream||this._isUpdateSource||(this._setState(this.STATE.BUFFERING),this._startBufferTimeout&&clearTimeout(this._startBufferTimeout),this._startBufferTimeout=setTimeout(function(){clearTimeout(this._startBufferTimeout),this._startBufferTimeout=0,this.state===this.STATE.BUFFERING&&(this._playing=!1,this._emit(m.START_BUFFERING),this._bufferTimeout=setTimeout(function(){this._emitError(i.STREAM.MEDIA_NOT_AVAILABLE,"No media available"),this.pause(this.state)}.bind(this),y))}.bind(this),2e3))},A._onStopBuffering=function(){clearTimeout(this._loadingTimeout),clearTimeout(this._bufferTimeout),this._playing=!0,this._setState(this.STATE.PLAYING),this._emit(m.STOP_BUFFERING)},A._onPause=function(){if(!this._isSwitchStream&&!this._isUpdateSource){clearTimeout(this._loadingTimeout),clearTimeout(this._bufferTimeout),this._playing=!1;var e=this._getPauseReason(this._triggered);_("nanoplayer ("+this._playerDivId+'): trigger pause with reason "'+e+'", state "'+this._getState()+'"',3),this._setState(this.STATE.PAUSED),this._stopIntervals(),this._emit(m.PAUSE,{reason:e})}},A._emitMetaData=function(e,t){if(this._metaDataEnabled){_("nanoplayer ("+this._playerDivId+"): onMetaData",2);try{var n=JSON.stringify(t);_("nanoplayer ("+this._playerDivId+"): handlerName="+e+", metaData="+n,2)}catch(e){}var r={handlerName:e,streamTime:0,message:t};this._emit(m.METADATA,r)}},A._onMetaData=function(e){if(Array.isArray(e)){for(var t={},n=0;n<e.length;n+=1)t[e[n].name]=e[n].value;e=t}var r={haveAudio:!!e.audiocodecid,haveVideo:!!e.videocodecid,audioInfo:null,videoInfo:null};r.haveAudio&&(r.audioInfo={sampleRate:e.audiosamplerate,channels:e.stereo?2:1,bitsPerSample:e.audiosamplesize}),r.haveVideo&&(r.videoInfo={width:e.width,height:e.height,frameRate:e.framerate}),this._onStreamInfo(r),this._emitMetaData("MetaData",e)},A._onCuePoint=function(e){this._emitMetaData("CuePoint",e)},A._onVolumeChange=function(){},A._onResize=function(){if(this._streamInfo&&this._streamInfo.haveVideo){this._setMediaElement();var e=this._streamInfo.videoInfo.width,t=this._streamInfo.videoInfo.height,n=this._mediaElement.GetCurrentSizeOfContainer(),r=n[0]/n[1],i=e/t;r<i?(e=n[0],t=Math.round(e/i)):(t=n[1],e=Math.round(t*i)),this._mediaElement.SetResize("true"),this._mediaElement.SetVideoDisplayResolution(e+","+t),this._mediaElement.SetResize("false"),this._mediaElement.SetOnlyThisResolution(e+","+t),this._resizeTimeout&&clearTimeout(this._resizeTimeout),this._resizeTimeout=setTimeout(function(){this._resizeTimeout=0,this._mediaElement.SetResize("true"),this._mediaElement.SetVideoDisplayResolution(e+","+t),this._mediaElement.SetResize("false"),this._mediaElement.SetOnlyThisResolution(e+","+t)}.bind(this),300)}},A._onDebug=function(e){var t="string"==typeof e?e:e.message?e.message:e.data.message?e.data.message:"unknown";_("nanoplayer ("+this._playerDivId+"): "+t,2)},A._onFlashReady=function(){this._onDebug("flash ready")},A._onFlashState=function(e,t){if(void 0!==e){var n="";if("string"==typeof e||e instanceof String)n=e,this._onDebug("onFlashCallState: "+n+" from "+t);else for(var r in this._onDebug("onFlashCallState: "+t),e)Object.prototype.hasOwnProperty.call(e,r)&&(this._onDebug("onFlashCallState: "+r+" = "+e[r]),"state"!==r&&"code"!==r||(n=e[r]));n===this.FLASH_STATE.VIDEO_DISPLAY.LOADING||n===this.FLASH_STATE.NET_STREAM.PLAY.RESET||n===this.FLASH_STATE.NET_STREAM.PLAY.UNPUBLISH_NOTIFY||n===this.FLASH_STATE.NET_STREAM.PLAY.PUBLISH_NOTIFY?this._onLoading():n===this.FLASH_STATE.VIDEO_DISPLAY.REWINDING||n===this.FLASH_STATE.VIDEO_DISPLAY.PLAYING||n===this.FLASH_STATE.NET_STREAM.PLAY.START?(this.state===this.STATE.LOADING&&this._onPlaying(),this.state===this.STATE.BUFFERING&&this._onStopBuffering()):n===this.FLASH_STATE.VIDEO_DISPLAY.BUFFERING?this._onStartBuffering():n!==this.FLASH_STATE.VIDEO_DISPLAY.CONNECTION_ERROR&&n!==this.FLASH_STATE.VIDEO_DISPLAY.STOPPED&&n!==this.FLASH_STATE.VIDEO_DISPLAY.DISCONNECTED&&n!==this.FLASH_STATE.NET_STREAM.PLAY.STOP&&n!==this.FLASH_STATE.NET_STREAM.PLAY.STREAM_NOT_FOUND||this._onPause(),"error"===e.level?this._onDebug("onFlashCallState: ERROR - "+n):this._onDebug("onFlashCallState: state="+n)}},A._setName=function(){this._setMediaElement(),this._mediaElement.SetName(this._mediaElementId)},A.STATE=g,A.FLASH_STATE=h,A.PAUSE_REASON=b,window._flashPlayers={},window.onFlashCallExternalApplicationReady=function(){return!0},window.onFlashCallInternalApplicationReady=function(e){window._flashPlayers[e]&&window._flashPlayers[e]._onFlashReady()},window.onFlashCallMyTrace=function(e,t){window._flashPlayers[e]&&window._flashPlayers[e]._onDebug(t)},window.onFlashCallAutoResizeContainer=function(e,t){window._flashPlayers[e]&&window._flashPlayers[e]._onResize(t)},window.onFlashCallVolumeChange=function(e,t){window._flashPlayers[e]&&window._flashPlayers[e]._onVolumeChange(t)},window.onFlashCallMetaDataReceived=function(e,t){window._flashPlayers[e]&&window._flashPlayers[e]._onMetaData(t)},window.onFlashCallCuePointReceived=function(e,t){window._flashPlayers[e]&&window._flashPlayers[e]._onCuePoint(t)},window.onFlashCallState=function(e,t,n){window._flashPlayers[e]&&window._flashPlayers[e]._onFlashState(t,n)},S}.apply(t,r))||(e.exports=i)},651:function(e,t,n){var r,i;r=[n(6637),n(3778),n(3890),n(492),n(9673),n(594),n(680),n(1376),n(7354),n(8333)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d){return{create:function(i){var l,c,f,E,p,m,h=d.create("ErrorSelector"),g=[{type:o.STATE_CHANGE,listener:function(e){(l=e.data.state)===r.LOADING&&(E=!1,f=null)}},{type:o.LOADING_TIMEOUT,listener:function(){b("STREAM",E?"NOT_ENOUGH_DATA":f&&"stopped"===f?"SOURCE_STOPPED":"NOT_FOUND")}},{type:o.BUFFERING_TIMEOUT,listener:function(){b("STREAM",f&&"stopped"===f?"SOURCE_STOPPED":"MEDIA_NOT_AVAILABLE")}},{type:o.STREAM_INFO,listener:function(){E=!0}},{type:o.DOCUMENT_VISIBLE,listener:_},{type:o.DOCUMENT_HIDDEN,listener:_},{type:a.MEDIA_ERROR,listener:function(e){switch(e.data.code){case t.MEDIA.ABORTED:b("MEDIA","ABORTED");break;case t.MEDIA.DOWNLOAD_ERROR:b("MEDIA","DOWNLOAD_ERROR");break;case t.MEDIA.DECODE_ERROR:b("MEDIA","DECODE_ERROR");break;case t.MEDIA.NOT_SUPPORTED:b("MEDIA","NOT_SUPPORTED");break;case t.MEDIA.HLS_VIDEO_DECODE_ERROR:b("MEDIA","HLS_VIDEO_DECODE_ERROR");break;case t.MEDIA.MEDIA_SOURCE_ENDED:(p||m)&&l===r.LOADING?b("PLAYER","VISIBILITY_HIDDEN"):b("MEDIA","MEDIA_SOURCE_ENDED");break;case t.PLAYER.PLAYBACK_ERROR:b("PLAYER","PLAYBACK_ERROR");break;case t.MEDIA.HLS_BUFFER_UNDERRUN:b("MEDIA","HLS_BUFFER_UNDERRUN")}}},{type:s.STREAM_STATUS,listener:function(e){f=e.data.onStreamStatus.status}},{type:u.READY,listener:function(e){var t=(c=e.data.config).source.entries[c.source.startIndex],n=c.playback.autoplay;(c.source.group&&c.source.group.state&&"live"!==c.source.group.state||t.bintu&&t.bintu.state&&"live"!==t.bintu.state)&&n&&b("STREAM","SOURCE_STOPPED")}}];function b(e,r){var a,s;a=o.ERROR,s={code:t[e][r],message:n[e][r]},i.emit(a,s)}function _(e){e.name===o.DOCUMENT_HIDDEN?p=!0:(p&&(m=!0),p=!1,setTimeout((function(){m=!1}),1e3))}return h.debugEnabled()&&h.debug("init"),e.add({target:i,listeners:g}),{destroy:function(){e.remove({target:i,listeners:g})}}}}}.apply(t,r))||(e.exports=i)},7771:function(e,t){var n;void 0===(n=function(){var e="connection.";return{BASE:e,CONNECT:e+"connect",DISCONNECT:e+"disconnect",VIDEO_SOURCE:e+"videoSource",NETWORK_PLAY:e+"networkPlay",NO_SERVER_ERROR:e+"noServerError",MISSING_RTMP_ERROR:e+"missingRtmpError",SERVER_INFO:e+"serverInfo",NO_KEEP_CONNECTION:e+"noKeepConnection",URL:e+"url",NETWORK_UPDATE_SOURCE:e+"networkUpdateSource",UPDATE_SOURCE_INIT:e+"updateSourceInit",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",SWITCH_STREAM_INIT:e+"switchStreamInit",SWITCH_STREAM_SUCCESS:e+"switchStreamSuccess",SWITCH_STREAM_FAIL:e+"switchStreamFail",SWITCH_STREAM_ABORT:e+"switchStreamAbort"}}.apply(t,[]))||(e.exports=n)},3937:function(e,t){var n;void 0===(n=function(){return{BASE:"latency.",SEEK:"latency.seek",RATE:"latency.rate",BUFFER_GOAL_STATS:"latency.bufferGoalStats"}}.apply(t,[]))||(e.exports=n)},3019:function(e,t){var n;void 0===(n=function(){var e="logicManager.";return{BASE:e,API_PLAY:e+"apiPlay",API_PAUSE:e+"apiPause",NETWORK_CONNECTING:e+"networkConnecting",NETWORK_CONNECTED:e+"networkConnected",NETWORK_DISCONNECTED:e+"networkDisconnected",NETWORK_RECONNECTION_IMMINENT:e+"networkReconnectionImminent",NETWORK_RECONNECTING:e+"networkReconnecting",NETWORK_CONNECTION_ERROR:e+"networkConnectionError",NETWORK_INITIALIZATION_ERROR:e+"networkInitializationError",NETWORK_MAX_RETRY_REACHED:e+"networkMaxRetryReached",NETWORK_ERROR:e+"networkError",NETWORK_STATE_CHANGE:e+"networkStateChange",SERVER_INFO:e+"serverInfo",STREAM_INFO:e+"streamInfo",STREAM_INFO_UPDATE:e+"streamInfoUpdate",STREAM_QUALITY:e+"streamQuality",STREAM_FRAGMENT:e+"streamFragment",RANDOM_ACCESS_POINT:e+"randomAccessPoint",STREAM_URL:e+"streamUrl",BUFFER_TWEAKS_CREATED:e+"tweaksCreated",PLAYING:e+"playing",BUFFERING:e+"buffering",PLAY_STATS:e+"playStats",APPLICATION_STATE_CHANGE:e+"applicationStateChange",DURATION_CHANGE:e+"durationChange",CAN_PLAY:e+"canPlay",CAN_PLAY_THROUGH:e+"canPlayThrough",META_DATA:e+"metaData",FRAME_DROP:e+"frameDrop",VIEWPORT_VISIBLE:e+"viewportvisible",VIEWPORT_HIDDEN:e+"viewporthidden",UPDATE_SOURCE_INIT:e+"updateSourceInit",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",SWITCH_STREAM:e+"switchStream",MEDIA_PLAYING:e+"mediaPlaying",TIME_OFFSET:e+"timeOffset",LOADED_DATA:e+"loadedData",LOADED_META_DATA:e+"loadedMetaData",MEDIA_ERROR:e+"mediaError",MEDIA_ERROR_RECOVER:e+"mediaErrorRecover",CONFIG:e+"config",UPDATE_SOURCE:e+"updateSource",STREAM_SWITCH:e+"streamSwitch",SET_ADAPTION:e+"setAdaption",NETWORK_PLAY:e+"networkPlay",PLAY:e+"play",PUBLIC_EVENT:e+"publicEvent"}}.apply(t,[]))||(e.exports=n)},1683:function(e,t){var n;void 0===(n=function(){return{BASE:"metaData.",RECEIVED:"metaData.received",ERROR:"metaData.error"}}.apply(t,[]))||(e.exports=n)},164:function(e,t){var n;void 0===(n=function(){return{BASE:"source.",STATS:"source.stats",STREAM_SWITCH:"source.streamSwitch"}}.apply(t,[]))||(e.exports=n)},519:function(e,t){var n;void 0===(n=function(){var e="startupstats.";return{BASE:e,CREATED:e+"created"}}.apply(t,[]))||(e.exports=n)},8983:function(e,t){var n;void 0===(n=function(){var e="streamInfo.";return{BASE:e,CREATED:e+"created",UPDATED:e+"updated",MIME_TYPE_UNSUPPORTED:e+"mimetypeUnsupported"}}.apply(t,[]))||(e.exports=n)},3667:function(e,t){var n;void 0===(n=function(){return{BASE:"timeout.",LOADING_TIMEOUT:"timeout.loadingTimeout",BUFFERING_TIMEOUT:"timeout.bufferingTimeout"}}.apply(t,[]))||(e.exports=n)},2450:function(e,t){var n;void 0===(n=function(){var e="visibility.";return{BASE:e,HIDDEN:e+"hidden",VISIBLE:e+"visible"}}.apply(t,[]))||(e.exports=n)},1797:function(e,t,n){var r,i;r=[n(6637),n(3778),n(9583),n(9673),n(594),n(680),n(1376),n(8333),n(8197)],void 0===(i=function(e,t,n,r,i,a,o,s,u){return{create:function(r){var d,l=s.create("FacadePropagator"),c=1,f=!1,E=[{type:i.VOLUME_CHANGE,listener:function(e){e.data.muted!==f?m((f=e.data.muted)?n.MUTE:n.UNMUTE,{volume:c}):e.data.volume!==c&&(c=e.data.volume,m(n.VOLUME_CHANGE,{volume:c}))}},{type:i.QUALITY_STATS,listener:function(e){d||(d={}),d.quality=e.data}},{type:i.PLAY_STATS,listener:function(e){var t,r,i;d&&d.quality&&(t=d.quality),r&&d.adaptive&&(r=d.adaptive),i&&d.buffergoal&&(i=d.buffergoal),d=e.data.stats,t&&!d.quality&&(d.quality=t),r&&!d.adaptive&&(d.adaptive=r),i&&!d.buffergoal&&(d.buffergoal=i);var a,o="stats error";try{o=JSON.stringify(d),delete(a=JSON.parse(o)).buffer.update,delete a.buffer.delay.before}catch(e){}m(n.STATS,{stats:a})}},{type:i.PLAYBACK_FINISHED,listener:function(){m(n.PLAYBACK_FINISHED)}},{type:i.ACTIVE_VIDEO_ELEMENT_CHANGE,listener:function(e){setTimeout(m.bind(this,n.ACTIVE_VIDEO_ELEMENT_CHANGE,e.data),0)}},{type:a.META_DATA_RECEIVED,listener:function(e){setTimeout(m.bind(this,n.METADATA,e.data),0)}},{type:a.UPDATE_SOURCE_INIT,listener:function(e){setTimeout(m.bind(this,n.UPDATE_SOURCE_INIT,e.data),0)}},{type:a.UPDATE_SOURCE_SUCCESS,listener:function(e){setTimeout(m.bind(this,n.UPDATE_SOURCE_SUCCESS,e.data),0)}},{type:a.UPDATE_SOURCE_FAIL,listener:function(e){setTimeout(m.bind(this,n.UPDATE_SOURCE_FAIL,e.data),0)}},{type:a.UPDATE_SOURCE_ABORT,listener:function(e){setTimeout(m.bind(this,n.UPDATE_SOURCE_ABORT,e.data),0)}},{type:a.STREAM_INFO,listener:function(e){var t=u.copy(e.data);delete t.streamInfo.mimeType,setTimeout(m.bind(this,n.STREAM_INFO,t),0)}},{type:a.STREAM_INFO_UPDATE,listener:function(e){var t=u.copy(e.data);delete t.streamInfo.mimeType,setTimeout(m.bind(this,n.STREAM_INFO_UPDATE,t),0)}},{type:a.SWITCH_STREAM_INIT,listener:function(e){setTimeout(m.bind(this,n.SWITCH_STREAM_INIT,e.data),0)}},{type:a.SWITCH_STREAM_SUCCESS,listener:function(e){setTimeout(m.bind(this,n.SWITCH_STREAM_SUCCESS,e.data),0)}},{type:a.SWITCH_STREAM_FAIL,listener:function(e){setTimeout(m.bind(this,n.SWITCH_STREAM_FAIL,e.data),0)}},{type:a.SWITCH_STREAM_ABORT,listener:function(e){setTimeout(m.bind(this,n.SWITCH_STREAM_ABORT,e.data),0)}},{type:a.ADAPTIVE_STATS,listener:function(e){d||(d={}),d.adaptive=e.data}},{type:a.BUFFER_GOAL_STATS,listener:function(e){d||(d={}),d.buffergoal=e.data}},{type:a.SERVER_INFO,listener:function(e){var t={serverInfo:u.copy(e.data.onServerInfo)};delete t.serverInfo.capabilities,delete t.serverInfo.events,delete t.serverInfo.interfaceVersion,t.serverInfo.serverApplicationName=t.serverInfo.serverName,t.serverInfo.serverApplicationVersion=t.serverInfo.serverVersion,delete t.serverInfo.serverName,delete t.serverInfo.serverVersion,setTimeout(m.bind(this,n.SERVER_INFO,t),0)}}],p=[{event:a.META_DATA_ERROR,code:null,message:null,defer:!0},{event:a.MISSING_RTMP_ERROR,code:t.PLAYER.NO_RTMP_URL_SET,message:"No rtmp url set.",defer:!1},{event:a.NO_SERVER_ERROR,code:t.PLAYER.NO_SERVER_SET,message:"No server set.",defer:!1},{event:o.RECONNECTION_CONFIG_INVALID,code:t.NETWORK.RECONNECTION_CONFIG_INVALID,message:null,defer:!0},{event:i.BUFFER_TWEAKS_ERROR,code:t.PLAYER.BUFFER_CONFIG_INVALID,message:null,defer:!1}];function m(e,t){r.emit(e,u.copy(t))}function h(e){var t,n;p.forEach((function(r){r.event===e.name&&(t=r.code?r.code:e.data.code,n=r.message?r.message:e.data.reason,r.defer?setTimeout(g.bind(this,t,n),0):g(t,n))}))}function g(e,t){m(n.ERROR,{code:e,message:t})}return l.debugEnabled()&&l.debug("init"),e.add({target:r,listeners:E}),p.forEach((function(e){r.addListener(e.event,h)})),{destroy:function(){e.remove({target:r,listeners:E}),p.forEach((function(e){r.removeListener(e.event,h)}))}}}}}.apply(t,r))||(e.exports=i)},2909:function(e,t,n){var r,i;r=[n(8333),n(9658),n(6627),n(8197),n(6637),n(5055),n(9336),n(9673),n(680),n(594),n(1376),n(7354),n(7771),n(3019),n(3667),n(8983),n(1683),n(2450),n(164),n(3937),n(519),n(387),n(5444),n(4600),n(9665),n(4351),n(5620),n(8307),n(7831)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b,_,T,y,S,A,v,R,O,I,N){"use strict";return{create:function(n){var C,w,D=new t,U=e.create("LogicManager"),P=[],L=[{from:f.CONNECT,to:s.CONNECT},{from:f.DISCONNECT,to:s.DISCONNECT},{from:f.VIDEO_SOURCE,to:s.VIDEO_SOURCE},{from:f.NETWORK_PLAY,to:s.NETWORK_PLAY},{from:f.NETWORK_UPDATE_SOURCE,to:s.NETWORK_UPDATE_SOURCE},{from:f.NO_SERVER_ERROR,to:u.NO_SERVER_ERROR},{from:f.MISSING_RTMP_ERROR,to:u.MISSING_RTMP_ERROR},{from:f.NO_KEEP_CONNECTION,to:u.NO_KEEP_CONNECTION},{from:f.SERVER_INFO,to:u.SERVER_INFO},{from:f.URL,to:u.STREAM_URL},{from:f.UPDATE_SOURCE_INIT,to:u.UPDATE_SOURCE_INIT},{from:f.UPDATE_SOURCE_SUCCESS,to:u.UPDATE_SOURCE_SUCCESS},{from:f.UPDATE_SOURCE_FAIL,to:u.UPDATE_SOURCE_FAIL},{from:f.UPDATE_SOURCE_ABORT,to:u.UPDATE_SOURCE_ABORT},{from:f.SWITCH_STREAM_INIT,to:u.SWITCH_STREAM_INIT},{from:f.SWITCH_STREAM_SUCCESS,to:u.SWITCH_STREAM_SUCCESS},{from:f.SWITCH_STREAM_FAIL,to:u.SWITCH_STREAM_FAIL},{from:f.SWITCH_STREAM_ABORT,to:u.SWITCH_STREAM_ABORT},{from:p.LOADING_TIMEOUT,to:u.LOADING_TIMEOUT},{from:p.BUFFERING_TIMEOUT,to:u.BUFFERING_TIMEOUT},{from:m.CREATED,to:u.STREAM_INFO},{from:m.UPDATED,to:u.STREAM_INFO_UPDATE},{from:m.MIME_TYPE_UNSUPPORTED,to:u.MIME_TYPE_UNSUPPORTED},{from:h.RECEIVED,to:u.META_DATA_RECEIVED},{from:h.ERROR,to:u.META_DATA_ERROR},{from:g.VISIBLE,to:u.DOCUMENT_VISIBLE},{from:g.HIDDEN,to:u.DOCUMENT_HIDDEN},{from:b.STATS,to:u.ADAPTIVE_STATS},{from:b.STREAM_SWITCH,to:u.STREAM_SWITCH},{from:_.SEEK,to:s.SEEK},{from:_.RATE,to:s.SET_RATE},{from:_.BUFFER_GOAL_STATS,to:u.BUFFER_GOAL_STATS},{from:T.CREATED,to:u.STARTUP_STATS}],M=[{from:o.PLAY,to:E.API_PLAY},{from:o.PAUSE,to:E.API_PAUSE},{from:d.BUFFER_TWEAKS_CREATED,to:E.BUFFER_TWEAKS_CREATED},{from:d.PLAY_STATS,to:E.PLAY_STATS},{from:d.PLAYING,to:E.PLAYING},{from:d.BUFFERING,to:E.BUFFERING},{from:d.DURATION_CHANGE,to:E.DURATION_CHANGE},{from:d.CAN_PLAY,to:E.CAN_PLAY},{from:d.CAN_PLAY_THROUGH,to:E.CAN_PLAY_THROUGH},{from:d.PLAYING,to:E.MEDIA_PLAYING},{from:d.FRAME_DROP,to:E.FRAME_DROP},{from:d.VIEWPORT_VISIBLE,to:E.VIEWPORT_VISIBLE},{from:d.VIEWPORT_HIDDEN,to:E.VIEWPORT_HIDDEN},{from:d.TIME_OFFSET,to:E.TIME_OFFSET},{from:d.LOADED_DATA,to:E.LOADED_DATA},{from:d.LOADED_META_DATA,to:E.LOADED_META_DATA},{from:d.UPDATE_SOURCE_ABORT,to:E.UPDATE_SOURCE_ABORT},{from:d.UPDATE_SOURCE_FAIL,to:E.UPDATE_SOURCE_FAIL},{from:d.MEDIA_ERROR,to:E.MEDIA_ERROR},{from:d.MEDIA_ERROR_RECOVER,to:E.MEDIA_ERROR_RECOVER},{from:l.CONNECTING,to:E.NETWORK_CONNECTING},{from:l.CONNECTED,to:E.NETWORK_CONNECTED},{from:l.CONNECTION_OPEN,to:E.NETWORK_CONNECTION_OPEN},{from:l.DISCONNECTED,to:E.NETWORK_DISCONNECTED},{from:l.DESTROYED,to:E.NETWORK_DISCONNECTED},{from:l.RECONNECTING,to:E.NETWORK_RECONNECTING},{from:l.RECONNECTION_IMMINENT,to:E.NETWORK_RECONNECTION_IMMINENT},{from:l.INITIALIZATION_ERROR,to:E.NETWORK_INITIALIZATION_ERROR},{from:l.ERROR,to:E.NETWORK_ERROR},{from:l.STATE_CHANGE,to:E.NETWORK_STATE_CHANGE},{from:l.RANDOM_ACCESS_POINT,to:E.RANDOM_ACCESS_POINT},{from:l.STREAM_FRAGMENT,to:E.STREAM_FRAGMENT},{from:l.SERVER_INFO,to:E.SERVER_INFO},{from:l.STREAM_INFO,to:E.STREAM_INFO},{from:l.STREAM_INFO_UPDATE,to:E.STREAM_INFO_UPDATE},{from:l.STREAM_QUALITY,to:E.STREAM_QUALITY},{from:l.META_DATA,to:E.META_DATA},{from:l.UPDATE_SOURCE_SUCCESS,to:E.UPDATE_SOURCE_SUCCESS},{from:l.UPDATE_SOURCE_FAIL,to:E.UPDATE_SOURCE_FAIL},{from:l.UPDATE_SOURCE_ABORT,to:E.UPDATE_SOURCE_ABORT},{from:u.UPDATE_SOURCE_INIT,to:E.UPDATE_SOURCE_INIT},{from:u.STATE_CHANGE,to:E.APPLICATION_STATE_CHANGE},{from:u.STREAM_URL,to:E.STREAM_URL},{from:u.STREAM_SWITCH,to:E.STREAM_SWITCH},{from:s.CONFIG,to:E.CONFIG},{from:s.UPDATE_SOURCE,to:E.UPDATE_SOURCE},{from:s.SWITCH_STREAM,to:E.SWITCH_STREAM},{from:s.SET_ADAPTION,to:E.SET_ADAPTION},{from:s.NETWORK_PLAY,to:E.NETWORK_PLAY},{from:s.PLAY,to:E.PLAY}],F=[{type:E.CONFIG,listener:function(e){e.data.config.playback.metadata&&P.push(v.create(D,e.data.config))}}],k=[];function x(e){D.emit(E.PUBLIC_EVENT,{event:r.copy(e)})}return function(){for(var e in U.debugEnabled()&&U.debug("init"),c)Object.prototype.hasOwnProperty.call(c,e)&&-1===c.STATS.indexOf(c[e])&&k.push({type:c[e],listener:x});C=a.create(n,D,M),w=a.create(D,n,L),i.add({target:D,listeners:F}),i.add({target:n,listeners:k}),P.push(I.create(D)),P.push(N.create(D)),P.push(y.create(D)),P.push(A.create(D)),P.push(S.create(D)),P.push(R.create(D)),P.push(O.create(D))}(),{destroy:function(){for(C.destroy(),C=null,w.destroy(),w=null;P.length;)P.pop().destroy();i.remove({target:D,listeners:F}),i.remove({target:n,listeners:k})}}}}}.apply(t,r))||(e.exports=i)},6704:function(e,t,n){var r,i;r=[n(6637),n(9583),n(9673),n(594),n(680),n(1376),n(8333)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(n){var r=o.create("StateMachine"),a=[{type:i.STATE_CHANGE,listener:function(e){var r,i;r=t.STATE_CHANGE,i=e.data,n.emit(r,i)}}];return r.debugEnabled()&&r.debug("init"),e.add({target:n,listeners:a}),{destroy:function(){e.remove({target:n,listeners:a})}}}}}.apply(t,r))||(e.exports=i)},387:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(7771),n(3019),n(2706),n(6432),n(8405),n(492),n(3778),n(8730)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){return{create:function(c){var f,E,p,m,h,g,b,_=e.create("Connection"),T=a.create(_),y=o.create(),S=[],A={},v=s.UNITIALIZED,R=!1,O=!1,I=!1,N=!1,C=!0,w=!1,D=0,U=[{type:i.NETWORK_STATE_CHANGE,listener:function(e){_.debug(e.name+": network state change from "+B(v)+" to "+B(e.data.connectionState)),v=e.data.connectionState}},{type:i.SERVER_INFO,listener:function(e){if(g=e.data.onServerInfo,O){var t=g.capabilities,n=g.serverVersion,i=parseFloat(n);-1===t.indexOf("onPlay")&&-1===t.indexOf("onPause")&&(O=!1,c.emit(r.NO_KEEP_CONNECTION,{message:i<1.8?"The keepConnection feature is enabled by the players config, but is not supported by this server version ("+n+"). Use at least server version 1.8.0.0. The feature will be disabled.":"The keepConnection feature is enabled by the players config, but is not enabled by this server ("+n+"). The feature will be disabled."}),C||M())}c.emit(r.SERVER_INFO,e.data)}},{type:i.UPDATE_SOURCE_SUCCESS,listener:P},{type:i.UPDATE_SOURCE_FAIL,listener:function(e){S.length>1&&("update"===e.data.type?S.shift():S.pop()),"update"===e.data.type?c.emit(r.UPDATE_SOURCE_FAIL,{source:p.source,entry:p.entry,rule:p.rule,tag:e.data.tag,count:e.data.count,code:e.data.code,message:e.data.message,type:e.data.type,id:e.data.id}):c.emit(r.SWITCH_STREAM_FAIL,{source:p.source,entry:p.entry,rule:p.rule,tag:e.data.tag,count:e.data.count,code:e.data.code,message:e.data.message,type:e.data.type,id:e.data.id}),e.data.code===d.NETWORK.SOURCE_TIMEOUT&&V(p),_.debugEnabled()&&_.debug("set updating to false in onUpdateSourceFail"),H(!1)}},{type:i.UPDATE_SOURCE_ABORT,listener:function(e){var t=!1;S.length>1&&("update"===e.data.type?S.shift():S.pop()),"superseded"===e.data.reason&&m&&(t=!0),"update"===e.data.type?"equalsource"===e.data.reason?c.emit(r.UPDATE_SOURCE_SUCCESS,{source:p.source,entry:p.entry,rule:p.rule,tag:e.data.tag,count:e.data.count,type:e.data.type,id:e.data.id}):c.emit(r.UPDATE_SOURCE_ABORT,{source:t?m.source:p.source,entry:t?m.entry:p.entry,rule:t?m.rule:p.rule,tag:e.data.tag,count:e.data.count,reason:e.data.reason,type:e.data.type,id:e.data.id}):c.emit(r.SWITCH_STREAM_ABORT,{source:I?e.data.source:p.source,entry:I?e.data.entry:p.entry,rule:I?e.data.rule:p.rule,tag:e.data.tag,count:e.data.count,reason:"equalsource"===e.data.reason?"equal":e.data.reason,type:e.data.type,id:e.data.id}),"recover"===e.data.reason?m?(_.debugEnabled()&&_.debug("set updating to updatingPrevious in onUpdateSourceAbort"),H(m),G(!1)):(_.debugEnabled()&&_.debug("recover abort without updatePrevious, do nothing"),_.debugEnabled()&&_.debug("updating in abort is "+JSON.stringify(p))):t?G(!1):(_.debugEnabled()&&_.debug("set updating to false in onUpdateSourceAbort"),H(!1))}},{type:i.CONFIG,listener:function(e){E=e.data.config,f=E.id,R=E.playback.metadata,O=E.playback.keepConnection,C=E.playback.autoplay,I=t.mustUseHLS||E.playback.allowSafariHlsFallback&&t.canUseHLS,N=E.playback.faststart}},{type:i.STREAM_SWITCH,listener:function(e){var t=e.data.entry,n=e.data.options;if("init"===e.data.type){for(;S.length;)S.pop();return F(t.h5live),void(O&&L())}D+=1,n.tag=n.tag||"",n.count=D,"update"===e.data.type?c.emit(r.UPDATE_SOURCE_INIT,{source:e.data.source,entry:t,rule:e.data.rule,options:n,tag:n.tag,count:n.count,type:e.data.type,id:e.data.id}):c.emit(r.SWITCH_STREAM_INIT,{source:e.data.source,entry:t,rule:e.data.rule,options:n,tag:n.tag,count:n.count,type:e.data.type,id:e.data.id}),w?"update"===e.data.type?c.emit(r.UPDATE_SOURCE_ABORT,{source:e.data.source,entry:t,rule:e.data.rule,reason:"frequency",tag:n.tag,count:n.count,type:e.data.type,id:e.data.id}):c.emit(r.SWITCH_STREAM_ABORT,{source:e.data.source,entry:t,rule:e.data.rule,reason:"frequency",tag:n.tag,count:n.count,type:e.data.type,id:e.data.id}):!I||!p||"update"!==p.type&&"recover"!==p.type||"update"===e.data.type?(w=!0,setTimeout((function(){w=!1}),500),p&&G(p),_.debugEnabled()&&_.debug("set updating to e.data in onStreamSwitch"),H(e.data),"update"===e.data.type?_.debugEnabled()&&_.debug("update source: "+JSON.stringify(e.data.source)):_.debugEnabled()&&_.debug("switch stream: "+JSON.stringify(t)),S.length>1&&S.pop(),F(t.h5live),O&&!C?(_.debugEnabled()&&_.debug("connect to new source with terminating the old one"),M(!0),S.shift(),P({data:{tag:e.data.options.tag,count:D,type:e.data.type,id:e.data.id}}),L()):C?(_.debugEnabled()&&_.debug("connect to new source without terminating the old one"),L(e.data)):(S.shift(),_.debugEnabled()&&_.debug("replaced source"),P({data:{tag:e.data.options.tag,count:D,type:e.data.type,id:e.data.id}}))):c.emit(r.SWITCH_STREAM_ABORT,{source:e.data.source,entry:t,rule:e.data.rule,reason:"recover"===p.type?"recover":"updateProgress",tag:n.tag,count:n.count,type:e.data.type,id:e.data.id})}},{type:i.API_PLAY,listener:function(){if(_.debugEnabled()&&_.debug("api play"),C=!0,O?(_.debugEnabled()&&_.debug("send network play"),c.emit(r.NETWORK_PLAY)):L(p&&p.options.forcePlay?p:void 0),I){var e=k("hls");c.emit(r.VIDEO_SOURCE,{src:e,type:l.HLS})}}},{type:i.API_PAUSE,listener:function(){_.debugEnabled()&&_.debug("api pause"),C=!1,(!O||p)&&M(),I&&c.emit(r.VIDEO_SOURCE,{src:"",type:l.HLS})}},{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case u.PAUSED:p&&P({data:{tag:p.options.tag,count:p.options.count,type:p.type,id:p.id}})}}},{type:i.CAN_PLAY,listener:function(){p&&I&&(_.debugEnabled()&&_.debug("updating onCanPlay is "+JSON.stringify(p)),clearTimeout(b),P({data:{tag:p.options.tag,count:p.options.count,type:p.type,id:p.id}}))}}];function P(e){S.length>1&&S.shift(),h&&h.id===e.data.id&&(_.debugEnabled()&&_.debug("set updating to updatingTimeoutObject in onUpdateSourceSuccess"),H(h),V(null)),"update"===e.data.type?c.emit(r.UPDATE_SOURCE_SUCCESS,{source:p.source,entry:p.entry,rule:p.rule,tag:e.data.tag,count:e.data.count,type:e.data.type,id:e.data.id}):c.emit(r.SWITCH_STREAM_SUCCESS,{source:p.source,entry:p.entry,rule:p.rule,tag:e.data.tag,count:e.data.count,type:e.data.type,id:e.data.id}),_.debugEnabled()&&_.debug("set updating to false in onUpdateSourceSuccess"),H(!1),clearTimeout(b)}function L(e){var t,n=e?S.length-1:0;if(R||S[n].security||!I){x(!C,"paused"),x(R&&I,"metastreamonly"),x(!R&&!!S[n].security&&I,"checkandclose"),x(e?e.faststart:N,"faststart"),t=k("websocket",n);var i=e?r.NETWORK_UPDATE_SOURCE:r.CONNECT;_.debugEnabled()&&_.debug("connect to: "+t,1),c.emit(i,{url:t,config:S[n],update:e||!1,reconnect:E.playback.reconnect,timeouts:E.playback.timeouts}),I||c.emit(r.URL,{url:t})}I&&(x(e?e.faststart:N,"faststart"),t=k("hls"),c.emit(r.URL,{url:t}),c.emit(r.VIDEO_SOURCE,{src:t,type:l.HLS}))}function M(e){_.debugEnabled()&&_.debug("disconnecting"),c.emit(r.DISCONNECT,{silent:!!e})}function F(e){var n=e.rtmp,i=e.params;T.initConfig().setServer(e.server).setToken(e.token).setQueryParams(i).setSecurity(e.security),T.connectionConfig.server.websocket.length||T.connectionConfig.server.hls.length||c.emit(r.NO_SERVER_ERROR),"object"==typeof n&&n.url&&n.url.length&&n.streamname&&n.streamname.length||i?T.setRtmp(n):c.emit(r.MISSING_RTMP_ERROR),(t.isIOSDesktopMode||t.isIOS)&&T.addQueryParam("os","ios"),S.push(T.connectionConfig)}function k(e,t){var n=void 0!==typeof t&&t>-1&&void 0!==typeof S[t]?t:S.length-1,r=S[n];return y.initURL(r,e).processConfigFlags(A).processConfigQuery(f).processSecurity().buildConnectionURL(),y.connectionURL}function x(e,t){!0===e&&void 0===A[t]?A[t]=!0:!1===e&&A[t]&&delete A[t]}function B(e){for(var t in s)if(Object.prototype.hasOwnProperty.call(s,t)&&s[t]===e)return t}function H(e){_.debugEnabled()&&_.debug("set updating to "+JSON.stringify(e)+" from "+JSON.stringify(p)),p=e}function G(e){_.debugEnabled()&&_.debug("set updatingPrevious to "+JSON.stringify(e)+" from "+JSON.stringify(m)),m=e}function V(e){_.debugEnabled()&&_.debug("set updatingTimeoutObject to "+JSON.stringify(e)+" from "+JSON.stringify(h)),h=e}return _.debugEnabled()&&_.debug("init"),n.add({target:c,listeners:U}),{destroy:function(){n.remove({target:c,listeners:U})}}}}}.apply(t,r))||(e.exports=i)},3445:function(e,t,n){var r,i;r=[n(8333),n(6627),n(9474),n(7146),n(561)],void 0===(i=function(e,t,n,r,i){return{create:function(a,o){var s,u,d,l,c,f=e.create("AdjustControllerAdapter"),E={default:r,fastadaptive:i,balancedadaptive:i};return f.debugEnabled()&&f.debug("init"),d=o.playback.latencyControlMode,l=t.mustUseHLS||o.playback.allowSafariHlsFallback&&t.canUseHLS,c=t.isTridentBrowser,u="default",d!==n.FAST_ADAPTIVE||l||c?d!==n.BALANCED_ADAPTIVE||l||c||(u="balancedadaptive"):u="fastadaptive",f.debugEnabled()&&f.debug("use adjustController type "+u),s=E[u].create(a,d),{calcBufferGoal:function(e){return s.calcBufferGoal(e)},destroy:function(){s.destroy(),s=null}}}}}.apply(t,r))||(e.exports=i)},561:function(e,t,n){var r,i;r=[n(8333),n(6637),n(492),n(9474),n(4234)],void 0===(i=function(e,t,n,r,i){return{create:function(a,o){var s,u=e.create("AdaptiveAdjustController"),d={real:0,base:0,min:0,max:0},l=0,c=0,f=0,E=0,p=0,m=1e3,h=0,g=0,b=0,_=0,T=.998,y=0,S=0,A={fastadaptive:{minClamp:.3,range:.4},balancedadaptive:{minClamp:.5,range:.7}},v=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case n.PAUSED:l=0,c=0,f=0,E=0,p=0,m=1e3,h=0,g=0,b=0,_=0;break;case n.BUFFERING:case n.PLAYING:}}},{type:i.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer,d.base||(d.base=s.start),d.min=Math.max(s.min+.1+.125*(s.min-.2),y),d.max=d.min+S}}];return u.debugEnabled()&&u.debug("init"),t.add({target:a,listeners:v}),Object.prototype.hasOwnProperty.call(A,o)||(o=r.FAST_ADAPTIVE,u.debugEnabled()&&u.debug("use default mode fast")),y=A[o].minClamp,S=A[o].range,u.debugEnabled()&&u.debug("use mode "+o),{destroy:function(){t.remove({target:a,listeners:v})},calcBufferGoal:function(e){if(f=e.stats.buffer.end,E=window.performance.now(),_<8)d.base=d.min,_++;else{l&&c&&(p=p*T+(1e3*(f-l)-(E-c)),m=Math.min(m,p)*T,h=Math.max(h,p)*T,g=(h-m)/2,b=Math.max(Math.abs(m),Math.abs(g))),c=E,l=f;var t=s.min+Math.abs(2*b)/1e3;t=Math.min(d.max,t),t=Math.max(d.min,t),t=Math.round(100*t)/100,d.base=t}return d}}}}}.apply(t,r))||(e.exports=i)},7146:function(e,t,n){var r,i;r=[n(8333),n(6637),n(492),n(5337),n(4234)],void 0===(i=function(e,t,n,r,i){return{create:function(r){var a,o=e.create("DefaultAdjustController"),s={real:0,base:0,min:0,max:0},u=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case n.PAUSED:case n.BUFFERING:case n.PLAYING:}}},{type:i.BUFFER_TWEAKS_CREATED,listener:function(e){a=e.data.tweaks.buffer,s.base||(s.base=a.target)}}];return o.debugEnabled()&&o.debug("init"),t.add({target:r,listeners:u}),{destroy:function(){t.remove({target:r,listeners:u})},calcBufferGoal:function(){return s}}}}}.apply(t,r))||(e.exports=i)},173:function(e,t,n){var r,i;r=[n(8333)],void 0===(i=function(e){return{create:function(t,n){var r,i,a=e.create("HlsConservativeLatencyController"),o=1+1e-7,s=o;return a.debugEnabled()&&a.debug("init"),{checkLatency:function(e,t){return r=e.stats,s=e.playbackRate,i=t.base,r.currentTime>=3&&(r.buffer.update.avg>0&&r.buffer.update.avg+1.7-.7<r.buffer.delay.avg?n.setRate(s,1.1):r.buffer.update.avg>0&&r.buffer.update.avg+.2<r.buffer.delay.avg<=r.buffer.update.avg+i-.7?n.setRate(s,o):r.buffer.update.avg>=0&&r.buffer.update.avg>=r.buffer.delay.avg&&n.setRate(s,.9)),i},destroy:function(){n.destroy(),n=null}}}}}.apply(t,r))||(e.exports=i)},7650:function(e,t,n){var r,i;r=[n(8333),n(6637),n(4234)],void 0===(i=function(e,t,n){return{create:function(r,i){var a,o,s,u=e.create("HlsConservativeLatencyController"),d=1+1e-7,l=d,c=[{type:n.BUFFER_TWEAKS_CREATED,listener:function(e){o=e.data.tweaks.buffer}}];return u.debugEnabled()&&u.debug("init"),t.add({target:r,listeners:c}),{checkLatency:function(e,t){return a=e.stats,l=e.playbackRate,s=t.base,o.limit<a.buffer.delay.avg?i.setRate(l,1.1):o.start<a.buffer.delay.avg&&a.buffer.delay.avg<=s?i.setRate(l,d):o.min>=a.buffer.delay.avg&&i.setRate(l,.9),s},destroy:function(){t.remove({target:r,listeners:c}),i.destroy(),i=null}}}}}.apply(t,r))||(e.exports=i)},3184:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234)],void 0===(i=function(e,t,n,r){return{create:function(i,a){var o,s,u,d,l,c,f,E=e.create("WSSChromiumProgressiveLatencyController"),p=0,m=0,h=1,g=0,b=0,_=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case t.PLAYING:break;case t.PAUSED:S()}}},{type:r.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer}}];function T(e){var t=0;return e&&f?(t=(window.performance.now()-f)/1e3,f=0):e||f||(f=window.performance.now()),t}function y(){return 1===h}function S(){p=0,clearTimeout(c),c=0}return E.debugEnabled()&&E.debug("init"),S(),n.add({target:i,listeners:_}),{checkLatency:function(e,t){if(o=e.stats,h=e.playbackRate,g=e.currentTime,u=t.base,l=1,m=o.buffer.delay.avg,b=Math.round(1e3*(+y()?o.buffer.delay.max-m:m-o.buffer.delay.min))/1e3,d=u+b,p=o.buffer.end>p+.1?o.buffer.end:p,s.max<m&&o.buffer.start<g)E.debug("max buffer reached, seeking to goal..."),a.setSeek(g,p-u);else if(s.limit<m)1.1!==h&&(E.debugEnabled()&&E.debug("avg over limit, set speed to 1.1"),l=1.1,T(),a.setRate(h,l));else if(y()&&d+.1<m||!y()&&d<m){if(!c){var n=1e3*(f?2+2*Math.random():.5+1.5*Math.random());E.debugEnabled()&&E.debug("avg over bufferGoalReal but under limit, make check in "+n+"ms"),c=setTimeout((function(){E.debugEnabled()&&E.debug("check timeout fired"),c=0,y()&&d+.1<m||!y()&&d<m?f||(T(),l=function(){var e=s.limit-d,t=Math.max(0,Math.min(e,o.buffer.delay.avg-d)),n=Math.round(t/e*100),r=1+Math.round(.1*Math.sqrt(n/100)*100)/100;return E.debugEnabled()&&E.debug("avg at "+n+"% between target and limit",2),E.debugEnabled()&&E.debug("after check timeout still avg with "+n+"% over target, set speed to "+r+" with sqrt function",2),r}(),a.setRate(h,l)):E.debugEnabled()&&E.debug("after check timeout avg not over bufferGoalReal, do nothing",2)}),n)}}else if(d>=m&&1!==h){var r=T(!0);r&&E.debugEnabled()&&E.debug("normalize speed after "+r.toFixed(2)+" seconds",2),l=1,a.setRate(h,l)}return d},destroy:function(){n.remove({target:i,listeners:_}),a.destroy(),a=null}}}}}.apply(t,r))||(e.exports=i)},2531:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234)],void 0===(i=function(e,t,n,r){return{create:function(i,a){var o,s,u,d,l,c,f=e.create("WSSDefaultConservativeLatencyController"),E=0,p=0,m=1,h=0,g=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case t.PLAYING:break;case t.PAUSED:_()}}},{type:r.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer}}];function b(e){var t=0;return e&&c?(t=(window.performance.now()-c)/1e3,c=0):e||c||(c=window.performance.now()),t}function _(){E=0}return f.debugEnabled()&&f.debug("init"),_(),n.add({target:i,listeners:g}),{checkLatency:function(e,t){if(o=e.stats,m=e.playbackRate,h=e.currentTime,u=t.base,l=1,p=o.buffer.delay.avg,d=u+0,E=o.buffer.end>E+.1?o.buffer.end:E,s.max<p&&o.buffer.start<h)f.debug("max buffer reached, seeking to goal..."),a.setSeek(h,E-u);else if(s.limit<p)1.1!==m&&(f.debugEnabled()&&f.debug("avg over limit, set speed to 1.1"),l=1.1,b(),a.setRate(m,l));else if(d>=p&&1!==m){var n=b(!0);n&&f.debugEnabled()&&f.debug("normalize speed after "+n.toFixed(2)+" seconds",2),l=1,a.setRate(m,l)}return d},destroy:function(){n.remove({target:i,listeners:g}),a.destroy(),a=null}}}}}.apply(t,r))||(e.exports=i)},8538:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234)],void 0===(i=function(e,t,n,r){return{create:function(i,a){var o,s,u,d,l,c,f,E,p,m=e.create("WSSFirefoxProgressiveLatencyController"),h=0,g=0,b=1,_=0,T=0,y=0,S=0,A=!1,v=!1,R=1,O=0,I=6e4,N=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case t.PLAYING:break;case t.PAUSED:F()}}},{type:r.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer}},{type:r.FRAME_DROP,listener:function(e){var t=e.data.type;switch(m.debugEnabled()&&m.debug("framedrop event with type "+t),t){case"extreme":case"high":case"medium":v?m.debugEnabled()&&m.debug("frame drop handle blocked"):(m.debugEnabled()&&m.debug("frame drop handle unblocked, starting"),U(!0,"framedrop event with type "+t),A?(m.debugEnabled()&&m.debug("frame drop handle still activated, restart cooldown timeout"),P()):(m.debugEnabled()&&m.debug("set buffer goal to buffer goal max"),T=d,m.debugEnabled()&&m.debug("check framedrop cooldown factor timeout"),S?(m.debugEnabled()&&m.debug("framedrop cooldown factor timeout is running, clearing"),M(),R<=4?(m.debugEnabled()&&m.debug("framedrop cooldown factor lower or 4, multiply"),R*=2,m.debugEnabled()&&m.debug("new framedrop cooldown factor is "+R.toString())):m.debugEnabled()&&m.debug("framedrop cooldown factor still at max ("+R.toString()+"), do nothing")):m.debugEnabled()&&m.debug("framedrop cooldown factor timeout is not running, use current factor ("+R.toString()+")")))}}}];function C(e){var t=0;return e&&E?(t=(window.performance.now()-E)/1e3,E=0):e||E||(E=window.performance.now()),t}function w(){return 1===b}function D(){return b>1}function U(e,t){m.debugEnabled()&&m.debug("set framedrop blocked to "+e.toString()+" from handle: '"+t+"'"),e?v=!0:(v=!1,P())}function P(){L(),M(),m.debugEnabled()&&m.debug("set framedrop cooldown timeout with "+(I*R/1e3).toString()+" s"),y=setTimeout((function(){m.debugEnabled()&&m.debug("framedrop cooldown timeout reached, set handle to inactive"),L(),A=!1,M(),m.debugEnabled()&&m.debug("set framedrop cooldown factor timeout with "+(I*R/1e3).toString()+" s"),S=setTimeout((function(){m.debugEnabled()&&m.debug("framedrop cooldown factor timeout reached, check for decrease cooldown factor"),M(),R>1?(m.debugEnabled()&&m.debug("framedrop cooldown factor higher 1, divide"),R/=2,m.debugEnabled()&&m.debug("new framedrop cooldown factor is "+R.toString())):m.debugEnabled()&&m.debug("framedrop cooldown factor still at min ("+R.toString()+"), do nothing")}),I*R)}),I*R)}function L(){m.debugEnabled()&&m.debug("clear framedrop cooldown timeout and set to 0"),clearTimeout(y),y=0}function M(){m.debugEnabled()&&m.debug("clear framedrop cooldown factor timeout and set to 0"),clearTimeout(S),S=0}function F(){h=0,clearTimeout(f),f=0,clearTimeout(y),y=0,clearTimeout(S),S=0,A=!1,v=!1,R=1}return m.debugEnabled()&&m.debug("init"),F(),n.add({target:i,listeners:N}),{checkLatency:function(e,t){if(o=e.stats,b=e.playbackRate,_=e.currentTime,u=t.base,d=t.max,c=1,g=o.buffer.delay.avg,O=Math.round(1e3*(D()?o.buffer.delay.max-g:g-o.buffer.delay.min))/1e3,v||A||(T*=.998),l=Math.max(u,T)+O,h=o.buffer.end>h+.1?o.buffer.end:h,s.max<g&&o.buffer.start<_)v&&U(!1,"over buffer max"),m.debug("max buffer reached, seeking to goal..."),a.setSeek(_,h-u);else if(s.limit<g)v&&U(!1,"between buffer limit and max"),1.1!==b&&(m.debugEnabled()&&m.debug("avg over limit, set speed to 1.1"),c=1.1,C(),a.setRate(b,c));else if(w()&&l+.1<g||D()&&l<g){if(!f){var n=1e3*(E?2+2*Math.random():.5+1.5*Math.random());m.debugEnabled()&&m.debug("avg over bufferGoalReal but under limit, make check in "+n+"ms"),f=setTimeout((function(){m.debugEnabled()&&m.debug("check timeout fired"),f=0,w()&&l+.1<g||D()&&l<g?E||(C(),c=function(){var e=s.limit-l,t=Math.max(0,Math.min(e,o.buffer.delay.avg-l)),n=Math.round(t/e*100),r=1+Math.round(.1*Math.sqrt(n/100)*100)/100;return m.debugEnabled()&&m.debug("avg at "+n+"% between target and limit"),m.debugEnabled()&&m.debug("after check timeout still avg with "+n+"% over target, set speed to "+r+" with sqrt function"),r}(),a.setRate(b,c)):m.debugEnabled()&&m.debug("after check timeout avg not over bufferGoalReal, do nothing")}),n)}}else!v&&l>=g&&D()?((p=C(!0))&&m.debugEnabled()&&m.debug("normalize speed after "+p.toFixed(2)+" seconds"),c=1,a.setRate(b,c)):v&&A&&l<=g+.1?((p=C(!0))&&m.debugEnabled()&&m.debug("normalize speed after "+p.toFixed(2)+" seconds"),m.debugEnabled()&&m.debug("normalize speed after reached bufferGoalReal again after frame drop"),v&&U(!1,"buffer target reached after framedrop"),c=1,a.setRate(b,c)):v&&!A&&(m.debugEnabled()&&m.debug("slow speed to reach bufferGoalReal after frame drop"),C(),c=.97,a.setRate(b,c),A=!0);return l},destroy:function(){n.remove({target:i,listeners:N}),a.destroy(),a=null}}}}}.apply(t,r))||(e.exports=i)},9321:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234)],void 0===(i=function(e,t,n,r){return{create:function(i,a){var o,s,u,d,l=e.create("WSSTridentConservativeLatencyController"),c=0,f=0,E=0,p=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case t.PLAYING:break;case t.PAUSED:m()}}},{type:r.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer}}];function m(){c=0}return l.debugEnabled()&&l.debug("init"),m(),n.add({target:i,listeners:p}),{checkLatency:function(e,t){return o=e.stats,E=e.currentTime,u=t.base,f=o.buffer.delay.avg,d=u+0,c=o.buffer.end>c+.1?o.buffer.end:c,s.max<f&&o.buffer.start<E?(l.debug("max buffer reached, seeking to goal..."),a.setSeek(E,c-u)):s.limit<f&&(l.debugEnabled()&&l.debug("seek to buffer goal"),a.setSeek(E,c-d)),d},destroy:function(){n.remove({target:i,listeners:p})}}}}}.apply(t,r))||(e.exports=i)},4422:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234)],void 0===(i=function(e,t,n,r){return{create:function(i,a){var o,s,u,d,l,c,f,E=e.create("WSSWebkitProgressiveLatencyController"),p=0,m=0,h=1,g=0,b=0,_=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case t.PLAYING:break;case t.PAUSED:S()}}},{type:r.BUFFER_TWEAKS_CREATED,listener:function(e){s=e.data.tweaks.buffer}}];function T(e){var t=0;return e&&f?(t=(window.performance.now()-f)/1e3,f=0):e||f||(f=window.performance.now()),t}function y(){return 1===h}function S(){p=0,clearTimeout(c),c=0}return E.debugEnabled()&&E.debug("init"),S(),n.add({target:i,listeners:_}),{checkLatency:function(e,t){if(o=e.stats,h=e.playbackRate,g=e.currentTime,u=t.base,l=1,m=o.buffer.delay.avg,b=Math.round(1e3*(+y()?o.buffer.delay.max-m:m-o.buffer.delay.min))/1e3,d=u+b,p=o.buffer.end>p+.1?o.buffer.end:p,s.max<m&&o.buffer.start<g)E.debug("max buffer reached, seeking to goal..."),a.setSeek(g,p-u);else if(s.limit<m||s.limit<m+.25&&1.1===h)1.1!==h&&(E.debugEnabled()&&E.debug("avg over limit, set speed to 1.1"),l=1.1,T(),a.setRate(h,l));else if(y()&&d+.25<m||!y()&&d<m&&s.min+.05<m){if(!c){var n=1e3*(f?2+2*Math.random():.5+1.5*Math.random());E.debugEnabled()&&E.debug("avg over bufferGoalReal but under limit, make check in "+n+"ms"),c=setTimeout((function(){E.debugEnabled()&&E.debug("check timeout fired"),c=0,d<m&&s.min+.05<m?(T(),l=1.05,a.setRate(h,l)):E.debugEnabled()&&E.debug("after check timeout avg not over bufferGoalReal, do nothing",2)}),n)}}else if((d-.15>=m||s.min+.05>=m)&&1!==h){var r=T(!0);r&&E.debugEnabled()&&E.debug("normalize speed after "+r.toFixed(2)+" seconds",2),l=1,a.setRate(h,l)}return d},destroy:function(){n.remove({target:i,listeners:_})}}}}}.apply(t,r))||(e.exports=i)},1148:function(e,t){var n;void 0===(n=function(){var e="adjustLatencyHandler.";return{BASE:e,SEEK:e+"seek",RATE:e+"rate"}}.apply(t,[]))||(e.exports=n)},4234:function(e,t){var n;void 0===(n=function(){var e="latencyManager.";return{BASE:e,BUFFER_TWEAKS_CREATED:e+"bufferTweakscreated",FRAME_DROP:e+"frameDrop",RANDOM_ACCESS_POINT:e+"randomAccessPoint",TIME_OFFSET:e+"timeOffset",CONFIG:e+"config",PLAY_STATS:e+"playStats",APPLICATION_STATE_CHANGE:e+"applicationStateChange"}}.apply(t,[]))||(e.exports=n)},2158:function(e,t){var n;void 0===(n=function(){var e="liveController.";return{BASE:e,BUFFER_GOAL_STATS:e+"bufferGoalStats"}}.apply(t,[]))||(e.exports=n)},4285:function(e,t,n){var r,i;r=[n(8333),n(6627),n(9474),n(8930),n(2531),n(9321),n(3184),n(8538),n(4422),n(7650),n(173)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){return{create:function(c,f){var E,p,m,h,g,b,_,T,y,S=e.create("LatencyControllerAdapter"),A={wssdefaultconservative:i,wsstridentconservative:a,wsschromiumprogressive:o,wssfirefoxprogressive:s,wsswebkitprogressive:u,hlsconservative:d,hlsautoconservative:l},v="wssdefaultconservative",R="wsstridentconservative",O="hlsconservative",I="hlsautoconservative",N="wsschromiumprogressive",C="wssfirefoxprogressive",w="wsswebkitprogressive";return S.debugEnabled()&&S.debug("init"),g=t.mustUseHLS||f.playback.allowSafariHlsFallback&&t.canUseHLS,b=!f.tweaks||!f.tweaks.buffer||0===Object.keys(f.tweaks.buffer).length,h=f.playback.latencyControlMode,_=t.isTridentBrowser,T=t.isWebkitBrowser,y=t.isFirefox,m=r.create(c),p=v,g&&b?p=I:g&&!b?p=O:_?p=R:h!==n.FAST_ADAPTIVE&&h!==n.BALANCED_ADAPTIVE||!T?h!==n.FAST_ADAPTIVE&&h!==n.BALANCED_ADAPTIVE||!y?h!==n.FAST_ADAPTIVE&&h!==n.BALANCED_ADAPTIVE||(p=N):p=C:p=w,S.debugEnabled()&&S.debug("use latencyController type "+p),E=A[p].create(c,m),{checkLatency:function(e,t){return E.checkLatency(e,t)},destroy:function(){E.destroy(),E=null,m.destroy(),m=null}}}}}.apply(t,r))||(e.exports=i)},7831:function(e,t,n){var r,i;r=[n(8333),n(9658),n(6627),n(8197),n(6637),n(5055),n(3937),n(3019),n(4234),n(1148),n(2158),n(8449)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c){"use strict";return{create:function(n){var r,i,f=new t,E=e.create("LatencyManager"),p=[],m=[{from:d.SEEK,to:o.SEEK},{from:d.RATE,to:o.RATE},{from:l.BUFFER_GOAL_STATS,to:o.BUFFER_GOAL_STATS}],h=[{from:s.BUFFER_TWEAKS_CREATED,to:u.BUFFER_TWEAKS_CREATED},{from:s.FRAME_DROP,to:u.FRAME_DROP},{from:s.RANDOM_ACCESS_POINT,to:u.RANDOM_ACCESS_POINT},{from:s.TIME_OFFSET,to:u.TIME_OFFSET},{from:s.CONFIG,to:u.CONFIG},{from:s.PLAY_STATS,to:u.PLAY_STATS},{from:s.APPLICATION_STATE_CHANGE,to:u.APPLICATION_STATE_CHANGE}];return E.debugEnabled()&&E.debug("init"),r=a.create(n,f,h),i=a.create(f,n,m),p.push(c.create(f)),{destroy:function(){for(r.destroy(),r=null,i.destroy(),i=null;p.length;)p.pop().destroy()}}}}}.apply(t,r))||(e.exports=i)},8449:function(e,t,n){var r,i;r=[n(8333),n(492),n(6637),n(4234),n(2158),n(3445),n(4285)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s){var u,d,l,c,f=e.create("LiveController"),E=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(n.remove({target:s,listeners:p}),e.data.state){case t.LOADING:case t.PLAYING:case t.BUFFERING:n.add({target:s,listeners:p})}}},{type:r.CONFIG,listener:function(e){l=a.create(s,e.data.config),c=o.create(s,e.data.config)}}],p=[{type:r.PLAY_STATS,listener:function(e){u=e.data,(d=l.calcBufferGoal(u)).real=c.checkLatency(u,d),s.emit(i.BUFFER_GOAL_STATS,{real:Math.round(100*d.real)/100,base:Math.round(100*d.base)/100,min:Math.round(100*d.min)/100,max:Math.round(100*d.max)/100})}}];return f.debugEnabled()&&f.debug("init"),n.add({target:s,listeners:E}),{destroy:function(){n.remove({target:s,listeners:E}),l.destroy(),c.destroy()}}}}}.apply(t,r))||(e.exports=i)},8930:function(e,t,n){var r,i;r=[n(8333),n(6627),n(492),n(6637),n(4234),n(1148)],void 0===(i=function(e,t,n,r,i,a){return{create:function(o){var s=e.create("AdjustLatencyHandler"),u=[],d=0,l=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case n.PLAYING:break;case n.PAUSED:c()}}},{type:i.RANDOM_ACCESS_POINT,listener:function(e){for(u.push(e.data.onRandomAccessPoint.streamTime/1e3+d/1e3);u.length>10;)u.shift()}},{type:i.TIME_OFFSET,listener:function(e){d=e.data.offset}}];function c(){for(;u.length;)u.shift();d=0}return s.debugEnabled()&&s.debug("init"),c(),r.add({target:o,listeners:l}),{setSeek:function(e,n){s.debugEnabled()&&s.debug("should seeking to "+n),t.isTridentBrowser&&(n=function(e,t){for(var n=0;n<u.length;++n)if(u[n]>e&&u[n]<=t)return u[n];return-1}(e,n),s.debugEnabled()&&s.debug("seeking to RAP "+n)),n>=0&&(s.debugEnabled()&&s.debug("seeking to "+n),o.emit(a.SEEK,{position:n}))},setRate:function(e,t){Math.abs(e-t)>1e-7&&(s.debugEnabled()&&s.debug("set rate to "+t),o.emit(a.RATE,{rate:t}))},destroy:function(){c(),r.remove({target:o,listeners:l})}}}}}.apply(t,r))||(e.exports=i)},9665:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(3778),n(492),n(3019),n(7771),n(1683)],void 0===(i=function(e,t,n,r,i,a,o,s){return{create:function(u,d){var l,c,f,E,p,m,h,g,b,_,T,y,S,A=e.create("MetaData"),v=0,R=[{type:a.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case i.LOADING:I(),n.add({target:u,listeners:O});break;case i.PAUSING:n.remove({target:u,listeners:O})}}},{type:a.TIME_OFFSET,listener:function(e){v=e.data.offset}},{type:a.STREAM_INFO,listener:function(){I()}},{type:o.UPDATE_SOURCE_SUCCESS,listener:function(){I()}},{type:o.SWITCH_STREAM_SUCCESS,listener:function(){I()}}],O=[{type:a.META_DATA,listener:function(e){A.debugEnabled()&&A.debug("onMetaData");var t,n,i=e.data[e.data.eventType];if(1===i.chunkIndex)"function"==typeof E.message.push&&0!==E.message.length&&u.emit(s.ERROR,{code:r.STREAM.METADATA_STILL_PROCESSING,message:"Received metadata with start index but currently process another. Discard old one."}),f=!0,t=l&&c?_+T+.25:i.streamTime+v/1e3,n=isNaN(n)?3:parseInt(n,10),y=Math.floor(t*Math.pow(10,n))/Math.pow(10,n),S=i.handlerName,E={handlerName:S,streamTime:y,message:[]};else{if(0===E.message.length||"function"!=typeof E.message.push)return void(f&&(u.emit(s.ERROR,{code:r.STREAM.METADATA_NO_START_INDEX,message:"Received metadata but no start index. Discard."}),f=!1,E.message=[]));if(!f)return}if(E.message.push(i.message),E.message.length!==i.chunkIndex)f&&(u.emit(s.ERROR,{code:r.STREAM.METADATA_WRONG_INDEX,message:"Received metadata with wrong index. Discard."}),f=!1,E.message=[]);else if(i.chunkCount-i.chunkIndex==0)try{g=E.message.join(""),S=E.handlerName,A.debugEnabled()&&A.debug("handlerName="+S+", metaData="+g),g=JSON.parse(g),E.message=g,b={handlerName:E.handlerName,streamTime:E.streamTime,message:E.message},p.push(b)}catch(e){u.emit(s.ERROR,{code:r.STREAM.METADATA_INVALID_JSON,message:"Received metadata with invalid json string."})}}},{type:a.PLAY_STATS,listener:function(e){for(_=e.data.stats.currentTime,T=e.data.stats.buffer.delay.current;p.length;)(h=p.shift()).streamTime<=_?u.emit(s.RECEIVED,h):m.push(h);for(;m.length;)p.push(m.shift())}}];function I(){_=0,T=0,f=!0,E={handlerName:"",streamTime:0,serverTime:0,diffTime:0,message:[]},p=[],m=[]}return A.debugEnabled()&&A.debug("init"),l=t.mustUseHLS||d.playback.allowSafariHlsFallback&&t.canUseHLS,c=d.playback.metadataLowDelay,n.add({target:u,listeners:R}),{destroy:function(){n.remove({target:u,listeners:R})}}}}}.apply(t,r))||(e.exports=i)},6701:function(e,t,n){var r,i;r=[n(6637),n(8333),n(4762),n(708),n(5739),n(7844),n(6627)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(r){var s=t.create("Adaptive.AdaptionController"),u=!1,d=[],l=[{type:n.CONFIG,listener:function(e){var t=e.data.config.source.options.adaption.rule,n=e.data.config.source.options.adaption;u=o.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&o.canUseHLS,n.useHLS=u,f(t,n)}},{type:n.UPDATE_SOURCE,listener:function(e){var t=e.data.source.options.adaption.rule,n=e.data.source.options.adaption;n.useHLS=u,f(t,n)}},{type:n.SWITCH_STREAM,listener:function(){f("none")}},{type:n.SET_ADAPTION,listener:function(e){var t=e.data.adaption.rule,n=e.data.adaption;n.useHLS=u,f(t,n)}}],c={deviationOfMean:i,deviationOfMean2:a};function f(e,t){for(;d.length;)d.pop().destroy();Object.prototype.hasOwnProperty.call(c,e)&&d.push(c[e].create(r,t))}return s.debugEnabled()&&s.debug("initialize"),e.add({target:r,listeners:l}),{destroy:function(){for(;d.length;)d.pop().destroy();e.remove({target:r,listeners:l})}}}}}.apply(t,r))||(e.exports=i)},708:function(e,t){var n;void 0===(n=function(){var e="adaptionController.";return{BASE:e,SWITCH_SUGGESTION:e+"switchSuggestion"}}.apply(t,[]))||(e.exports=n)},8134:function(e,t){var n;void 0===(n=function(){var e="ressourceController.";return{BASE:e,STATS:e+"stats",BUFFERING:e+"buffering",INITIAL_SWITCH_UP:e+"initialSwitchUp",STREAM_INFO:e+"streamInfo",STREAM_QUALITY:e+"streamQuality",PAUSED:e+"paused",BUFFERING_LONG:e+"longBuffering"}}.apply(t,[]))||(e.exports=n)},3049:function(e,t,n){var r,i;r=[n(6637),n(8333),n(8197),n(4762),n(8134),n(3349),n(6400)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(n){var i=t.create("Adaptive.RessourceController"),s=[],u=[{type:r.CONFIG,listener:function(e){l(e.data.config.source.options.adaption.rule,e.data.config.source.options.adaption)}},{type:r.UPDATE_SOURCE,listener:function(e){l(e.data.source.options.adaption.rule,e.data.source.options.adaption)}},{type:r.SWITCH_STREAM,listener:function(){l("none")}},{type:r.SET_ADAPTION,listener:function(e){l(e.data.adaption.rule,e.data.adaption)}}],d={deviationOfMean:a,deviationOfMean2:o};function l(e,t){for(;s.length;)s.pop().destroy();Object.prototype.hasOwnProperty.call(d,e)&&s.push(d[e].create(n,t))}return i.debugEnabled()&&i.debug("initialize"),e.add({target:n,listeners:u}),{destroy:function(){for(;s.length;)s.pop().destroy();e.remove({target:n,listeners:u})}}}}}.apply(t,r))||(e.exports=i)},7844:function(e,t,n){var r,i;r=[n(6637),n(8333),n(4762),n(708),n(8134),n(4035)],void 0===(i=function(e,t,n,r,i,a){return{create:function(o){var s,u,d=t.create("Adaptive.DeviationOfMean2.Adaption"),l=!0,c=!1,f=!1,E=0,p=!1,m=!1,h=!1,g=[{type:n.UPDATE_SOURCE_SUCCESS,listener:v},{type:n.UPDATE_SOURCE_FAIL,listener:R},{type:n.UPDATE_SOURCE_ABORT,listener:R},{type:n.SWITCH_STREAM_SUCCESS,listener:v},{type:n.SWITCH_STREAM_FAIL,listener:R},{type:n.SWITCH_STREAM_ABORT,listener:R},{type:n.LOADED_DATA,listener:function(){d.debugEnabled()&&d.debug("onLoadedData"),m=!0}},{type:n.TIME_OFFSET,listener:function(){d.debugEnabled()&&d.debug("onFirstFragmentRendered"),h=!0}},{type:i.STATS,listener:function(e){p||v(!1),function(e){if(!l){d.debugEnabled()&&d.debug("making switch suggestion allowed");var t,n,i=e.buffer.delay.bufferDelaysStandardDeviationsDev.length;d.debugEnabled()&&d.debug("bufferDelaysStandardDeviationsLength >= 5 && !wasAbortedHighest ?"),i>=5&&!I()&&(d.debugEnabled()&&d.debug("bufferDelaysStandardDeviationsLength >= 5 && !wasAbortedHighest !"),t=y(e.buffer.delay.bufferDelaysStandardDeviationsDev,5),n=y(e.buffer.delay.bufferDelaysArithmeticMeans,5),d.debugEnabled()&&d.debug("bufferDevationAboveValue && bufferCurrentHasMinimumValue ?"),function(e,t){d.debugEnabled()&&d.debug("bufferDevationAboveValue with limit 0.045");for(var n=!0,r=0;r<=e.length-1;r++)if(!_(e[r],.045)){n=!1;break}return n}(t)&&function(e,t){for(var n=!1,r=0;r<=e.length-1;r++)if(b(e[r],.5)){n=!0;break}return n}(n)&&(d.debugEnabled()&&d.debug("make suggestion to switch down"),S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"down"}))),d.debugEnabled()&&d.debug("bufferDelaysStandardDeviationsLength >= 8 && !wasAbortedLowest ?"),i>=8&&!N()&&(d.debugEnabled()&&d.debug("bufferDelaysStandardDeviationsLength >= 8 && !wasAbortedLowest !"),function(e,t){d.debugEnabled()&&d.debug("bufferDevationBelowValue with limit 0.016");for(var n=!0,r=0;r<=e.length-1;r++)if(!b(e[r],.016)){n=!1;break}return n}(t=y(e.buffer.delay.bufferDelaysStandardDeviationsDev,8))&&(d.debugEnabled()&&d.debug("make suggestion to switch up"),S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"up"})))}}(e.data.deviationOfMean2)}},{type:i.BUFFERING,listener:function(e){(function(e){var t,n=e.data.startBufferings.length;l||I()||(d.debugEnabled()&&d.debug("evaluating buffering events allowed"),n>=2&&(d.debugEnabled()&&d.debug("startBufferingsLength "+n),t=y(e.data.startBufferings,3),d.debugEnabled()&&d.debug("startBufferingsCutOffLength >= 3 && isCriticalEventHistory ?"),t.length>=3&&T(t,10)?(d.debugEnabled()&&d.debug("startBufferingsCutOffLength >= 3 && isCriticalEventHistory !"),S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"down",priority:1})):t.length>=2&&(t=y(e.data.startBufferings,2),d.debugEnabled()&&d.debug("startBufferingsCutOffLength >= 2 && isCriticalEventHistory ?"),T(t,10)&&(d.debugEnabled()&&d.debug("startBufferingsCutOffLength >= 2 && isCriticalEventHistory !"),S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"down",priority:0})))))})(e),O()}},{type:i.STREAM_INFO,listener:function(){v(!1)}},{type:i.STREAM_QUALITY,listener:function(e){E=e.data.streamQuality.timestamp.standardDeviation,d.debugEnabled()&&d.debug("serverStandardDeviation "+E)}},{type:i.PAUSED,listener:function(){C(),O()}},{type:i.BUFFERING_LONG,listener:function(){d.debugEnabled()&&d.debug("onBufferingLong"),S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"down",priority:1})}},{type:a.SUGGESTION_ABORT,listener:function(e){C(e.data.reason),R()}}];function b(e,t){return e<t}function _(e,t){return e>t}function T(e,t){d.debugEnabled()&&d.debug("isCriticalEventHistory with allowedTimeDifferenceSeconds "+t);var n=e.length;return function(e,t,n){return(t-e)/1e3<n}(e[0].timestamp,e[n-1].timestamp,t)}function y(e,t){var n=e.length;return n>0?e.slice(Math.max(n-t,0),n):[]}function S(e){d.debug((e?"allow":"deny")+" prediction"),e?(d.debugEnabled()&&d.debug("deny prediction"),clearTimeout(u),u=0,l=!0):u?d.debugEnabled()&&d.debug("allow prediction already in timeout"):(d.debugEnabled()&&d.debug("allow prediction in 3 seconds"),u=setTimeout((function(){d.debugEnabled()&&d.debug("allow prediction after timeout"),l=!1,u=0}),3e3))}function A(e,t){d.debugEnabled()&&d.debug("make suggestion to switch "+t.suggestion+(t.priority?" with priority "+t.priority:" without priority")),o.emit(e,t)}function v(e){d.debugEnabled()&&d.debug("success"),c||e||(m||h)&&(p=!0,s&&clearTimeout(s),s=setTimeout((function(){S(!0),A(r.SWITCH_SUGGESTION,{suggestion:"up"}),h=!1,m=!1}),5e3)),C(),S(!1)}function R(){d.debugEnabled()&&d.debug("error"),S(!1),O()}function O(){d.debugEnabled()&&d.debug("destroy initial switch up"),clearTimeout(s),c=!0}function I(){return"highest"===f}function N(){return"lowest"===f}function C(e){(f=e||!1)?d.debugEnabled()&&d.debug("aborted with reason: "+f):d.debugEnabled()&&d.debug("reset abort reason"),N()&&O()}return d.debugEnabled()&&d.debug("initialize"),e.add({target:o,listeners:g}),{destroy:function(){O(),e.remove({target:o,listeners:g})}}}}}.apply(t,r))||(e.exports=i)},6400:function(e,t,n){var r,i;r=[n(492),n(6637),n(8333),n(5337),n(4762),n(8134)],void 0===(i=function(e,t,n,r,i,a){return{create:function(o){var s,u,d,l,c,f,E,p=n.create("Adaptive.DeviationOfMean2.Ressource"),m=!1,h=[],g=[],b=[],_=[],T=[{type:i.APPLICATION_STATE_CHANGE,listener:function(t){for(s||p.debugEnabled()&&p.debug("initial player state"),(s=t.data.state)===e.PAUSED&&"playbackrestart"!==t.data.reason&&(v(),o.emit(a.PAUSED,{paused:{}})),s===e.PLAYING&&g.length&&g[g.length-1].state===e.LOADING&&(p.debugEnabled()&&p.debug("setRessourceBlockTimeout from playing"),S());g.length>100;)g.shift();g.push({state:s,timestamp:performance.now()}),l&&R()}},{type:i.PLAY_STATS,listener:function(e){var t,n;m||(c.add(e.data.stats.buffer.delay.current),c.count%10==0&&(f.add(c.arithmetic),E.add(f.values[f.values.length-1]),b.add(E.deviation),_.add(b.deviation),t=c,(n=u.adaptive.buffer.delay).avg=t.arithmetic,n.min=t.minimum,n.max=t.maximum,n.deviation=t.deviation,n.samples=t.values.length,n.count=t.count,n.bufferDelaysArithmeticMeans=f.values,n.bufferDelaysStandardDeviations=b.values,n.bufferDelaysStandardDeviationsDev=_.values,o.emit(a.STATS,{deviationOfMean2:u.adaptive})))}},{type:i.BUFFERING,listener:function(t){for(p.debugEnabled()&&p.debug("push startBuffering!"),h.push({event:t.data,timestamp:performance.now()});h.length>10;)h.shift();m?p.debugEnabled()&&p.debug("block emit startBuffering!"):(p.debugEnabled()&&p.debug("emit startBuffering!"),o.emit(a.BUFFERING,{startBufferings:h})),p.debugEnabled()&&p.debug("setBufferingLongTimeout"),R(),l=setTimeout((function(){p.debugEnabled()&&p.debug("setBufferingLongTimeout fired"),s===e.BUFFERING?(p.debugEnabled()&&p.debug("setBufferingLongTimeout fired and still in buffering state"),o.emit(a.BUFFERING_LONG)):p.debugEnabled()&&p.debug("setBufferingLongTimeout fired and not in buffering state")}),3e3)}},{type:i.STREAM_INFO,listener:function(){o.emit(a.STREAM_INFO,{streamInfo:{}}),v()}},{type:i.STREAM_QUALITY,listener:function(e){o.emit(a.STREAM_QUALITY,{streamQuality:e.data.onStreamQuality})}},{type:i.UPDATE_SOURCE_SUCCESS,listener:y},{type:i.SWITCH_STREAM_SUCCESS,listener:y}];function y(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout from success"),S()}function S(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout"),A(!0),clearTimeout(d),d=setTimeout((function(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired"),A(!1),s===e.BUFFERING?(p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired but in buffering state"),o.emit(a.BUFFERING,{startBufferings:h})):(p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired & not in buffering state"),v())}),2e3)}function A(e){p.debugEnabled()&&p.debug("setRessourceBlock "+(e?"true":"false")),m=e}function v(){for(p.debugEnabled()&&p.debug("reset stats"),u={adaptive:{buffer:{delay:{samples:0,frequency:1,avg:0,min:0,max:0,deviation:0,count:0}}}},f=r.create(20,1),E=r.create(10,1),c=r.create(10,1),b=r.create(10,1),_=r.create(10,1);h.length;)h.pop()}function R(){p.debugEnabled()&&p.debug("clearBufferingLongTimeout"),clearTimeout(l),l=0}return p.debugEnabled()&&p.debug("initialize"),v(),t.add({target:o,listeners:T}),{destroy:function(){t.remove({target:o,listeners:T})}}}}}.apply(t,r))||(e.exports=i)},5739:function(e,t,n){var r,i;r=[n(6637),n(8333),n(4762),n(708),n(8134),n(4035)],void 0===(i=function(e,t,n,r,i,a){return{create:function(o,s){var u,d,l=t.create("Adaptive.DeviationOfMean.Adaption"),c=!0,f=s&&s.useHLS?.035:.02,E=!1,p=!1,m=0,h=!1,g=!1,b=!1,_=[{type:n.UPDATE_SOURCE_SUCCESS,listener:O},{type:n.UPDATE_SOURCE_FAIL,listener:I},{type:n.UPDATE_SOURCE_ABORT,listener:I},{type:n.SWITCH_STREAM_SUCCESS,listener:O},{type:n.SWITCH_STREAM_FAIL,listener:I},{type:n.SWITCH_STREAM_ABORT,listener:I},{type:n.LOADED_DATA,listener:function(){l.debugEnabled()&&l.debug("onLoadedData"),g=!0}},{type:n.TIME_OFFSET,listener:function(){l.debugEnabled()&&l.debug("onFirstFragmentRendered"),b=!0}},{type:i.STATS,listener:function(e){h||O(!0),function(e){if(l.debugEnabled()&&l.debug("make switch suggestion ?"),!c){l.debugEnabled()&&l.debug("making switch suggestion allowed");var t,n,i=e.buffer.delay.bufferDelaysStandardDeviations.length;l.debugEnabled()&&l.debug("bufferDelaysStandardDeviationsLength >= 5 && !wasAbortedHighest ?"),i>=5&&!C()&&(l.debugEnabled()&&l.debug("bufferDelaysStandardDeviationsLength >= 5 && !wasAbortedHighest !"),t=A(e.buffer.delay.bufferDelaysStandardDeviations,5),n=A(e.buffer.delay.bufferDelaysArithmeticMeans,5),l.debugEnabled()&&l.debug("bufferDevationAboveValue && bufferCurrentHasMinimumValue ?"),function(e,t){l.debugEnabled()&&l.debug("bufferDevationAboveValue with limit 0.075");for(var n=!0,r=0;r<=e.length-1;r++)if(!y(e[r],.075)){n=!1;break}return n}(t)&&function(e,t){l.debugEnabled()&&l.debug("bufferCurrentHasMinimumValue with limit 0.5");for(var n=!1,r=0;r<=e.length-1;r++)if(T(e[r],.5)){n=!0;break}return n}(n)&&(l.debugEnabled()&&l.debug("bufferDevationAboveValue && bufferCurrentHasMinimumValue !"),l.debugEnabled()&&l.debug("make suggestion to switch down"),v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"down"}))),l.debugEnabled()&&l.debug("bufferDelaysStandardDeviationsLength >= 8 && !wasAbortedLowest ?"),i>=8&&!w()&&(l.debugEnabled()&&l.debug("bufferDelaysStandardDeviationsLength >= 8 && !wasAbortedLowest !"),t=A(e.buffer.delay.bufferDelaysStandardDeviations,8),l.debugEnabled()&&l.debug("bufferDevationBelowValue ?"),function(e,t){l.debugEnabled()&&l.debug("bufferDevationBelowValue with limit "+t);for(var n=!0,r=0;r<=e.length-1;r++)if(!T(e[r],t)){n=!1;break}return n}(t,f)&&(l.debugEnabled()&&l.debug("bufferDevationBelowValue !"),l.debugEnabled()&&l.debug("make suggestion to switch up"),v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"up"})))}}(e.data.deviationOfMean)}},{type:i.BUFFERING,listener:function(e){(function(e){var t,n=e.data.startBufferings.length;l.debugEnabled()&&l.debug("evaluate buffering events ?"),c||C()||(l.debugEnabled()&&l.debug("evaluating buffering events allowed"),n>=2&&(l.debugEnabled()&&l.debug("startBufferingsLength "+n),t=A(e.data.startBufferings,3),l.debugEnabled()&&l.debug("startBufferingsCutOffLength >= 3 && isCriticalEventHistory ?"),t.length>=3&&S(t,10)?(l.debugEnabled()&&l.debug("startBufferingsCutOffLength >= 3 && isCriticalEventHistory !"),v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"down",priority:1})):t.length>=2&&(t=A(e.data.startBufferings,2),l.debugEnabled()&&l.debug("startBufferingsCutOffLength >= 2 && isCriticalEventHistory ?"),S(t,10)&&(l.debugEnabled()&&l.debug("startBufferingsCutOffLength >= 2 && isCriticalEventHistory !"),v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"down",priority:0})))))})(e),N()}},{type:i.STREAM_INFO,listener:function(){O(!0)}},{type:i.STREAM_QUALITY,listener:function(e){m=e.data.streamQuality.timestamp.standardDeviation,l.debugEnabled()&&l.debug("serverStandardDeviation "+m)}},{type:i.PAUSED,listener:function(){D(),N()}},{type:i.BUFFERING_LONG,listener:function(){l.debugEnabled()&&l.debug("onBufferingLong"),v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"down",priority:1})}},{type:a.SUGGESTION_ABORT,listener:function(e){D(e.data.reason),I()}}];function T(e,t){return e<t}function y(e,t){return e>t}function S(e,t){l.debugEnabled()&&l.debug("isCriticalEventHistory with allowedTimeDifferenceSeconds "+t);var n=e.length;return function(e,t,n){return(t-e)/1e3<n}(e[0].timestamp,e[n-1].timestamp,t)}function A(e,t){var n=e.length;return n>0?e.slice(Math.max(n-t,0),n):[]}function v(e){l.debug((e?"allow":"deny")+" prediction"),e?(l.debugEnabled()&&l.debug("deny prediction"),clearTimeout(d),d=0,c=!0):d?l.debugEnabled()&&l.debug("allow prediction already in timeout"):(l.debugEnabled()&&l.debug("allow prediction in 3 seconds"),d=setTimeout((function(){l.debugEnabled()&&l.debug("allow prediction after timeout"),c=!1,d=0}),3e3))}function R(e,t){l.debugEnabled()&&l.debug("make suggestion to switch "+t.suggestion+(t.priority?" with priority "+t.priority:" without priority")),o.emit(e,t)}function O(e){l.debugEnabled()&&l.debug("success"),E||e||(g||b)&&(h=!0,u&&clearTimeout(u),u=setTimeout((function(){v(!0),R(r.SWITCH_SUGGESTION,{suggestion:"up"}),b=!1,g=!1}),5e3)),D(),v(!1)}function I(){l.debugEnabled()&&l.debug("error"),v(!1),N()}function N(){l.debugEnabled()&&l.debug("destroy initial switch up"),clearTimeout(u),E=!0}function C(){return"highest"===p}function w(){return"lowest"===p}function D(e){(p=e||!1)?l.debugEnabled()&&l.debug("aborted with reason: "+p):l.debugEnabled()&&l.debug("reset abort reason"),w()&&N()}return l.debugEnabled()&&l.debug("initialize"),e.add({target:o,listeners:_}),{destroy:function(){N(),e.remove({target:o,listeners:_})}}}}}.apply(t,r))||(e.exports=i)},3349:function(e,t,n){var r,i;r=[n(492),n(6637),n(8333),n(8197),n(5337),n(4762),n(8134)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(r){var s,u,d,l,c,f,E,p=n.create("Adaptive.DeviationOfMean.Ressource"),m=!1,h=[],g=[],b=[],_=[{type:a.APPLICATION_STATE_CHANGE,listener:function(t){for(s||p.debugEnabled()&&p.debug("initial player state"),(s=t.data.state)===e.PAUSED&&"playbackrestart"!==t.data.reason&&(A(),r.emit(o.PAUSED,{paused:{}})),s===e.PLAYING&&g.length&&g[g.length-1].state===e.LOADING&&(p.debugEnabled()&&p.debug("setRessourceBlockTimeout from playing"),y());g.length>100;)g.shift();g.push({state:s,timestamp:performance.now()}),l&&v()}},{type:a.PLAY_STATS,listener:function(e){var t,n;m||(c.add(e.data.stats.buffer.delay.current),c.count%10==0&&(f.add(c.arithmetic),E.add(f.values[f.values.length-1]),b.add(E.deviation),t=c,(n=u.adaptive.buffer.delay).avg=t.arithmetic,n.min=t.minimum,n.max=t.maximum,n.deviation=t.deviation,n.samples=t.values.length,n.count=t.count,n.bufferDelaysArithmeticMeans=f.values,n.bufferDelaysStandardDeviations=b.values,r.emit(o.STATS,{deviationOfMean:u.adaptive})))}},{type:a.BUFFERING,listener:function(t){for(p.debugEnabled()&&p.debug("push startBuffering!"),h.push({event:t.data,timestamp:performance.now()});h.length>10;)h.shift();m?p.debugEnabled()&&p.debug("block emit startBuffering!"):(p.debugEnabled()&&p.debug("emit startBuffering!"),r.emit(o.BUFFERING,{startBufferings:h})),p.debugEnabled()&&p.debug("setBufferingLongTimeout"),v(),l=setTimeout((function(){p.debugEnabled()&&p.debug("setBufferingLongTimeout fired"),s===e.BUFFERING?(p.debugEnabled()&&p.debug("setBufferingLongTimeout fired and still in buffering state"),r.emit(o.BUFFERING_LONG)):p.debugEnabled()&&p.debug("setBufferingLongTimeout fired and not in buffering state")}),3e3)}},{type:a.STREAM_INFO,listener:function(){r.emit(o.STREAM_INFO,{streamInfo:{}}),A()}},{type:a.STREAM_QUALITY,listener:function(e){r.emit(o.STREAM_QUALITY,{streamQuality:e.data.onStreamQuality})}},{type:a.UPDATE_SOURCE_SUCCESS,listener:T},{type:a.SWITCH_STREAM_SUCCESS,listener:T}];function T(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout from success"),y()}function y(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout"),S(!0),clearTimeout(d),d=setTimeout((function(){p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired"),S(!1),s===e.BUFFERING?(p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired but in buffering state"),r.emit(o.BUFFERING,{startBufferings:h})):(p.debugEnabled()&&p.debug("setRessourceBlockTimeout fired & not in buffering state"),A())}),2e3)}function S(e){p.debugEnabled()&&p.debug("setRessourceBlock "+(e?"true":"false")),m=e}function A(){for(p.debugEnabled()&&p.debug("reset stats"),u={adaptive:{buffer:{delay:{samples:0,frequency:1,avg:0,min:0,max:0,deviation:0,count:0}}}},f=i.create(20,1),E=i.create(10,1),c=i.create(10,1),b=i.create(20,1);h.length;)h.pop()}function v(){p.debugEnabled()&&p.debug("clearBufferingLongTimeout"),clearTimeout(l),l=0}return p.debugEnabled()&&p.debug("initialize"),A(),t.add({target:r,listeners:_}),{destroy:function(){t.remove({target:r,listeners:_})}}}}}.apply(t,r))||(e.exports=i)},4762:function(e,t){var n;void 0===(n=function(){var e="sourceManager.";return{BASE:e,SERVER_INFO:e+"serverInfo",STREAM_INFO:e+"streamInfo",STREAM_INFO_UPDATE:e+"streamInfoUpdate",STREAM_QUALITY:e+"streamQuality",STREAM_FRAGMENT:e+"streamFragment",RANDOM_ACCESS_POINT:e+"randomAccessPoint",STREAM_URL:e+"streamUrl",CONFIG:e+"config",PLAYING:e+"playing",BUFFERING:e+"buffering",PLAY_STATS:e+"playStats",APPLICATION_STATE_CHANGE:e+"applicationStateChange",UPDATE_SOURCE:e+"updateSource",UPDATE_SOURCE_INIT:e+"updateSourceInit",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",SWITCH_STREAM:e+"switchStream",SWITCH_STREAM_INIT:e+"switchStreamInit",SWITCH_STREAM_SUCCESS:e+"switchStreamSuccess",SWITCH_STREAM_FAIL:e+"switchStreamFail",SWITCH_STREAM_ABORT:e+"switchStreamAbort",SET_ADAPTION:e+"setAdaption",LOADED_DATA:e+"loadedData",TIME_OFFSET:e+"timeOffset",PUBLIC_EVENT:e+"publicEvent"}}.apply(t,[]))||(e.exports=n)},4035:function(e,t){var n;void 0===(n=function(){var e="switchController.";return{BASE:e,STREAM_SWITCH:e+"streamSwitch",SUGGESTION_ABORT:e+"suggestionAbort"}}.apply(t,[]))||(e.exports=n)},8307:function(e,t,n){var r,i;r=[n(8333),n(9658),n(6627),n(8197),n(6637),n(5055),n(164),n(3019),n(8983),n(7771),n(4762),n(708),n(8134),n(4035),n(6701),n(3049),n(4122)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h){"use strict";return{create:function(n){var r,i,c=new t,g=e.create("SourceManager"),b=[],_=[{from:f.STATS,to:o.STATS},{from:E.STREAM_SWITCH,to:o.STREAM_SWITCH}],T=[{from:s.CONFIG,to:l.CONFIG},{from:s.PLAY_STATS,to:l.PLAY_STATS},{from:s.FRAME_DROP,to:l.FRAME_DROP},{from:s.RANDOM_ACCESS_POINT,to:l.RANDOM_ACCESS_POINT},{from:s.STREAM_QUALITY,to:l.STREAM_QUALITY},{from:s.APPLICATION_STATE_CHANGE,to:l.APPLICATION_STATE_CHANGE},{from:s.UPDATE_SOURCE,to:l.UPDATE_SOURCE},{from:s.SWITCH_STREAM,to:l.SWITCH_STREAM},{from:s.PUBLIC_EVENT,to:l.PUBLIC_EVENT},{from:s.BUFFERING,to:l.BUFFERING},{from:s.SET_ADAPTION,to:l.SET_ADAPTION},{from:s.LOADED_DATA,to:l.LOADED_DATA},{from:s.TIME_OFFSET,to:l.TIME_OFFSET},{from:d.SERVER_INFO,to:l.SERVER_INFO},{from:d.UPDATE_SOURCE_INIT,to:l.UPDATE_SOURCE_INIT},{from:d.UPDATE_SOURCE_SUCCESS,to:l.UPDATE_SOURCE_SUCCESS},{from:d.UPDATE_SOURCE_FAIL,to:l.UPDATE_SOURCE_FAIL},{from:d.UPDATE_SOURCE_ABORT,to:l.UPDATE_SOURCE_ABORT},{from:d.SWITCH_STREAM_INIT,to:l.SWITCH_STREAM_INIT},{from:d.SWITCH_STREAM_SUCCESS,to:l.SWITCH_STREAM_SUCCESS},{from:d.SWITCH_STREAM_FAIL,to:l.SWITCH_STREAM_FAIL},{from:d.SWITCH_STREAM_ABORT,to:l.SWITCH_STREAM_ABORT},{from:u.CREATED,to:l.STREAM_INFO},{from:u.UPDATED,to:l.STREAM_INFO_UPDATE}];return g.debugEnabled()&&g.debug("init"),r=a.create(n,c,T),i=a.create(c,n,_),b.push(h.create(c)),b.push(p.create(c)),b.push(m.create(c)),{destroy:function(){for(r.destroy(),r=null,i.destroy(),i=null;b.length;)b.pop().destroy()}}}}}.apply(t,r))||(e.exports=i)},4122:function(e,t,n){var r,i;r=[n(6637),n(8333),n(8197),n(492),n(4762),n(4035),n(708)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s){var u,d,l,c,f,E,p,m,h,g,b,_,T,y,S,A,v,R=t.create("SwitchRequestController"),O={},I=0,N=!1,C={},w=0,D=!1,U=0,P=0,L=[],M=60,F=120,k=!1,x=[{type:i.CONFIG,listener:function(e){var t;B(e.data.config.source),t=e.data.config.playback,l=t.faststart,H("init")}},{type:i.SWITCH_STREAM,listener:function(e){if("recover"===e.data.type){for(var t=0;t<Object.keys(C).length;t+=1){var n=Object.keys(C)[t];if(Object.prototype.hasOwnProperty.call(C,n)&&"down"===C[n][0]){e.data.index=C[n][1];break}}void 0===e.data.index&&(e.data.index=c)}var r;R.debugEnabled()&&R.debug("forced to switch to stream with index "+e.data.index),R.debugEnabled()&&R.debug("current stream has index "+c+" of "+(E-1)),(r=e.data.index)<E&&r>-1&&(f=e.data.index,"direct"===(N=e.data.type)&&(u.options.adaption.rule="none"),H(N))}},{type:i.UPDATE_SOURCE,listener:function(e){B(e.data.source),H(N="update")}},{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(d||R.debugEnabled()&&R.debug("initial player state"),d=e.data.state){case r.PAUSED:j()}}},{type:i.UPDATE_SOURCE_SUCCESS,listener:function(){N=!1}},{type:i.UPDATE_SOURCE_FAIL,listener:function(){N=!1}},{type:i.UPDATE_SOURCE_ABORT,listener:function(){N=!1}},{type:i.SWITCH_STREAM_INIT,listener:function(e){var t=e.data.tag;R.debugEnabled()&&R.debug('stream switch init with tag "'+t+'"')}},{type:i.SWITCH_STREAM_SUCCESS,listener:function(e){delete C[e.data.id];var t=e.data.tag,n=t.split(" streamSwitch "),r=e.data.type;"down"===r&&(D=!0,U=V()),"up"===r&&(P=V(),L.push(P)),R.debugEnabled()&&R.debug('stream switch success with tag "'+t+'"'),R.debugEnabled()&&R.debug("switch with id "+n[1]+" was successful, switch index changed from "+c+" to "+O[n[1]]),G(O[n[1]]),N=!1}},{type:i.SWITCH_STREAM_FAIL,listener:function(e){delete C[e.data.id];var t=e.data.tag,n=t.split(" streamSwitch ");R.debugEnabled()&&R.debug('stream switch fail with tag "'+t+'"'),R.debugEnabled()&&R.debug("switch with id "+n[1]+" has been failed with code "+e.data.code+", switch index stays at "+c),N=!1}},{type:i.SWITCH_STREAM_ABORT,listener:function(e){delete C[e.data.id];var t=e.data.tag,n=t.split(" streamSwitch ");R.debugEnabled()&&R.debug('stream switch abort with tag "'+t+'"'),R.debugEnabled()&&R.debug("switch with id "+n[1]+" has been aborted"+(e.data&&e.data.reason?" with reason '"+e.data.reason+"'":"")+", switch index stays at "+c),N=!1}},{type:i.SET_ADAPTION,listener:function(e){u.options.adaption=e.data.adaption}},{type:o.SWITCH_SUGGESTION,listener:function(e){if(R.debugEnabled()&&R.debug("suggest stream to switch "+e.data.suggestion),R.debugEnabled()&&R.debug("current stream has index "+c+" of "+(E-1)),N&&(!e.data.priority||e.data.priority&&1!==e.data.priority))R.debugEnabled()&&R.debug("switch already initialized, abort"),s.emit(a.SUGGESTION_ABORT,{reason:"initialized"});else{if(N=void 0,e.data.suggestion)if("down"===e.data.suggestion)c<E-1?(f=1===e.data.priority?E-1:Math.min(c+u.options.adaption.downStep,E-1),N="down",k=!1,R.debugEnabled()&&R.debug("downswitch suggestion, no timeout applied")):(R.debugEnabled()&&R.debug("suggest to switch down but highest index reached, abort"),s.emit(a.SUGGESTION_ABORT,{reason:"highest"}));else if("up"===e.data.suggestion){w=V(),h=Math.max(P,U),p=D?function(){if((m=V())<h)return 0;var e=Y(m,U),t=Math.abs(P-U);S=Y(m,_),y=Y(m,T);var n=(A=p)+10<e,r=!!(e>(A<=M?M:v+60));if(k)p=A;else if(P>0&&!k)if(t<30&&e<=v+10)W(!!_&&!!(P-_||P-T>0)&&!!(S>60&&S<180||y<300)?240:F);else if(!k&&n&&r)switch(A){case 30:case M:W(30);break;case F:W(M);break;case 240:W(F)}else W(M);else W(M);return p}():0,g=P>U?"up":"down";var t=w-h;if(0===c)R.debugEnabled()&&R.debug("suggest to switch up but lowest index reached, abort"),s.emit(a.SUGGESTION_ABORT,{reason:"lowest"}),k=!1;else if(D&&t<p)switch(b=p-t,R.debugEnabled()&&R.debug("switchSuggestionTime "+w.toFixed(2)+"; lastSwitchTime "+h.toFixed(2)+"; timeout in sec: "+p+"; timeLeft: "+b.toFixed(2)),g){case"down":R.debugEnabled()&&R.debug("suggest to switch up but too less time passed since the last switch down"),s.emit(a.SUGGESTION_ABORT,{reason:"toofast"});break;case"up":R.debugEnabled()&&R.debug("suggest to switch up but too less time passed since the last upswitch"),s.emit(a.SUGGESTION_ABORT,{reason:"toofast"})}else f=c-1,N="up",k=!1,R.debugEnabled()&&R.debug("after upswitch, no timeout applied"),v=F}N?H(N):R.debugEnabled()&&R.debug("switch not initialized")}}}];function B(e){E=(u=e).entries.length,c=u.startIndex,f=u.startIndex}function H(e){R.debugEnabled()&&R.debug("apply switch to stream with index "+f),I=Math.round(1e11*Math.random()),O[I]=f,C[I]=[e,f],R.debugEnabled()&&R.debug("switch with id "+I+" should change switch index from "+c+" to "+f);var t,r,i=n.copy(u.options.switch);"recover"===e&&(i.timeout=10,i.method="client"),i.tag="update"===e?i.tag:(t=f,((r=u.entries[t].h5live).rtmp?r.rtmp.streamname:r.params&&r.params.stream)+" streamSwitch "+I),i.pauseOnError="update"===e||i.pauseOnError,R.debugEnabled()&&R.debug('switch has tag "'+i.tag+'"');var o={source:n.copy(u),rule:n.copy(u.options.adaption.rule),downStep:n.copy(u.options.adaption.downStep),entry:n.copy(u.entries[f]),options:n.copy(i),faststart:("init"===e||"update"===e)&&l,type:e,id:I};switch(s.emit(a.STREAM_SWITCH,o),e){case"init":R.debugEnabled()&&R.debug('stream switch init with tag "'+i.tag+'"'),R.debugEnabled()&&R.debug("switch with id "+I+" is type 'init', set initial index to "+f),G(f),j();break;case"update":case"direct":case"recover":j()}}function G(e){c=e}function V(){return(new Date).getTime()/1e3}function W(e){p=e,v=30===e?M:e,k=!0,R.debugEnabled()&&R.debug("applied timeout: "+e),240===p?T=V():p===F&&(_=V())}function Y(e,t){return e-t}function j(){w=0,U=0,P=0,h=0,D=!1,k=!1,L=[],v=0,R.debugEnabled()&&R.debug("reset time values")}return R.debugEnabled()&&R.debug("initialize"),e.add({target:s,listeners:x}),{destroy:function(){e.remove({target:s,listeners:x})}}}}}.apply(t,r))||(e.exports=i)},5620:function(e,t,n){var r,i;r=[n(4556),n(8333),n(6627),n(6637),n(492),n(3019),n(519),n(8983)],void 0===(i=function(e,t,n,r,i,a,o,s){return{create:function(u){var d,l,c,f=t.create("StartupStatsCollector"),E=[],p=E[0]="connecting",m=E[1]="connected",h=E[2]="firstFragmentReceived",g=E[3]="firstFrameRendered",b=E[4]="playable",_=E[5]="playing",T={},y=null,S=!1,A=0,v=!1,R=[{type:a.APPLICATION_STATE_CHANGE,listener:function(e){switch(l=e.data.state){case i.LOADING:D(),r.add({target:u,listeners:d?C:O});break;default:D(),r.remove({target:u,listeners:d?C:O})}}},{type:a.CONFIG,listener:function(e){c=e.data.config.id,d=n.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&n.canUseHLS,v=e.data.config.playback.keepConnection,!d&&(O=O.concat(v?N:I)),S=e.data.config.playback.faststart}},{type:s.CREATED,listener:function(e){A=e.data.streamInfo.prerollDuration?e.data.streamInfo.prerollDuration:0}}],O=[{type:a.STREAM_FRAGMENT,listener:M},{type:a.LOADED_DATA,listener:F},{type:a.CAN_PLAY_THROUGH,listener:k},{type:a.PLAYING,listener:x}],I=[{type:a.NETWORK_CONNECTING,listener:P},{type:a.NETWORK_CONNECTED,listener:L}],N=[{type:a.NETWORK_PLAY,listener:P},{type:a.NETWORK_PLAY,listener:L}],C=[{type:a.PLAY,listener:P},{type:a.DURATION_CHANGE,listener:L},{type:a.LOADED_META_DATA,listener:M},{type:a.LOADED_DATA,listener:F},{type:a.CAN_PLAY_THROUGH,listener:k},{type:a.PLAYING,listener:x}];function w(t){if(l===i.LOADING)if(f.debugEnabled()&&f.debug("enabled, should set stat: "+t),-1===T[t])for(var n=function(e){for(var t=[],n=E.indexOf(e);n>=0;)-1===T[E[n]]&&(e!==_||e===_&&-1!==[h,g,b].indexOf(y))?(t.unshift(E[n]),n--):n=-1;return t.length&&(y=e),f.debugEnabled()&&f.debug(t.length?'valid labels are "'+t.join('", "')+'"':"no valid labels found"),t}(t),r=0,a=n.length;r<a;r+=1)t=n[r],-1===T[t]&&(f.debugEnabled()&&f.debug("set stat: "+t),T[t]=e.now(),e.mark(["nano",c,t].join(".")),f.debug(t+": "+T[t]),t===_&&U());else f.debugEnabled()&&f.debug("stat already set");else f.debugEnabled()&&f.debug("disabled, shouldn't set stat: "+t)}function D(){y=null;for(var e=0;e<E.length;e++)T[E[e]]=-1}function U(){for(var e=1;e<E.length;e++)T[E[e-1]]>T[E[e]]&&(T[E[e]]=T[E[e-1]]);var t=T[p],n={};for(var r in T)Object.prototype.hasOwnProperty.call(T,r)&&(n[r]=Math.round(T[r]-t));var i={stats:n,faststart:S,prerollDuration:1e3*A};u.emit(o.CREATED,i),f.debugEnabled()&&f.debug("startup stats: "+JSON.stringify(i))}function P(e){(!d||d&&e.data.external)&&w(p)}function L(){w(m),d&&B("remove",a.DURATION_CHANGE,L)}function M(){w(h),!d&&B("remove",a.STREAM_FRAGMENT,M)}function F(){w(g)}function k(){w(b)}function x(){w(_)}function B(e,t,n){r[e]({target:u,listeners:[{type:t,listener:n}]})}return D(),r.add({target:u,listeners:R}),{destroy:function(){r.remove({target:u,listeners:R})}}}}}.apply(t,r))||(e.exports=i)},5444:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(3019),n(8983),n(492),n(7605),n(8730)],void 0===(i=function(e,t,n,r,i,a,o,s){return{create:function(u){var d,l,c,f,E,p=e.create("StreamInfo"),m=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){d=e.data.state}},{type:r.CONFIG,listener:function(e){l=t.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&t.canUseHLS}},{type:r.STREAM_INFO,listener:function(e){d!==a.READY&&h(i.CREATED,e)}},{type:r.STREAM_INFO_UPDATE,listener:function(e){d!==a.BUFFERING&&d!==a.PLAYING||h(i.UPDATED,e)}},{type:r.STREAM_URL,listener:function(e){f=e.data.url,E=function(e){for(var t={},n=e.indexOf("?")+1,r=("&"+e.substring(n)).split("&"),i=0;i<r.length;i+=1){var a=r[i].split("=");2===a.length&&(t[a[0]]=decodeURIComponent(a[1]))}return t}(f)}},{type:r.CAN_PLAY,listener:function(e){l&&(p.debugEnabled()&&p.debug("create streamInfo for hls on canPlay"),(c={haveAudio:e.data.haveAudio,haveVideo:e.data.haveVideo,prerollDuration:0,audioInfo:e.data.haveAudio?{bitsPerSample:null,channels:null,sampleRate:null}:null,videoInfo:e.data.haveVideo?{width:e.data.videoWidth,height:e.data.videoHeight,frameRate:null}:null}).url=f,c.rtmp=g(),p.debugEnabled()&&p.debug("created streamInfo for hls: "+JSON.stringify(c)),setTimeout((function(){u.emit(i.CREATED,{streamInfo:c})}),0))}}];function h(e,t){c=t.data.onStreamInfo,p.debugEnabled()&&p.debug("handle streamInfo with "+e+": "+JSON.stringify(c));var n=c&&c.mimeType?c.mimeType:s.MP4_MS;if(window.MediaSource.isTypeSupported(n)){var r={},a=[];for(var d in o)if(Object.prototype.hasOwnProperty.call(o,d)){var l=o[d];Object.prototype.hasOwnProperty.call(o,d)&&Object.prototype.hasOwnProperty.call(c,l)&&-1===a.indexOf(l)&&(r[l]=c[l])}(c=r).url=f,c.rtmp=g(),p.debugEnabled()&&p.debug("parsed streamInfo with "+e+": "+JSON.stringify(c)),u.emit(e,{streamInfo:c})}else u.emit(i.MIME_TYPE_UNSUPPORTED)}function g(){return{streamname:E.stream?E.stream:"",url:E.url?E.url:""}}return p.debugEnabled()&&p.debug("init"),n.add({target:u,listeners:m}),{destroy:function(){n.remove({target:u,listeners:m})}}}}}.apply(t,r))||(e.exports=i)},4600:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(3019),n(3667),n(492)],void 0===(i=function(e,t,n,r,i,a){var o=2e4,s=2e4;return{create:function(t){var u,d=e.create("Timeout"),l=0,c=0,f=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(u=e.data.state){case a.READY:case a.PLAYING:case a.PAUSED:clearTimeout(l),clearTimeout(c)}var n;u===a.BUFFERING?(clearTimeout(c),c=setTimeout((function(){t.emit(i.BUFFERING_TIMEOUT,{delay:s})}),s)):clearTimeout(c),u===a.LOADING&&(n=e.data.connectDelay,clearTimeout(l),l=setTimeout((function(){t.emit(i.LOADING_TIMEOUT,{delay:o+n})}),o+n))}},{type:r.CONFIG,listener:function(e){var t=e.data.config,n=1e3*t.playback.timeouts.loading,r=1e3*t.playback.timeouts.buffering;o=Math.max(Math.min(n,6e4),1e4),s=Math.max(Math.min(r,6e4),1e4)}},{type:r.UPDATE_SOURCE_INIT,listener:function(){clearTimeout(l),clearTimeout(c)}},{type:r.MEDIA_ERROR_RECOVER,listener:function(){clearTimeout(l),clearTimeout(c)}}];return d.debugEnabled()&&d.debug("init"),n.add({target:t,listeners:f}),{destroy:function(){clearTimeout(l),clearTimeout(c),n.remove({target:t,listeners:f})}}}}}.apply(t,r))||(e.exports=i)},4351:function(e,t,n){var r,i;r=[n(6637),n(8333),n(3019),n(2450)],void 0===(i=function(e,t,n,r){return{create:function(i){var a,o,s,u=t.create("VisibilityProxy"),d=!1,l=[{type:n.APPLICATION_STATE_CHANGE,listener:function(e){s||(u.debugEnabled()&&u.debug("initial player state"),c()),s=e.data.state}}];function c(){d=document[a],u.debug(d?"hidden":"visible"),i.emit(d?r.HIDDEN:r.VISIBLE)}return u.debugEnabled()&&u.debug("initialize"),e.add({target:i,listeners:l}),void 0!==document.hidden?(a="hidden",o="visibilitychange"):void 0!==document.msHidden?(a="msHidden",o="msvisibilitychange"):void 0!==document.webkitHidden&&(a="webkitHidden",o="webkitvisibilitychange"),document.addEventListener(o,c),{destroy:function(){e.remove({target:i,listeners:l}),document.removeEventListener(o,c)}}}}}.apply(t,r))||(e.exports=i)},6432:function(e,t,n){var r,i;r=[n(8197)],void 0===(i=function(e){var t="?",n=null,r=null;return{create:function(){var i=function(){n=null,t="?",r=null};return{initURL:function(a,o){return i(),r=e.copy(a),n=r.server[o],t="?",this},processConfigQuery:function(e){for(var n in r.query)if(!1!==r.query[n]&&Object.prototype.hasOwnProperty.call(r.query,n)){let i=null;i="cid"===n?Math.round(1e6*Math.random()).toString():"pid"!==n||r.query[n].length?r.query[n]:e,null===i&&void 0!==i||!i.length||(t+=n+"="+encodeURIComponent(i)+"&")}return this},processConfigFlags:function(e){return r.query.flags=Object.keys(e).join(","),this},processSecurity:function(){for(var e in r.security)Object.prototype.hasOwnProperty.call(r.security,e)&&r.security[e].length&&(t+=e+"="+encodeURIComponent(r.security[e])+"&");return this},buildConnectionURL:function(){return t.lastIndexOf("&")===t.length-1&&(t=t.substring(0,t.length-1)),this},reset:i,get connectionURL(){return n+t}}}}}.apply(t,r))||(e.exports=i)},2706:function(e,t){var n;void 0===(n=function(){var e=null;function t(){return{server:{websocket:"",hls:"",progressive:""},query:{url:"",stream:"",cid:"",pid:"",token:"",flags:""},security:null}}return{create:function(n){var r=function(){e=null};return{initConfig:function(){return r(),e=new t,n.debugEnabled()&&n.debug("set connection config",3),this},setServer:function(t){if("object"!=typeof t)return this;for(var r=["websocket","hls","progressive"],i=0;i<r.length;i++)t[r[i]]&&"string"==typeof t[r[i]]&&0!==t[r[i]].length&&(e.server[r[i]]=t[r[i]]);return n.debugEnabled()&&n.debug("set connection server: "+JSON.stringify(e.server),3),this},setRtmp:function(t){for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var i=r.replace("name","");Object.prototype.hasOwnProperty.call(e.query,i)&&(e.query[i]=t[r],n.debugEnabled()&&n.debug("set connection config rtmp: "+i+":"+e.query[i]))}return this},setToken:function(t){return t?(Object.prototype.hasOwnProperty.call(e.query,"token")&&(e.query.token=t,n.debugEnabled()&&n.debug("set connection config token: "+e.query.token)),this):this},setQueryParams:function(t){if("object"!=typeof t)return this;for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e.query[r]=t[r],n.debugEnabled()&&n.debug("set connection param: "+r+":"+e.query[r]));return this},addQueryParam:function(t,n){return t&&(e.query[t]=n),this},setSecurity:function(t){if(t)for(var r in e.security={},n.debugEnabled()&&n.debug("set security config",3),t)Object.prototype.hasOwnProperty.call(t,r)&&t[r].length&&(e.security[r]=t[r],n.debugEnabled()&&n.debug("set security config "+r+": "+e.security[r]));return this},reset:r,get connectionConfig(){return e}}}}}.apply(t,[]))||(e.exports=n)},3763:function(e,t){var n;void 0===(n=function(){return{BASE:"",LOAD_START:"loadstart",PROGRESS:"progress",SUSPEND:"suspend",ABORT:"abort",EMPTIED:"emptied",STALLED:"stalled",PLAY:"play",PAUSE:"pause",LOADED_META_DATA:"loadedmetadata",LOADED_DATA:"loadeddata",WAITING:"waiting",ERROR:"error",PLAYING:"playing",CAN_PLAY:"canplay",CAN_PLAY_THROUGH:"canplaythrough",SEEKING:"seeking",SEEKED:"seeked",TIME_UPDATE:"timeupdate",ENDED:"ended",RATE_CHANGE:"ratechange",DURATION_CHANGE:"durationchange",VOLUME_CHANGE:"volumechange"}}.apply(t,[]))||(e.exports=n)},5304:function(e,t){var n;void 0===(n=function(){return{ABORT_ERROR:"AbortError",NOT_ALLOWED_ERROR:"NotAllowedError"}}.apply(t,[]))||(e.exports=n)},855:function(e,t){var n;void 0===(n=function(){return Object.freeze({MEDIA_ERR_ABORTED:MediaError.MEDIA_ERR_ABORTED,MEDIA_ERR_NETWORK:MediaError.MEDIA_ERR_NETWORK,MEDIA_ERR_DECODE:MediaError.MEDIA_ERR_DECODE,MEDIA_ERR_SRC_NOT_SUPPORTED:MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED,MEDIA_ERR_HLS_VIDEO_DECODE:5,MEDIA_ERR_MEDIA_SOURCE_ENDED:100,MEDIA_ERR_HLS_BUFFER_UNDERRUN:101,MEDIA_ERR_HLS_JUMP:1008})}.apply(t,[]))||(e.exports=n)},5865:function(e,t){var n;void 0===(n=function(){return{BASE:"",SOURCE_OPEN:"sourceopen",SOURCE_ENDED:"sourceended",SOURCE_CLOSED:"sourceclosed"}}.apply(t,[]))||(e.exports=n)},7990:function(e,t){var n;void 0===(n=function(){return{BASE:"",ABORT:"abort",ERROR:"error",UPDATE:"update",UPDATE_START:"updatestart",UPDATE_END:"updateend"}}.apply(t,[]))||(e.exports=n)},7572:function(e,t){var n;void 0===(n=function(){var e="mediaControl.";return{BASE:e,PLAYBACK_STARTED:e+"playbackStarted",BUFFERING:e+"buffering"}}.apply(t,[]))||(e.exports=n)},947:function(e,t){var n;void 0===(n=function(){var e="mediaControl.";return{BASE:e,BUFFER_TWEAKS_CREATED:e+"tweaksCreated",BUFFER_TWEAKS_ERROR:e+"tweaksError"}}.apply(t,[]))||(e.exports=n)},8250:function(e,t){var n;void 0===(n=function(){var e="mediaControl.";return{BASE:e,CREATE_VIDEO:e+"createVideo",DESTROY_VIDEO:e+"destroyVideo",VIDEO_SOURCE:e+"videoSource",UPDATE_SOURCE:e+"updateSource",PLAY:e+"play",PAUSE:e+"pause",SEEK:e+"seek",MUTE:e+"mute",UNMUTE:e+"unmute",SET_VOLUME:e+"volume",SET_RATE:e+"playbackRate"}}.apply(t,[]))||(e.exports=n)},92:function(e,t){var n;void 0===(n=function(){var e="mediaElementHandler.";return{BASE:e,LOAD_START:e+"loadstart",PROGRESS:e+"progress",SUSPEND:e+"suspend",ABORT:e+"abort",EMPTIED:e+"emptied",STALLED:e+"stalled",PLAY:e+"play",PAUSE:e+"pause",LOADED_META_DATA:e+"loadedmetadata",LOADED_DATA:e+"loadeddata",WAITING:e+"waiting",ERROR:e+"error",PLAYING:e+"playing",CAN_PLAY:e+"canplay",CAN_PLAY_THROUGH:e+"canplaythrough",SEEKING:e+"seeking",SEEKED:e+"seeked",TIME_UPDATE:e+"timeupdate",ENDED:e+"ended",RATE_CHANGE:e+"ratechange",DURATION_CHANGE:e+"durationchange",VOLUME_CHANGE:e+"volumechange",QUALITY_UPDATE:e+"qualityupdate",VIDEO_SOURCE:e+"videoSource",ELEMENT_CREATED:e+"elementCreated",PLAY_START_SUCCESS:e+"playStartSuccess",PLAY_START_ERROR:e+"playStartError",VIEWPORT_VISIBLE:e+"viewportvisible",VIEWPORT_HIDDEN:e+"viewporthidden",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_TIMEOUT:e+"updateSourceTimeout",MEDIA_ERROR_RECOVERED:e+"recovered",ACTIVE_VIDEO_ELEMENT_CHANGE:e+"activeVideoElementChange",SWITCH_STREAM:e+"switchStream"}}.apply(t,[]))||(e.exports=n)},5853:function(e,t){var n;void 0===(n=function(){var e="mediaElementProxy.";return{BASE:e,LOAD_START:e+"loadstart",PROGRESS:e+"progress",SUSPEND:e+"suspend",ABORT:e+"abort",EMPTIED:e+"emptied",STALLED:e+"stalled",PLAY:e+"play",PAUSE:e+"pause",LOADED_META_DATA:e+"loadedmetadata",LOADED_DATA:e+"loadeddata",WAITING:e+"waiting",ERROR:e+"error",PLAYING:e+"playing",CAN_PLAY:e+"canplay",CAN_PLAY_THROUGH:e+"canplaythrough",SEEKING:e+"seeking",SEEKED:e+"seeked",TIME_UPDATE:e+"timeupdate",ENDED:e+"ended",RATE_CHANGE:e+"ratechange",DURATION_CHANGE:e+"durationchange",VOLUME_CHANGE:e+"volumechange",QUALITY_UPDATE:e+"qualityupdate",ELEMENT_CREATED:e+"elementCreated",PLAY_START_SUCCESS:e+"playStartSuccess",PLAY_START_ERROR:e+"playStartError",VIEWPORT_VISIBLE:e+"viewportvisible",VIEWPORT_HIDDEN:e+"viewporthidden"}}.apply(t,[]))||(e.exports=n)},6273:function(e,t){var n;void 0===(n=function(){var e="mediaErrorHandler.";return{BASE:e,ERROR:e+"error",RECOVER:e+"recover",RECOVERED:e+"recovered",QUEUED:e+"queued",UNQUEUED:e+"unqueued"}}.apply(t,[]))||(e.exports=n)},1241:function(e,t){var n;void 0===(n=function(){var e="mediaHandler.";return{BASE:e,ERROR:e+"error",SUSPENDED:e+"suspended",OFFLINE_BUFFER_UNDERRUN:e+"offlinebufferunderrun"}}.apply(t,[]))||(e.exports=n)},9437:function(e,t){var n;void 0===(n=function(){var e="mediaManager.";return{BASE:e,CONFIG:e+"config",SERVER_INFO:e+"serverInfo",STREAM_INFO:e+"streamInfo",STREAM_INFO_UPDATE:e+"streamInfoUpdate",STREAM_FRAGMENT:e+"streamFragment",RANDOM_ACCESS_POINT:e+"randomAccessPoint",APPLICATION_STATE_CHANGE:e+"applicationStateChange",STARTUP_STATS:e+"startupStats",DOCUMENT_VISIBLE:e+"documentVisible",DOCUMENT_HIDDEN:e+"documentHidden",NETWORK_ONLINE:e+"networkOnline",NETWORK_OFFLINE:e+"networkOffline",SWITCH_STREAM_INIT:e+"switchStreamInit",SWITCH_STREAM_SUCCESS:e+"switchStreamSuccess",SWITCH_STREAM_ABORT:e+"switchStreamAbort",SWITCH_STREAM_FAIL:e+"switchStreamFail",UPDATE_SOURCE_INIT:e+"updateSourceInit",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",UPDATE_SOURCE_FAIL:e+"updateSourceFail"}}.apply(t,[]))||(e.exports=n)},5751:function(e,t){var n;void 0===(n=function(){var e="mediaSourceHandler.";return{BASE:e,SOURCE_OPEN:e+"sourceopen",SOURCE_ENDED:e+"sourceended",SOURCE_CLOSED:e+"sourceclosed",SOURCE_READY:e+"sourceready",ERROR:e+"error",MEDIA_ERROR_RECOVERED:e+"mediaerrorrecovered"}}.apply(t,[]))||(e.exports=n)},549:function(e,t){var n;void 0===(n=function(){var e="qualityHandler.";return{BASE:e,QUALITY_STATS:e+"qualitystats",FRAME_DROP:e+"framedrop",FRAME_CORRUPTED:e+"framecorrupted"}}.apply(t,[]))||(e.exports=n)},3135:function(e,t){var n;void 0===(n=function(){var e="sourceBufferQueue.";return{BASE:e,TIME_OFFSET:e+"timeOffset",FIRST_FRAGMENT:e+"firstFragment"}}.apply(t,[]))||(e.exports=n)},1667:function(e,t){var n;void 0===(n=function(){var e="statsCollector.";return{BASE:e,PLAY_STATS:e+"playStats",MEDIA_ERROR_RECOVER_STATS:e+"mediaErrorRecoverStats"}}.apply(t,[]))||(e.exports=n)},1358:function(e,t,n){var r,i;r=[n(8333),n(6627),n(9658),n(1933),n(6637),n(5055),n(8730),n(9673),n(680),n(594),n(1376),n(7572),n(1241),n(8250),n(5751),n(9437),n(549),n(1667),n(947),n(3135),n(92),n(6273),n(1383),n(7400),n(8207),n(1733),n(3786),n(6864),n(8245),n(7380),n(7611),n(8394),n(1104),n(4856),n(4606),n(8680),n(1264)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b,_,T,y,S,A,v,R,O,I,N,C,w,D,U,P,L,M,F){"use strict";return{create:function(e){var r,o,k=new n,x=[],B=[{type:m.CONFIG,listener:function(e){!t.isMediaSourceH264Supported||e.data.config.playback.allowSafariHlsFallback&&t.canUseHLS||x.push(v.create(k)),t.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&t.canUseHLS?(x.push(I.create(k)),x.push(N.create(k))):(x.push(O.create(k)),x.push(L.create(k)))}}],H=[{from:T.LOAD_START,to:d.LOAD_START},{from:T.PROGRESS,to:d.PROGRESS},{from:T.SUSPEND,to:d.SUSPEND},{from:T.ABORT,to:d.ABORT},{from:T.EMPTIED,to:d.EMPTIED},{from:T.STALLED,to:d.STALLED},{from:T.PLAY,to:d.PLAY},{from:T.PAUSE,to:d.PAUSE},{from:T.LOADED_META_DATA,to:d.LOADED_META_DATA},{from:T.LOADED_DATA,to:d.LOADED_DATA},{from:T.WAITING,to:d.WAITING},{from:T.PLAYING,to:d.PLAYING},{from:T.CAN_PLAY,to:d.CAN_PLAY},{from:T.CAN_PLAY_THROUGH,to:d.CAN_PLAY_THROUGH},{from:T.SEEKING,to:d.SEEKING},{from:T.SEEKED,to:d.SEEKED},{from:T.TIME_UPDATE,to:d.TIME_UPDATE},{from:T.ENDED,to:d.ENDED},{from:T.RATE_CHANGE,to:d.RATE_CHANGE},{from:T.DURATION_CHANGE,to:d.DURATION_CHANGE},{from:T.VOLUME_CHANGE,to:d.VOLUME_CHANGE},{from:T.ELEMENT_CREATED,to:d.ELEMENT_CREATED},{from:T.PLAY_START_SUCCESS,to:d.PLAY_START_SUCCESS},{from:T.PLAY_START_ERROR,to:d.PLAY_START_ERROR},{from:T.VIEWPORT_VISIBLE,to:d.VIEWPORT_VISIBLE},{from:T.VIEWPORT_HIDDEN,to:d.VIEWPORT_HIDDEN},{from:T.UPDATE_SOURCE_ABORT,to:d.UPDATE_SOURCE_ABORT},{from:T.UPDATE_SOURCE_FAIL,to:d.UPDATE_SOURCE_FAIL},{from:T.UPDATE_SOURCE_TIMEOUT,to:d.UPDATE_SOURCE_TIMEOUT},{from:T.ACTIVE_VIDEO_ELEMENT_CHANGE,to:d.ACTIVE_VIDEO_ELEMENT_CHANGE},{from:T.SWITCH_STREAM,to:s.SWITCH_STREAM},{from:y.ERROR,to:d.MEDIA_ERROR},{from:y.RECOVER,to:d.MEDIA_ERROR_RECOVER},{from:y.RECOVERED,to:d.MEDIA_ERROR_RECOVERED},{from:f.SUSPENDED,to:d.PLAYBACK_SUSPENDED},{from:f.OFFLINE_BUFFER_UNDERRUN,to:d.OFFLINE_BUFFER_UNDERRUN},{from:h.QUALITY_STATS,to:d.QUALITY_STATS},{from:h.FRAME_DROP,to:d.FRAME_DROP},{from:h.FRAME_CORRUPTED,to:d.FRAME_CORRUPTED},{from:g.PLAY_STATS,to:d.PLAY_STATS},{from:g.MEDIA_ERROR_RECOVER_STATS,to:d.MEDIA_ERROR_RECOVER_STATS},{from:p.SOURCE_ENDED,to:d.PLAYBACK_FINISHED},{from:c.PLAYBACK_STARTED,to:d.PLAYBACK_STARTED},{from:c.BUFFERING,to:d.BUFFERING},{from:b.BUFFER_TWEAKS_CREATED,to:d.BUFFER_TWEAKS_CREATED},{from:b.BUFFER_TWEAKS_ERROR,to:d.BUFFER_TWEAKS_ERROR},{from:_.TIME_OFFSET,to:d.TIME_OFFSET}],G=[{from:s.CREATE_VIDEO,to:E.CREATE_VIDEO},{from:s.DESTROY_VIDEO,to:E.DESTROY_VIDEO},{from:s.VIDEO_SOURCE,to:E.VIDEO_SOURCE},{from:s.PLAY,to:E.PLAY},{from:s.PAUSE,to:E.PAUSE},{from:s.SEEK,to:E.SEEK},{from:s.MUTE,to:E.MUTE},{from:s.UNMUTE,to:E.UNMUTE},{from:s.SET_VOLUME,to:E.SET_VOLUME},{from:s.SET_RATE,to:E.SET_RATE},{from:s.UPDATE_SOURCE,to:E.UPDATE_SOURCE},{from:s.CONFIG,to:m.CONFIG},{from:l.SERVER_INFO,to:m.SERVER_INFO},{from:l.STREAM_FRAGMENT,to:m.STREAM_FRAGMENT},{from:l.RANDOM_ACCESS_POINT,to:m.RANDOM_ACCESS_POINT},{from:l.ONLINE,to:m.NETWORK_ONLINE},{from:l.OFFLINE,to:m.NETWORK_OFFLINE},{from:u.STATE_CHANGE,to:m.APPLICATION_STATE_CHANGE},{from:u.STARTUP_STATS,to:m.STARTUP_STATS},{from:u.DOCUMENT_VISIBLE,to:m.DOCUMENT_VISIBLE},{from:u.DOCUMENT_HIDDEN,to:m.DOCUMENT_HIDDEN},{from:u.STREAM_INFO,to:m.STREAM_INFO},{from:u.STREAM_INFO_UPDATE,to:m.STREAM_INFO_UPDATE},{from:u.SWITCH_STREAM_INIT,to:m.SWITCH_STREAM_INIT},{from:u.SWITCH_STREAM_SUCCESS,to:m.SWITCH_STREAM_SUCCESS},{from:u.SWITCH_STREAM_ABORT,to:m.SWITCH_STREAM_ABORT},{from:u.SWITCH_STREAM_FAIL,to:m.SWITCH_STREAM_FAIL},{from:u.UPDATE_SOURCE_INIT,to:m.UPDATE_SOURCE_INIT},{from:u.UPDATE_SOURCE_SUCCESS,to:m.UPDATE_SOURCE_SUCCESS}];return i.add({target:k,listeners:B}),r=a.create(e,k,G),o=a.create(k,e,H),x.push(S.create(k)),x.push(A.create(k)),x.push(M.create(k)),x.push(F.create(k)),x.push(C.create(k)),x.push(P.create(k)),x.push(w.create(k)),x.push(U.create(k)),t.useFakeAudio&&x.push(R.create(k)),t.isTridentBrowser&&x.push(D.create(k)),{destroy:function(){for(;x.length;)x.pop().destroy();r.destroy(),r=null,o.destroy(),o=null}}}}}.apply(t,r))||(e.exports=i)},1383:function(e,t,n){var r,i;r=[n(8333),n(6627),n(8730),n(9474),n(6637),n(7572),n(947),n(8250),n(92),n(5751),n(9437),n(1667),n(6273),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E){return{create:function(n){var r,d,p=e.create("BufferControl"),m={},h=!1,g=!0,b=!1,_=!1,T=-1,y=null,S=[],A=[],v=!0,R=0,O=[{type:l.APPLICATION_STATE_CHANGE,listener:function(e){switch(r=e.data.state,y=null,r){case E.LOADING:p.debugEnabled()&&p.debug("add play state listeners"),i.add({target:n,listeners:C});break;case E.PAUSED:d=!1;break;case E.PAUSING:p.debugEnabled()&&p.debug("remove play state listeners"),i.remove({target:n,listeners:C}),p.warnEnabled()&&p.warn("entering paused state"),T=-1,h=!1,v=!1,g=!0,_=!1}}},{type:l.STARTUP_STATS,listener:function(e){y=e.data}},{type:l.STREAM_INFO,listener:D},{type:l.STREAM_INFO_UPDATE,listener:D},{type:l.CONFIG,listener:function(e){(b=t.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&t.canUseHLS)&&(p.debugEnabled()&&p.debug("add media listeners"),i.add({target:n,listeners:I}))}},{type:o.BUFFER_TWEAKS_CREATED,listener:function(e){p.debugEnabled()&&p.debug("buffer tweaks created"),!b&&r===E.PLAYING&&m.buffer.start<e.data.tweaks.buffer.start&&setTimeout((function(){v=!1,g=!0,n.emit(a.BUFFERING),p.debugEnabled()&&p.debug("new tweaks, pausing to raise buffer"),n.emit(s.PAUSE)}),10),m.buffer=e.data.tweaks.buffer}}],I=[{type:u.CAN_PLAY_THROUGH,listener:function(e){r===E.LOADING&&(p.debugEnabled()&&p.debug("useHLS: canplaythrough while loading, wait for progress"),e.data.paused?(p.debugEnabled()&&p.debug("useHLS: video paused, make 2nd play hook"),n.emit(s.PLAY,{external:!1})):p.debugEnabled()&&p.debug("useHLS: video not paused, no need for a 2nd play hook"),_=!0)}},{type:u.PROGRESS,listener:function(){r===E.LOADING&&_&&y?(p.debugEnabled()&&p.debug("useHLS: progress after canplaythrough while loading, continue"),_=!1,g=!1,h=!0,n.emit(a.PLAYBACK_STARTED,y)):r===E.BUFFERING&&(p.debugEnabled()&&p.debug("useHLS: progress at buffering, continue"),_=!1,g=!1,h=!0,n.emit(a.PLAYBACK_STARTED))}},{type:u.WAITING,listener:function(){r!==E.PLAYING||d||(p.debugEnabled()&&p.debug("useHLS: waiting while playing, now buffering"),v=!1,g=!0,n.emit(a.BUFFERING))}},{type:f.RECOVERED,listener:function(){p.debugEnabled()&&p.debug("media error recovered"),d=!1}}],N=[{type:s.PLAY,listener:function(e){p.debugEnabled()&&p.debug("on play"),!b&&-1===T&&e.data.external&&setTimeout((function(){p.debugEnabled()&&p.debug("pausing to raise buffer at start"),n.emit(s.PAUSE)}),10)}}],C=[{type:c.PLAY_STATS,listener:function(e){if(e.data.buffered&&e.data.buffered.length){var r=e.data.stats,i=r.buffer.delay.current,o=i>=m.buffer.start,u=i>=R-(m.buffer.start-m.buffer.min),l=m.buffer.start>R,c=b?i>0:i>m.buffer.min,f=r.buffer.end-r.buffer.start,E=e.data.currentTime,O=e.data.buffered,I=-1!==L(O,E);P(S,E,5),P(A,i,5);var N,C,w=U(S)&&U(A);if(!b&&!I&&!h&&f>.45)return N=O.length,C=Math.min(O.start(N-1)+.3,O.end(N-1)),p.warn("seek to range: "+O.start(N-1)+" < "+C+" > "+O.end(N-1)+", currentTime: "+E),void n.emit(s.SEEK,{position:t.isTridentBrowser?0:C});if(!b&&!h&&t.isFirefox&&O.length>1&&L(O,E)!==O.length-1&&O.end(N-1)-O.start(N-1)>.3)return N=O.length,C=O.start(N-1),p.warnEnabled()&&p.warn("seek to startable range start: "+O.start(N-1)),void n.emit(s.SEEK,{position:C});if(-1===T&&I&&o){p.debugEnabled()&&p.debug("start buffer of "+m.buffer.start+" reached");var D=!b&&t.isSafari,M=D&&E<.2;if(!R||R&&l){if(p.debugEnabled()&&p.debug(" start buffer case"),M)return T=.3,p.warnEnabled()&&p.warn("initial safari stream startTime set to "+T+", seeking"),void n.emit(s.SEEK,{position:T,fast:!0});T=E,p.debugEnabled()&&p.debug("default stream startTime set to "+T)}else if(R&&u&&!l&&(p.debugEnabled()&&p.debug("preroll buffer of "+R+" reached"),T=D?r.buffer.end-.15:R-(m.buffer.start-m.buffer.min),p.warnEnabled()&&p.warn("initial preroll stream startTime set to "+T+", seeking"),n.emit(s.SEEK,{position:T,fast:!0}),D))return}g&&o&&-1!==T&&(b?b&&_&&(p.debugEnabled()&&p.debug("useHLS: buffering and has enough to start, don't wait for progress"),_=!1):(p.debugEnabled()&&p.debug("starting stream"),n.emit(s.PLAY,{external:!1})),g=!1,h&&n.emit(a.PLAYBACK_STARTED)),g||(!h&&-1!==T&&T<E-.1&&(h=!0,b&&_&&(p.debugEnabled()&&p.debug("useHLS: current time rising and started, don't wait for progress"),_=!1),b&&E<1&&r.buffer.end>1&&p.debugEnabled()&&p.debug("useHLS: buffer higher 1 at start")),h&&b&&E<.5&&r.buffer.end>1&&(p.debugEnabled()&&p.debug("useHLS: maybe preroll detected, seek to 0.5"),n.emit(s.SEEK,{position:.5})),h&&y&&n.emit(a.PLAYBACK_STARTED,y),E>m.buffer.start&&!d&&(!c||h&&w)&&(v?(v=!1,g=!0,b||(p.debugEnabled()&&p.debug("pausing to buffer during playback"),n.emit(s.PAUSE)),n.emit(a.BUFFERING)):v=!0))}else p.detailEnabled()&&p.detail("no data in play stats")}}],w=[{type:f.RECOVER,listener:function(){p.debugEnabled()&&p.debug("media error recover"),d=!0}}];function D(e){R=e.data.streamInfo.prerollDuration?e.data.streamInfo.prerollDuration:0}function U(e){var t=!0;if(e.length){for(var n=e[0],r=1;r<e.length;r++)if(e[r]!==n){t=!1;break}return t}return!1}function P(e,t,n){e.length===n&&e.shift(),e.push(t)}function L(e,n){for(var r=0;e&&r<e.length;++r)if(e.start(r)<=n+1e-6&&e.end(r)>=n-1e-6||t.isTridentBrowser&&n<=.3&&e.start(r)<=5&&e.end(r)-e.start(r)>.2)return r;return-1}return i.add({target:n,listeners:O}),p.debugEnabled()&&p.debug("add control listeners"),i.add({target:n,listeners:N}),p.debugEnabled()&&p.debug("add media error listeners!"),i.add({target:n,listeners:w}),{destroy:function(){i.remove({target:n,listeners:O}),b&&(p.debugEnabled()&&p.debug("remove media listeners"),i.remove({target:n,listeners:I})),p.debugEnabled()&&p.debug("remove control listeners"),i.remove({target:n,listeners:N}),p.debugEnabled()&&p.debug("remove media error listeners"),i.remove({target:n,listeners:w})}}}}}.apply(t,r))||(e.exports=i)},7400:function(e,t,n){var r,i;r=[n(8333),n(8197),n(6627),n(5337),n(6637),n(9474),n(7572),n(947),n(549),n(9437),n(1667),n(3683)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c){return{create:function(f){var E,p,m,h=e.create("BufferTweaker"),g={max:8,min:.2,start:.5,target:1.2,limit:1.7},b=t.copy(g),_=t.copy(g),T=a.CLASSIC,y=0,S=0,A=0,v=-1,R=!1,O=!1,I=[{type:d.STREAM_INFO,listener:U},{type:d.STREAM_INFO_UPDATE,listener:function(e){U(e)}},{type:d.CONFIG,listener:function(e){var r;if((m=n.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&n.canUseHLS)||i.add({target:f,listeners:C}),e.data.config.playback.latencyControlMode)for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&a[o]===e.data.config.playback.latencyControlMode&&(T=a[o]);e.data.config.tweaks&&(e.data.config.tweaks.buffer&&((r=c.validateBuffer(e.data.config.tweaks.buffer)).success?(b=t.copy(e.data.config.tweaks.buffer),_=t.copy(b),O=!0):f.emit(s.BUFFER_TWEAKS_ERROR,{reason:r.reason})),e.data.config.tweaks.bufferDynamic&&!m&&T===a.CLASSIC&&((r=c.validateDynamic(e.data.config.tweaks.bufferDynamic)).success?(y=e.data.config.tweaks.bufferDynamic.offsetThreshold,S=e.data.config.tweaks.bufferDynamic.offsetStep,A=e.data.config.tweaks.bufferDynamic.cooldownTime):f.emit(s.BUFFER_TWEAKS_ERROR,{reason:r.reason}))),L()}},{type:u.FRAME_DROP,listener:function(e){if(h.debugEnabled()&&h.debug("frame drop with type "+e.data.type),n.isFirefox&&T===a.CLASSIC)switch(h.debugEnabled()&&h.debug("frame drop on firefox in classic mode with type "+e.data.type),e.data.type){case"extreme":case"high":case"medium":h.debugEnabled()&&h.debug("frame drop"),h.debugEnabled()&&h.debug(""+JSON.stringify(e)),h.debugEnabled()&&h.debug("current buffer: "+JSON.stringify(b)),h.debugEnabled()&&h.debug("limit: "+_.limit+", start: "+b.start),b.start<_.limit?(h.debugEnabled()&&h.debug("start lower then limit -> tweaking"),P(b.target-b.start),L()):h.debugEnabled()&&h.debug("start higher then limit -> tweaking denied");break;case"low":h.debugEnabled()&&h.debug("just low, no tweaking necessary")}}}],N=[{type:l.PLAY_STATS,listener:function(e){var t=e.data.stats.buffer.delay.current;t&&p.add(t),!R&&n.isTridentBrowser&&t>2&&(b.target=t,b.limit=b.target+1.5,L()),R=!0}},{type:o.BUFFERING,listener:function(){D(!0);var e=((new Date).getTime()-v)/1e3;-1!==v&&e<y&&b.start<_.limit&&(h.warnEnabled()&&h.warn("buffering "+e+" seconds after stream started, raising buffer values"),P(S),L()),w(),clearInterval(E),E=0}}],C=[{type:o.PLAYBACK_STARTED,listener:function(){D(!1),v=(new Date).getTime(),R&&A&&(E=setInterval((function(){h.debugEnabled()&&h.debug("stream running stable since "+A+" sec"),h.debugEnabled()&&h.debug("buffer min: "+p.minimum+", max: "+p.maximum),b.start>_.start?(b.start-_.start<S?(h.debug("normalize cooldown"),P()):(h.debug(-S+" sec cooldown"),P(-S)),L()):(h.debugEnabled()&&h.debug("no cooldown"),clearInterval(E),E=0)}),1e3*A))}}];function w(){p=r.create(10*A)}function D(e){i.add({target:f,listeners:e?C:N}),i.remove({target:f,listeners:e?N:C})}function U(e){e.data.streamInfo.videoInfo&&e.data.streamInfo.haveVideo&&e.data.streamInfo.videoInfo.frameRate<=30&&n.isTridentBrowser&&!O&&(b=t.copy(g),P(Math.sqrt(30)-Math.sqrt(e.data.streamInfo.videoInfo.frameRate),1),b.start+=(b.target-b.start)/2,b.min+=(b.start-b.min)/2,L())}function P(e,t){t=t||1,["start","target","limit"].forEach((function(n){void 0===typeof e||isNaN(t)?b[n]=_[n]:(b[n]*=t,b[n]+=e)}))}function L(){h.debugEnabled()&&h.debug("new buffer: "+JSON.stringify(b)),h.debugEnabled()&&h.debug("latencyControlMode: "+T),f.emit(s.BUFFER_TWEAKS_CREATED,{tweaks:{buffer:t.copy(b)},latencyControlMode:T})}return h.debugEnabled()&&h.debug("initialize"),w(),i.add({target:f,listeners:I}),{destroy:function(){p=null,[I,N,C].forEach((function(e){i.remove({target:f,listeners:e})}))}}}}}.apply(t,r))||(e.exports=i)},1733:function(e,t,n){var r,i;r=[n(8333),n(6637),n(8250),n(9437)],void 0===(i=function(e,t,n,r){return{create:function(i){var a,o=e.create("FakeAudioContext"),s=[{type:r.STREAM_INFO,listener:function(e){u(),e.data.streamInfo.haveAudio||(window.AudioContext=window.AudioContext||window.webkitAudioContext||function(){return{close:function(){}}},a=new AudioContext)}},{type:n.PAUSE,listener:u}];function u(){a&&"function"==typeof a.close&&(a.close(),a=null)}return o.debugEnabled()&&o.debug("initialize"),t.add({target:i,listeners:s}),{destroy:function(){u(),t.remove({target:i,listeners:s})}}}}}.apply(t,r))||(e.exports=i)},1104:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(1241),n(9437),n(92),n(6273),n(549),n(855),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d){return{create:function(a){var l,c=e.create("FrameCorruptedHandler"),f=!1,E=!1,p=!1,m=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(l=e.data.state){case d.PLAYING:case d.BUFFERING:break;case d.PAUSED:p=!1}}},{type:i.CONFIG,listener:function(e){E=t.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&t.canUseHLS}},{type:i.DOCUMENT_VISIBLE,listener:h},{type:i.DOCUMENT_HIDDEN,listener:h},{type:s.FRAME_CORRUPTED,listener:function(e){if(!p&&(c.debugEnabled()&&c.debug("frame corrupted"),c.debugEnabled()&&c.debug(JSON.stringify(e.data)),"extreme"===e.data.type&&!f&&E&&l===d.PLAYING)){c.debugEnabled()&&c.debug("visible and extreme frame corrupted detected (hls)");var t={code:u.MEDIA_ERR_HLS_VIDEO_DECODE};a.emit(r.ERROR,t)}}},{type:o.RECOVER,listener:function(){c.debugEnabled()&&c.debug("media error recover"),p=!0}},{type:o.RECOVERED,listener:function(){c.debugEnabled()&&c.debug("media error recovered"),p=!1}}];function h(e){f=e.name===i.DOCUMENT_HIDDEN,c.debug(f?"hidden":"visible")}return c.debugEnabled()&&c.debug("initialize"),n.add({target:a,listeners:m}),{destroy:function(){n.remove({target:a,listeners:m})}}}}}.apply(t,r))||(e.exports=i)},8394:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(8250),n(9437),n(3135),n(549),n(92),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u){return{create:function(t){var d,l=e.create("FrameDropHandler"),c=[],f=!1,E=0,p=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(d=e.data.state){case u.PLAYING:case u.BUFFERING:break;case u.PAUSED:g()}}},{type:i.RANDOM_ACCESS_POINT,listener:function(e){for(c.push(e.data.onRandomAccessPoint.streamTime/1e3+E/1e3);c.length>10;)c.shift()}},{type:a.TIME_OFFSET,listener:function(e){E=e.data.offset}},{type:o.FRAME_DROP,listener:function(e){var n;l.debugEnabled()&&l.debug("frame drop"),l.debugEnabled()&&l.debug(JSON.stringify(e.data)),"extreme"!==e.data.type||f||d!==u.PLAYING||(l.debugEnabled()&&l.debug("visible and extreme frame drop detected (edge)"),l.debugEnabled()&&l.debug("try seeking to last RAP to get stable playback"),n=c.length?c[c.length-1]:0,l.debugEnabled()&&l.debug("seeking to "+n),t.emit(r.SEEK,{position:n}))}}],m=[{type:s.VIEWPORT_VISIBLE,listener:h},{type:s.VIEWPORT_HIDDEN,listener:h}];function h(e){f=e.name!==s.VIEWPORT_VISIBLE,l.debugEnabled()&&l.debug("viewport changed to "+(f?"hidden":"visible"))}function g(){for(;c.length;)c.shift();E=0}return l.debugEnabled()&&l.debug("initialize"),g(),n.add({target:t,listeners:p}),n.add({target:t,listeners:m}),{destroy:function(){n.remove({target:t,listeners:p}),n.remove({target:t,listeners:m})}}}}}.apply(t,r))||(e.exports=i)},3786:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(8250),n(9437),n(92),n(492)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s){var u,d,l=.5,c=e.create("GapHandler"),f=0,E=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){u=e.data.state,n.remove({target:s,listeners:p}),(t.isTridentBrowser&&u===o.PLAYING||!t.isTridentBrowser&&u!==o.PAUSED)&&n.add({target:s,listeners:p})}}],p=[{type:a.TIME_UPDATE,listener:function(e){if(function(e){return e.length>0}(e.data.buffered)){var t=e.data.buffered,n=e.data.currentTime,i=h(n,t),a=g(n,t),o=b(t),u=(T=d,y=n,Math.abs(T-y)<.001),E=function(e,t){var n=t.length;return n>1&&t.end(n-2)<e&&e<t.start(n-1)}(n,t);if(u||E){if(++f<10)return;if(a>i&&_(t,a)>l&&m(n,t)||a>i&&o>a&&_(t,a)<l&&_(t,o)>l&&m(n,t)||a===i&&_(t,o)>l&&function(e,t){return e<t.start(h(e,t))}(n,t)){var p=t.start(o>a&&_(t,a)<l?b(t):g(n,t));c.debugEnabled()&&c.debug("recover gap, seek to "+p,3),s.emit(r.SEEK,{position:p}),s.emit(r.PLAY,{external:!1})}}d=n,f=0}var T,y}}];function m(e,t){return e+.2>=t.end(h(e,t))}function h(e,t){for(var n=0;n<t.length-1&&t.start(n+1)<=e;)n++;return n}function g(e,t){for(var n=0;n<t.length-1&&t.start(n)<=e;)n++;return n}function b(e){return e.length-1}function _(e,t){return t<e.length?e.end(t)-e.start(t):0}return c.debugEnabled()&&c.debug("initialize"),n.add({target:s,listeners:E}),{destroy:function(){n.remove({target:s,listeners:E}),n.remove({target:s,listeners:p})}}}}}.apply(t,r))||(e.exports=i)},8245:function(e,t,n){var r,i;r=[n(8333),n(6637),n(1241),n(9437),n(6273),n(855),n(492)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s){var u,d,l,c,f,E=e.create("HLSInterruptHandler"),p=!1,m=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(u=e.data.state){case o.LOADING:E.debugEnabled()&&E.debug("loading"),p||(E.debugEnabled()&&E.debug("not online at loading, set waitForOnline to true"),d=!0,h(!0));break;case o.PLAYING:E.debugEnabled()&&E.debug("playing"),g();break;case o.BUFFERING:E.debugEnabled()&&E.debug("buffering"),f||(E.debugEnabled()&&E.debug("set buffering timeout"),f=setTimeout((function(){E.debugEnabled()&&E.debug("buffering timeout fired"),clearTimeout(f),f=0,u!==o.BUFFERING||c||(p?(E.debugEnabled()&&E.debug("online at buffering timeout"),performance.now()-l.online<5e3&&(E.debugEnabled()&&E.debug("was offline before buffering timeout"),h(!1))):(E.debugEnabled()&&E.debug("not online at buffering timeout, set waitForOnline to true"),d=!0,h(!0)))}),2e3))}}},{type:r.NETWORK_ONLINE,listener:function(){E.debugEnabled()&&E.debug("network online"),p=!0,l.online=performance.now(),u!==o.LOADING&&u!==o.BUFFERING||!d||c||(E.debugEnabled()&&E.debug("active state & waited for online"),h(!1))}},{type:r.NETWORK_OFFLINE,listener:function(){E.debugEnabled()&&E.debug("network offline"),p=!1,l.offline=performance.now()}},{type:i.RECOVER,listener:function(){E.debugEnabled()&&E.debug("media error recover"),c=!0}},{type:i.RECOVERED,listener:function(){E.debugEnabled()&&E.debug("media error recovered"),c=!1}}];function h(e){E.debugEnabled()&&E.debug("emit buffer underrun"),setTimeout((function(){s.emit(n.ERROR,{code:a.MEDIA_ERR_HLS_BUFFER_UNDERRUN,reconnect:!0,waiting:!!e}),!e&&g()}),200)}function g(){E.debugEnabled()&&E.debug("reset"),d=!1,l={online:0,offline:0},c=!1}return E.debugEnabled()&&E.debug("initialize"),t.add({target:s,listeners:m}),g(),{destroy:function(){t.remove({target:s,listeners:m})}}}}}.apply(t,r))||(e.exports=i)},6864:function(e,t,n){var r,i;r=[n(8333),n(8197),n(6637),n(1241),n(9437),n(1667),n(92),n(6273),n(855),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d){return{create:function(o){var l,c,f,E=e.create("HLSJumpHandler"),p=[{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(n.remove({target:o,listeners:m}),e.data.state){case d.PAUSED:f=!1;break;case d.PAUSING:E.debugEnabled()&&E.debug("entering paused state"),l=null;break;case d.PLAYING:n.add({target:o,listeners:m})}}},{type:s.RECOVER,listener:function(){E.debugEnabled()&&E.debug("media error recover"),f=!0,l=null}},{type:s.RECOVERED,listener:function(){E.debugEnabled()&&E.debug("media error recovered"),f=!1}}],m=[{type:a.PLAY_STATS,listener:function(e){if(!f){if(c===e.data.index&&l&&void 0!==l.currentTime&&e.data.stats.currentTime<l.currentTime-2){var n={code:u.MEDIA_ERR_HLS_JUMP,currentTime:{last:l.currentTime,current:e.data.stats.currentTime}};o.emit(r.ERROR,n)}c=e.data.index,l=t.copy(e.data.stats)}}}];return E.debugEnabled()&&E.debug("initialize"),n.add({target:o,listeners:p}),{destroy:function(){n.remove({target:o,listeners:p})}}}}}.apply(t,r))||(e.exports=i)},8680:function(e,t,n){var r,i;r=[n(8333),n(6627),n(8197),n(6637),n(8250),n(9437),n(1747),n(92),n(5853),n(6273),n(3778),n(3890),n(492),n(5493)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E){var p=e.create("MediaElementHandler");return{create:function(e){var m,h,g,b,_,T,y,S,A,v,R,O,I,N,C,w,D,U,P,L,M,F,k,x,B,H=[{type:a.CONFIG,listener:function(e){var n;ye(e)&&(n=e.data.config,_=n,m=t.mustUseHLS||_.playback.allowSafariHlsFallback&&t.canUseHLS,h=0,M=0,Ve(y=_.source.startIndex),_.playback&&(Y=!!_.playback.muted,q=!!_.playback.automute,Q=_.playback.crossOrigin))}},{type:i.CREATE_VIDEO,listener:function(t){var n,r;ye(t)&&(t.data.elementIds&&(C=t.data.elementIds),t.data.container&&(w=t.data.container)),M=C.length;for(var i=0;i<M;i++)r={elementId:C[i],index:i},n=o.create(e,r.elementId,w,r.index,m,Y,q,!1,Q),V.push(n),ae[i]=[];e.emit(s.ELEMENT_CREATED,{video:pe(n[h])}),e.emit(s.VOLUME_CHANGE,{muted:Y,volume:z}),he(V[h]),ke(h),be(V[h]),x=Ge(),B=Ge()}},{type:i.DESTROY_VIDEO,listener:function(){for(var e=0;e<V.length;e++)(t=V[e])&&t.destroyVideo();var t}},{type:i.VIDEO_SOURCE,listener:function(e){var t,n,r=Me();p.debugEnabled()&&p.debug("received video source"),p.debugEnabled()&&p.debug("src: "+e.data.src),p.debugEnabled()&&p.debug("type: "+e.data.type),ye(e)&&(O=R||e.data.src,t=!(R=e.data.src).length,I=e.data.type,n=(r||j)&&W?N:h,p.debugEnabled()&&p.debug("sourceTemplate set"),V.length&&(!m||m&&(t||!F&&(W&&j||!W&&!j)))&&(we(V[n]),De(V[n]),m||Te(V[n]),m&&j&&ce(V[N]),W&&!t&&(p.debugEnabled()&&p.debug("call play while playing after video source received"),de(V[n],void 0,r)),Se(),Ae(),t||(p.debugEnabled()&&p.debug("add time update interval for play stats"),ve(),void 0!==Ee(V[n])&&(p.debugEnabled()&&p.debug("add quality update interval"),Re()))))}},{type:i.PLAY,listener:function(e){if(V[h]){if(m){he(V[h]);var t=h?0:1;ge(V[t]),(!e||e&&e.data.external)&&((n=V[t])&&(n.setMute(),n.unlock())),(!e||e&&e.data.external)&&!Y&&ue()}var n;p.debugEnabled()&&p.debug("onPlay index: "+h),de(V[h],e,!1)}}},{type:i.PAUSE,listener:function(e){p.debugEnabled()&&p.debug("onPause index: "+h),le(V[h],e,!1),isNaN(N)||le(V[N],e,!1),He(),x=Ge(),function(){for(var e in ae)if(Object.prototype.hasOwnProperty.call(ae,e))for(;ae[e].length;)ae[e].pop()}()}},{type:i.MUTE,listener:function(){p.debugEnabled()&&p.debug("mute index: "+h),ce(V[h],!0)}},{type:i.UNMUTE,listener:ue},{type:i.SEEK,listener:function(e){p.debugEnabled()&&p.debug("onSeek index: "+h),function(e,t){e&&e.seek(t)}(V[h],e)}},{type:i.SET_VOLUME,listener:function(e){ye(e)&&(p.debugEnabled()&&p.debug("onSetVolume index: "+h),z=Math.min(1,Math.max(0,e.data.volume)),me(V[h],z))}},{type:i.SET_RATE,listener:function(e){if(ye(e)){p.debugEnabled()&&p.debug("onSetRate index: "+h);var t=e.data.rate;!function(e,t){e&&e.setRate(t)}(V[h],t)}}},{type:u.LOAD_START,listener:se},{type:u.PROGRESS,listener:se},{type:u.SUSPEND,listener:se},{type:u.ABORT,listener:se},{type:u.EMPTIED,listener:se},{type:u.STALLED,listener:se},{type:u.PLAY,listener:se},{type:u.PAUSE,listener:se},{type:u.LOADED_META_DATA,listener:se},{type:u.LOADED_DATA,listener:se},{type:u.WAITING,listener:se},{type:u.ERROR,listener:se},{type:u.PLAYING,listener:se},{type:u.CAN_PLAY,listener:se},{type:u.CAN_PLAY_THROUGH,listener:se},{type:u.SEEKING,listener:se},{type:u.SEEKED,listener:se},{type:u.TIME_UPDATE,listener:se},{type:u.QUALITY_UPDATE,listener:se},{type:u.ENDED,listener:se},{type:u.RATE_CHANGE,listener:se},{type:u.DURATION_CHANGE,listener:se},{type:u.VOLUME_CHANGE,listener:se},{type:u.ELEMENT_CREATED,listener:se},{type:u.PLAY_START_SUCCESS,listener:se},{type:u.PLAY_START_ERROR,listener:se},{type:u.VIEWPORT_VISIBLE,listener:se},{type:u.VIEWPORT_HIDDEN,listener:se},{type:a.SWITCH_STREAM_INIT,listener:function(e){W&&Ue(e)}},{type:a.SWITCH_STREAM_SUCCESS,listener:function(e){m&&ye(e)&&(W?D&&e.data.id===D.id&&j&&(K=!0,j=!1,X=!0,Ve(D.entry.index)):Object.prototype.hasOwnProperty.call(e.data,"entry")&&Object.prototype.hasOwnProperty.call(e.data.entry,"index")&&Ve(e.data.entry.index))}},{type:a.UPDATE_SOURCE_INIT,listener:function(e){W&&(Ue(e),P=void 0)}},{type:a.UPDATE_SOURCE_SUCCESS,listener:function(e){m&&ye(e)&&(W?D&&e.data.id===D.id&&j&&(K=!0,j=!1,X=!0,Ve(L)):Object.prototype.hasOwnProperty.call(e.data,"source")&&Object.prototype.hasOwnProperty.call(e.data.source,"startIndex")&&Ve(e.data.source.startIndex))}},{type:a.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case f.PAUSED:(j||K)&&void 0!==N&&-1===[E.BUFFER,E.STREAM_NOT_FOUND].indexOf(e.data.reason)?(he(V[N]),ge(V[h]),ke(N),h=N,F?(Ve(F.source.startIndex),y=F.source.startIndex):Ve(L),Ce(),Ne(),F=void 0):(ge(V[N]),Ce(),Ne(),E.STREAM_NOT_FOUND===e.data.reason&&P&&(Ve(P),P=void 0)),W=!1,X=!1,ie=!1,We(!1),je(!1),Ke(!1),ze(!1),qe(!1),Qe(!1),Ye(!1);break;case f.PLAYING:}}},{type:d.RECOVER,listener:function(t){if(p.debugEnabled()&&p.debug("media error recover"),m)switch(p.debugEnabled()&&p.debug("recovering..."),We(t.data.code),t.data.code){case l.MEDIA.DECODE_ERROR:p.debugEnabled()&&p.debug("switch stream to recover after media error decode error"),e.emit(s.SWITCH_STREAM,{type:"recover"});break;case l.MEDIA.HLS_VIDEO_DECODE_ERROR:p.debugEnabled()&&p.debug("switch stream to recover after media error hls video decode error"),e.emit(s.SWITCH_STREAM,{type:"recover"});break;case l.MEDIA.HLS_BUFFER_UNDERRUN:t.data.reconnect?(p.debugEnabled()&&p.debug("get timeOffset from previous stats after media error hls ended"),xe(G[0]),p.debugEnabled()&&p.debug("switch stream to recover after media error hls ended"),e.emit(s.SWITCH_STREAM,{type:"recover"})):(p.debugEnabled()&&p.debug("call raw play to recover after media error hls ended"),xe(),de(V[h],{data:{hlsended:!0}},!1),te&&ne&&(p.debugEnabled()&&p.debug("was canPlay during switch & media error queued"),Qe(!1),je(!0)));break;case l.PLAYER.PLAYBACK_ERROR:p.debugEnabled()&&p.debug("get timeOffset from previous stats after media error playback error"),xe(G[0]),p.debugEnabled()&&p.debug("call raw pause after media error playback error"),le(V[h],{data:{error:!0}},!0),p.debugEnabled()&&p.debug("switch stream to recover after media error playback error"),e.emit(s.SWITCH_STREAM,{type:"recover"})}}},{type:d.QUEUED,listener:function(e){if(p.debugEnabled()&&p.debug("media error queued"),Ye(!0),j)switch(p.debugEnabled()&&p.debug("media error queued & switch initialized"),e.data.code){case l.MEDIA.HLS_VIDEO_DECODE_ERROR:case l.MEDIA.HLS_BUFFER_UNDERRUN:break;case l.PLAYER.PLAYBACK_ERROR:le(V[h],{data:{error:!0}},!0)}}},{type:d.UNQUEUED,listener:function(){p.debugEnabled()&&p.debug("media error unqueued"),Ye(!1),Qe(!1)}}],G=[],V=[],W=!1,Y=!1,j=!1,K=!1,z=1,q=!1,Q="not-set",X=!1,J=!1,$=!1,Z=!1,ee=!1,te=!1,ne=!1,re=!1,ie=!1,ae={0:[]},oe=!1;function se(t){if(t&&t.name&&t.data){var r=t.data.index;v=be(V[r]);var i,a=t.data,o=Xe(v,t.data,!1),d=t.name,c=function(e){return"mediaElementHandler."+function(e){var t=e.indexOf(".")+1;return e.substring(t)}(e)}(d);if(d!==u.TIME_UPDATE)for(ae[r].unshift(d);ae[r].length>20;)ae[r].pop();if(h===r)switch(d){case u.SEEKING:ie===l.MEDIA.HLS_BUFFER_UNDERRUN&&ze(!0),e.emit(c,o);break;case u.SEEKED:ie===l.MEDIA.HLS_BUFFER_UNDERRUN&&qe(!0),e.emit(c,o);break;case u.CAN_PLAY:if(ie===l.MEDIA.HLS_BUFFER_UNDERRUN){if(_e(V[r])&&(p.debugEnabled()&&p.debug("video paused at can play in recovery on current media element"),p.debugEnabled()&&p.debug("call raw play"),de(V[r],{data:{hlsended:!0}},!1)),ee&&G[0].currentTime>a.currentTime&&a.currentTime<1)return ze(!1),qe(!1),p.debugEnabled()&&p.debug("pause because seek back during buffer recovery detected"),le(V[h],{data:{error:!0}},!0),p.debugEnabled()&&p.debug("switch stream to recover after seek back during buffering"),void e.emit(s.SWITCH_STREAM,{index:U,type:"recover"});p.debugEnabled()&&p.debug("now wait for progress for completion"),je(!0)}X||(!m||j||!W)&&m||(e.emit(c,o),X=!0,Ce()),Qe(!(!j||!ne));break;case u.PROGRESS:ie&&!j&&ie===l.MEDIA.HLS_BUFFER_UNDERRUN&&J&&(_e(V[r])||(p.debugEnabled()&&p.debug("video not paused & wait for progress in recovery on current media element"),Z&&!ee?p.debugEnabled()&&p.debug("ongoing seek, wait for seeked in next progress"):Z&&ee?(p.debugEnabled()&&p.debug("media error "+ie+" recovered in progress after seek"),ze(!1),qe(!1),e.emit(s.MEDIA_ERROR_RECOVERED,{data:{code:ie}}),We(!1),je(!1),p.debugEnabled()&&p.debug("merge pending time offset after seeked"),Be(!0),He()):Z||ee||(p.debugEnabled()&&p.debug("media error "+ie+" recovered in progress without seek"),e.emit(s.MEDIA_ERROR_RECOVERED,{data:{code:ie}}),We(!1),je(!1),He()))),e.emit(c,o);break;case u.PAUSE:ie===l.MEDIA.HLS_BUFFER_UNDERRUN&&p.debugEnabled()&&p.debug("pause event during recovery of 3101"),e.emit(c,o);break;case u.PLAY_START_ERROR:m&&!X?p.debugEnabled()&&p.debug("play start error but not initialized yet"):m&&X?(p.debugEnabled()&&p.debug("play start error and initialized"),p.debugEnabled()&&p.debug("emit play start error delayed"),setTimeout(function(t,n){e.emit(t,n)}.bind(null,c,o),100)):e.emit(c,o);break;default:e.emit(c,o)}else if(r===N&&N!==h&&W)switch(p.debugEnabled()&&p.debug("onMediaElementEvent switchMediaElementIndex: "+N),d){case u.PLAY:case u.DURATION_CHANGE:case u.LOADED_META_DATA:case u.LOADED_DATA:e.emit(c,o);break;case u.CAN_PLAY:p.debugEnabled()&&p.debug("onMediaElementEvent CAN_PLAY on switchMediaElementIndex: "+N),X||(p.debugEnabled()&&p.debug("onMediaElementEvent emit CAN_PLAY"),e.emit(c,o),X=!0);break;case u.CAN_PLAY_THROUGH:p.debugEnabled()&&p.debug("onMediaElementEvent CAN_PLAY_THROUGH on switchMediaElementIndex: "+N),K&&(p.debugEnabled()&&p.debug("onMediaElementEvent CAN_PLAY_THROUGH wait for progress"),Ke(!0),e.emit(c,o));break;case u.PROGRESS:p.debugEnabled()&&p.debug("onMediaElementEvent PROGRESS on switchMediaElementIndex: "+N),K&&$&&(p.debugEnabled()&&p.debug("onMediaElementEvent PROGRESS call mediaElementSwitch"),Ke(!1),m&&void 0!==N&&K&&(p.debugEnabled()&&p.debug("switch mediaElement after successfull "+(Me()?"updateSource":"switchStream")),Ne(),(i=V[N])&&i.showOnTop(),setTimeout((function(){p.debugEnabled()&&p.debug("handle mediaElement switch after timeout"),he(V[N]),ge(V[h]),ke(N),pe(V[h])&&(Y=pe(V[h]).muted),ce(V[h]),Y?ce(V[N]):fe(V[N]),ie&&(p.debugEnabled()&&p.debug("media error "+ie+" recovered in switch success"),e.emit(s.MEDIA_ERROR_RECOVERED,{data:{code:ie}}),We(!1)),oe||xe(),Be(!0),He(),le(V[h],void 0,!0),me(V[N],z),h=N,F?(K=!1,function(){if(F){var e="update"===F.type,t=F.entry.index,r=n.copy(F);F=void 0,p.debugEnabled()&&p.debug("handleQueuedSwitch"),Fe(r,t,e,!0)}}()):Ce()}),150)));break;case u.PLAYING:e.emit(c,o)}}}function ue(){p.debugEnabled()&&p.debug("unmute index: "+h);for(var e=0;e<V.length;e++)fe(V[e],!0);setTimeout((function(){for(var e=0;e<V.length;e++)e!==h&&ce(V[e],!1)}),50)}function de(e,t,n){e&&(e.play(t),n||(W=!0))}function le(e,t,n){e&&(e.pause(t),n||(W=!1))}function ce(e,t){e&&(t&&(Y=!0),e.setMute())}function fe(e,t){e&&(t&&(Y=!1),e.setUnmute())}function Ee(e){if(e)return e.getVideoPlaybackQuality()}function pe(e){if(e)return e.getVideoElement()}function me(e,t){e&&(e.setVolume(t),z=t)}function he(e){e&&e.show()}function ge(e){e&&e.hide()}function be(e){return e?(oe||(k=e.getMediaElementPayload()),k.timeOffset=x,k):{index:0}}function _e(e){if(e)return e.getVideoPausedState()}function Te(e){e&&e.loadVideo()}function ye(e,t){var n=e&&Object.prototype.hasOwnProperty.call(e,"data");return t?n&&Object.prototype.hasOwnProperty.call(e.data,t):n}function Se(){clearInterval(g),g=void 0}function Ae(){clearInterval(b),b=void 0}function ve(){g=setInterval(Oe,100)}function Re(){b=setInterval(Ie,1e3)}function Oe(){for(S=be(V[h]);G.length>2;)G.shift();G.push(S),e.emit(u.TIME_UPDATE,S)}function Ie(){(A=be(V[h])).quality=Ee(V[h]),e.emit(u.QUALITY_UPDATE,A)}function Ne(){T&&(clearTimeout(T),T=0)}function Ce(){p.debugEnabled()&&p.debug("reset switch variables"),N=void 0,X=!0,j=!1,K=!1,D=void 0,L=void 0}function we(e,t){e&&e.setSource({source:t?O:R})}function De(e){e&&e.setType({type:I})}function Ue(e){var t,r,i,a;if(m&&ye(e)&&Object.prototype.hasOwnProperty.call(e.data,"entry"))if(i=e.data.entry.index,t=F?"update"===F.type:Me(),r="update"===e.data.type,p.debugEnabled()&&p.debug((r?"updateSourceInit":"switchStreamInit")+" with entryIndex: "+i),re)p.debugEnabled()&&p.debug((r?"updating":"switching")+" is disabled, abort");else{if(!r&&ie&&"recover"!==e.data.type)return p.debugEnabled()&&p.debug("abort switch attempt during recovery"),(a=n.copy(e.data)).reason="recover",void Le(a);if(j&&(!t||t&&r)||F&&(!t||t&&r))p.debugEnabled()&&p.debug("abort ongoing "+(t?"update":"switch")+" process"),(a=n.copy(F||D)).reason="superseded",Le(a),Ne(),F?F=void 0:(le(V[N],void 0,!0),Ce());else if(j&&t&&!r||F&&t&&!r)return void(p.debugEnabled()&&p.debug("ongoing updateSource process!"));r||U!==i||"recover"===e.data.type?(p.debugEnabled()&&p.debug("new "+(r?"update":"switch")),K?(p.debugEnabled()&&p.debug("do not handle switch, still ongoing switch!"),p.debugEnabled()&&p.debug("add to queue!"),F=e.data):(p.debugEnabled()&&p.debug("handle switch without ongoing"),Fe(e.data,i,r))):(p.debugEnabled()&&p.debug("abort switch attempt on equal index"),(a=n.copy(e.data)).reason="equalsource",Ce(),Ne(),Le(a)),re=!0,setTimeout((function(){re=!1}),500)}}function Pe(){if(void 0!==N&&N!==h&&j&&!K){var t=n.copy(D);le(V[N],void 0,!0),Me()&&(Ve(void 0),e.emit(s.UPDATE_SOURCE_TIMEOUT,{code:l.STREAM.MEDIA_NOT_AVAILABLE,message:c.STREAM.MEDIA_NOT_AVAILABLE})),F=void 0,Ce(),function(t){e.emit(s.UPDATE_SOURCE_FAIL,{tag:t.options.tag,count:t.options.count,code:l.NETWORK.SOURCE_TIMEOUT,message:c.NETWORK.SOURCE_TIMEOUT,type:t.type,id:t.id})}(t)}}function Le(t){setTimeout((function(){e.emit(s.UPDATE_SOURCE_ABORT,{source:t.source,entry:t.entry,rule:t.rule,reason:t.reason,tag:t.tag,count:t.count,type:t.type,id:t.id})}),0)}function Me(){return D&&Object.prototype.hasOwnProperty.call(D,"type")&&"update"===D.type}function Fe(e,t,n,r){N=h?0:1,p.debugEnabled()&&p.debug("mediaElementPrepareSwitch on switchMediaElementIndex: "+N),X=!1,j=!0,D=e,n?(y=D.source.startIndex,L=y,r&&(we(V[N],y),De(V[N]),m||Te(V[N]),m&&j&&ce(V[N]),de(V[N],void 0,!0),Se(),Ae(),R.length&&(p.debugEnabled()&&p.debug("add time update interval for play stats"),ve(),void 0!==Ee(V[N])&&(p.debugEnabled()&&p.debug("add quality update interval"),Re())))):W&&(N=h?0:1,L=t,r&&(we(V[N],t),De(V[N]),m||Te(V[N]),m&&j&&ce(V[N]),de(V[N],void 0,!0))),W&&function(e){T&&Ne();var t=e&&Object.prototype.hasOwnProperty.call(e,"options")&&Object.prototype.hasOwnProperty.call(e.options,"timeout")?1e3*e.options.timeout:1e4;T=setTimeout((function(){Pe()}),t)}(D)}function ke(t){for(var n=V.map((function(e){return e.getVideoElement()})),r=0;r<n.length;r+=1)void 0!==typeof n[r]&&n[r]||n.pop();n.length-1<t&&(t=-1),e.emit(s.ACTIVE_VIDEO_ELEMENT_CHANGE,{videoElementList:n,activeVideoElement:t<0?null:n[t]})}function xe(e){p.debugEnabled()&&p.debug("set pending time offset"),e||(e=be(V[h])),oe=!0,B.currentTime=e.currentTime,p.debugEnabled()&&p.debug("new pending time offset: "+JSON.stringify(B))}function Be(e){p.debugEnabled()&&p.debug("apply pending time offset"),x=Xe(x,B,e),p.debugEnabled()&&p.debug("new time offset: "+JSON.stringify(x))}function He(){p.debugEnabled()&&p.debug("reset pending time offset"),oe=!1,B=Ge()}function Ge(){return!!m&&{currentTime:0}}function Ve(e){P=U,U=e}function We(e){p.debugEnabled()&&p.debug("set inRecovery to "+e),ie=e}function Ye(e){p.debugEnabled()&&p.debug("set isMediaErrorQueued to "+e),ne=e}function je(e){p.debugEnabled()&&p.debug("set waitForProgress to "+e),J=e}function Ke(e){p.debugEnabled()&&p.debug("set waitForProgressSwitch to "+e),$=e}function ze(e){p.debugEnabled()&&p.debug("set wasSeeking to "+e),Z=e}function qe(e){p.debugEnabled()&&p.debug("set wasSeeked to "+e),ee=e}function Qe(e){p.debugEnabled()&&p.debug("set wasCanPlayDuringSwitch to "+e),te=e}function Xe(e,t,n){for(var r in t)"object"!=typeof t[r]?Object.prototype.hasOwnProperty.call(e,r)&&!n||(n?e[r]+=t[r]:e[r]=t[r]):(Object.prototype.hasOwnProperty.call(e,r)||(e[r]={}),Xe(e[r],t[r],n));return e}return r.add({target:e,listeners:H}),{destroy:function(){Ae(),Se(),r.remove({target:e,listeners:H}),function(e){if(e){for(var t=0;t<e.length;t++)e[t].destroy();ke(-1),e=[]}}(V)}}}}}.apply(t,r))||(e.exports=i)},1747:function(e,t,n){var r,i;r=[n(8333),n(6627),n(803),n(6637),n(5304),n(3763),n(5853)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s,u,d,l,c,f,E,p,m){var h,g,b,_,T,y,S=e.create("MediaElementProxy "+u),A=!1,v=0,R=!0,O=!1,I=[];function N(){h&&(r.remove({target:h,listeners:I}),t.isTridentBrowser&&(y=void 0,["scroll","resize"].forEach((function(e){window.removeEventListener&&window.removeEventListener(e,M,!1),window.detachEvent&&window.detachEvent("on"+e,M)}))),H({source:""}),G(),h.firstChild&&h.removeChild(h.firstChild),h.parentNode&&h.parentNode.removeChild(h),A&&(S.debugEnabled()&&S.debug("releasing external video"),h.style.cssText=_,_=null,T&&T.appendChild(h)),h=null,b&&(b.parentNode&&b.parentNode.removeChild(b),b=null))}function C(){S.debugEnabled()&&S.debug("mute"),h&&(h.muted=!0)}function w(){S.debugEnabled()&&S.debug("unmute"),h&&(h.muted=!1)}function D(e){S.debugEnabled()&&S.debug("onPlay"),function(e){return new Promise((function(t){c&&(!e||e&&e.data.external||e&&e.data.unlocked)?v||(S.debugEnabled()&&S.debug("set load timeout before call play"),v=setTimeout((function(){if(v=0,h){S.debugEnabled()&&S.debug("load before call play");try{G(),P(1)}catch(e){S.debugEnabled()&&S.debug("err in onPlay Promise resolve")}t(e)}}),250)):t(e)}))}(e).then((function e(t){if(c&&(R=!1),h)return S.debugEnabled()&&S.debug("call play at currentTime "+h.currentTime),n.play(h).then((function(){t&&t.data&&t.data.unlocked?(O=!0,S.debugEnabled()&&S.debug("unlocked set to true"),S.debugEnabled()&&S.debug("play promise resolved, video element unlocked"),U(t)):(S.debugEnabled()&&S.debug("play promise resolved"),S.debugEnabled()&&S.debug("resolve play at currentTime "+h.currentTime),s.emit(o.PLAY_START_SUCCESS,{index:l}),p=!1)})).catch((function(n){n.error.name===i.ABORT_ERROR&&S.debugEnabled()&&S.debug("play promise rejected, abort error"),t&&t.data&&t.data.unlocked?S.debugEnabled()&&S.debug("play promise rejected, video element not activated"):(S.debugEnabled()&&S.debug("play promise rejected"),n.error.name!==i.NOT_ALLOWED_ERROR||h.muted||!E||p?n.error.name===i.NOT_ALLOWED_ERROR&&h&&h.muted&&p&&(S.debugEnabled()&&S.debug("unmute to reset"),w(),h.muted||(L({type:a.VOLUME_CHANGE}),p=!1)):(S.debugEnabled()&&S.debug("mute to play"),C(),h&&h.muted&&(p=!0,setTimeout(e.bind(null,{data:{external:!1}}),0))),(t&&t.data&&!t.data.external||p||(!h.muted&&!E||h.muted)&&c&&n.error.name===i.NOT_ALLOWED_ERROR)&&s.emit(o.PLAY_START_ERROR,{error:n.error,automuted:p,index:l}))}))}))}function U(e){S.debugEnabled()&&S.debug("onPause"),h&&(v&&(clearTimeout(v),v=0),h.pause(),c&&(R=!0,e&&e.data&&e.data.unlocked?S.debugEnabled()&&S.debug("paused after state check"):e&&e.data&&e.data.error&&S.debugEnabled()&&S.debug("pause after media error"),H({source:""}),G(),b&&b.tagName&&"IFRAME"===b.tagName&&(b.contentWindow&&b.contentWindow.location?b.contentWindow.location.reload(!0):F())),P(1))}function P(e){c&&(t.isExactIOS15||t.isExactIOS16||t.isExactIOS17)&&1===e&&(e=1+1e-7),h&&e&&h.playbackRate!==e&&(S.debugEnabled()&&S.debug("set rate to: "+e),h.playbackRate=e)}function L(e){var t={index:l,currentTime:h.currentTime,paused:h.paused};switch(e.type){case a.ERROR:t.code=h.error&&"number"==typeof h.error.code?h.error.code:200;break;case a.VOLUME_CHANGE:t.muted=h.muted,t.volume=h.volume;break;case a.CAN_PLAY:t.haveVideo=void 0!==h.videoTracks&&h.videoTracks.length>0,t.haveAudio=void 0!==h.audioTracks&&h.audioTracks.length>0,t.videoWidth=h.videoWidth,t.videoHeight=h.videoHeight}S.detailEnabled()&&S.detail(e.type+" - (index: "+l+", paused: "+V()+", currentTime: "+h.currentTime+", latency: "+((h.buffered.length?h.buffered.end(h.buffered.length-1):0)-h.currentTime)+")"),(!c||e.type!==a.CAN_PLAY||c&&e.type===a.CAN_PLAY&&!R)&&s.emit("mediaElementProxy."+e.type,t)}function M(){var e=h&&"function"==typeof h.getBoundingClientRect?h.getBoundingClientRect():{top:0,left:0,bottom:0,right:0},t=window.innerHeight||document.documentElement.clientHeight,n=window.innerWidth||document.documentElement.clientWidth,r=e.top<=t&&e.top+e.height>=0,i=e.left<=n&&e.left+e.width>=0,a=r&&i;a!==y&&(S.debugEnabled()&&S.debug("viewport changed to "+(a?"visible":"hidden")),y=a,s.emit(a?o.VIEWPORT_VISIBLE:o.VIEWPORT_HIDDEN,{index:l}))}function F(){S.debugEnabled()&&S.debug("iOS11 detected, using iframe hack");var e={name:"h5live-iframe-"+l,frameBorder:0,scrolling:"no",style:{cssText:"z-index: 0;width: 100%;height: 100%;position: absolute;display: none;top: 0;left: 0;"},h5liveIndex:l,h5liveActive:!1};b||(b=d.querySelector('iframe[name="'+e.name+'"]')),b&&b.name===e.name&&(b.contentDocument&&b.contentDocument.body&&b.contentDocument.body.firstChild&&b.contentDocument.body.removeChild(b.contentDocument.body.firstChild),b.removeEventListener("load",k),b.removeEventListener("unload",x),b.parentNode&&b.parentNode.removeChild(b),b=null),B(b=document.createElement("iframe"),e),b.addEventListener("load",k),b.addEventListener("unload",x),d.appendChild(b)}function k(e){var t=e.target.contentDocument.body;t&&(B(t,{style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",margin:"0",padding:"0"}}),t.firstChild||(t.appendChild(h),r.remove({target:h,listeners:I}),r.add({target:h,listeners:I})))}function x(e){var t=e.target.contentDocument.body;t&&t.firstChild&&t.removeChild(h)}function B(e,t){Object.keys(t).forEach((function(n){"object"!=typeof t[n]?e[n]=t[n]:Object.keys(t[n]).forEach((function(r){e[n][r]=t[n][r]}))}))}function H(e){e&&Object.prototype.hasOwnProperty.call(e,"source")&&(g.src=e.source)}function G(){h&&h.load()}function V(){return h?h.paused:void 0}function W(e,t){e.h5liveActive=t}return function(){var e=[a.TIME_UPDATE];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&-1===e.indexOf(a[t])&&I.push({type:a[t],listener:L})}(),function(){var e={id:u,autoplay:!1,controls:!1,playsInline:!0,muted:f,volume:1,style:{backgroundColor:"transparent"},h5liveIndex:l,h5liveActive:!1};switch(m){case"anonymous":case"use-credentials":e.crossOrigin=m}(t.isIOS11||!t.isIOS&&t.mustUseHLS)&&(e.style.width="100vw",e.style.height="100vh"),h=document.getElementById(u),A=!!h,S.debugEnabled()&&S.debug("using "+(A?"external ":"generated ")+"video element"),A?(T=h.parentNode,_=h.style.cssText,h.style.cssText=""):h=document.createElement("video"),B(h,e)}(),g=document.createElement("source"),h.appendChild(g),t.isIOS11||!t.isIOS&&t.mustUseHLS?F():(S.debugEnabled()&&S.debug("appending video to container"),d.appendChild(h),r.add({target:h,listeners:I}),t.isTridentBrowser&&(["scroll","resize"].forEach((function(e){window.addEventListener&&window.addEventListener(e,M,!1),window.attachEvent&&window.attachEvent("on"+e,M)})),M())),{destroy:function(){N()},play:D,pause:U,loadVideo:G,setSource:H,setType:function(e){h&&e&&(h.type=e)},setVolume:function(e){h&&!isNaN(e)&&e>=0&&e<=1&&h.volume!==e&&(S.debugEnabled()&&S.debug("set volume: "+e),h.volume=e)},setMute:C,setUnmute:w,setRate:P,destroyVideo:N,seek:function(e){if(h&&e&&e.data&&!isNaN(e.data.position)&&e.data.position>=0){var t=Math.round(1e6*e.data.position)/1e6;S.debugEnabled()&&S.debug("seek to: "+e.data.position+" (rounded: "+t+"), from: "+h.currentTime),e.data.fast&&void 0!==h.fastSeek?h.fastSeek(t):h.currentTime=t}},getVideoPlaybackQuality:function(){return h&&"function"==typeof h.getVideoPlaybackQuality?h.getVideoPlaybackQuality():void 0},getVideoElement:function(){return h||void 0},getMediaElementPayload:function(){return{currentTime:h.currentTime,buffered:h.buffered,played:h.played,playbackRate:h.playbackRate,index:l}},show:function(){b&&(b.style.display="block",b.style.zIndex=1,W(b,!0)),h&&(h.style.display="block",h.style.zIndex=1,W(h,!0))},hide:function(){b&&(b.style.display="block",b.style.zIndex=0,W(b,!1)),h&&(h.style.display="block",h.style.zIndex=0,W(h,!1))},getIndex:function(){return l},setIndex:function(e){l=e},showOnTop:function(){S.debugEnabled()&&S.debug("show index "+l+" on top"),b?(b.style.display="block",b.style.zIndex=10):h&&(h.style.display="block",h.style.zIndex=10)},unlock:function(){O?S.debugEnabled()&&S.debug("skip check because is unlocked"):(S.debugEnabled()&&S.debug("2nd vid try unlock with silent play attempt"),D({data:{unlocked:!0}}))},getVideoPausedState:V}}}}.apply(t,r))||(e.exports=i)},1264:function(e,t,n){var r,i;r=[n(8333),n(3778),n(3890),n(6637),n(9437),n(1241),n(92),n(5751),n(6273),n(855),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){return{create:function(c){var f=e.create("MediaErrorHandler"),E=null,p=0,m=0,h=0,g=[],b=[t.MEDIA.DECODE_ERROR,t.MEDIA.HLS_VIDEO_DECODE_ERROR,t.MEDIA.MEDIA_SOURCE_ENDED,t.MEDIA.HLS_BUFFER_UNDERRUN,t.PLAYER.PLAYBACK_ERROR],_=[],T=[],y=null,S=0,A=3,v=[{type:o.ERROR,listener:R},{type:o.MEDIA_ERROR_RECOVERED,listener:O},{type:s.ERROR,listener:R},{type:s.MEDIA_ERROR_RECOVERED,listener:O},{type:a.ERROR,listener:R},{type:i.APPLICATION_STATE_CHANGE,listener:function(e){switch(e.data.state){case l.PAUSED:!function(){for(f.debugEnabled()&&f.debug("reset media error recover values");g.length;)g.shift();E=null,p=0,m=0,F()}();break;case l.PLAYING:}}},{type:i.CONFIG,listener:function(e){var t;isNaN(e.data.config.playback.mediaErrorRecoveries)?A=0:(0,1/0,t=e.data.config.playback.mediaErrorRecoveries,A=isNaN(t)?0:Math.min(Infinity,Math.max(0,t)))}},{type:i.SWITCH_STREAM_INIT,listener:function(e){P(e.data.type)?F():(F(),k(_,e.data.id))}},{type:i.SWITCH_STREAM_SUCCESS,listener:function(e){P(e.data.type)||(x(_,e.data.id),F(),M())}},{type:i.SWITCH_STREAM_FAIL,listener:function(e){P(e.data.type)?I():(x(_,e.data.id),L())}},{type:i.SWITCH_STREAM_ABORT,listener:function(e){P(e.data.type)?I():(x(_,e.data.id),L())}},{type:i.UPDATE_SOURCE_INIT,listener:function(e){F(),M(),k(T,e.data.id)}},{type:i.UPDATE_SOURCE_SUCCESS,listener:function(e){F(),M(),x(T,e.data.id)}},{type:i.UPDATE_SOURCE_FAIL,listener:function(e){F(),M(),x(T,e.data.id)}},{type:i.UPDATE_SOURCE_ABORT,listener:function(e){F(),M(),x(T,e.data.id)}}];function R(e){switch(E=e.data,f.debugEnabled()&&f.debug("media error occured with native code "+E.code),E.code){case d.MEDIA_ERR_ABORTED:E.code=t.MEDIA.ABORTED,E.message=n.MEDIA.ABORTED;break;case d.MEDIA_ERR_NETWORK:E.code=t.MEDIA.DOWNLOAD_ERROR,E.message=n.MEDIA.DOWNLOAD_ERROR;break;case d.MEDIA_ERR_DECODE:D()&&f.debugEnabled()&&f.debug("in switch or update, but continue handling from error "+E.code),E.code=t.MEDIA.DECODE_ERROR,E.message=n.MEDIA.DECODE_ERROR;break;case d.MEDIA_ERR_HLS_VIDEO_DECODE:if(D())return void(f.debugEnabled()&&f.debug("return from error "+E.code));E.code=t.MEDIA.HLS_VIDEO_DECODE_ERROR,E.message=n.MEDIA.HLS_VIDEO_DECODE_ERROR;break;case d.MEDIA_ERR_SRC_NOT_SUPPORTED:E.code=t.MEDIA.NOT_SUPPORTED,E.message=n.MEDIA.NOT_SUPPORTED;break;case d.MEDIA_ERR_MEDIA_SOURCE_ENDED:D()&&f.debugEnabled()&&f.debug("in switch or update, but continue handling from error "+E.code),E.code=t.MEDIA.MEDIA_SOURCE_ENDED,E.message=n.MEDIA.MEDIA_SOURCE_ENDED;break;case d.MEDIA_ERR_HLS_JUMP:if(U(e,t.PLAYER.PLAYBACK_ERROR))return void(f.debugEnabled()&&f.debug("return from error "+E.code));E.code=t.PLAYER.PLAYBACK_ERROR,E.message=n.PLAYER.PLAYBACK_ERROR;break;case d.MEDIA_ERR_HLS_BUFFER_UNDERRUN:if(E.waiting&&p<A)return void c.emit(a.OFFLINE_BUFFER_UNDERRUN);if(!E.reconnect&&U(e,t.MEDIA.HLS_BUFFER_UNDERRUN))return void(f.debugEnabled()&&f.debug("return from error "+E.code));E.code=t.MEDIA.HLS_BUFFER_UNDERRUN,E.message=n.MEDIA.HLS_BUFFER_UNDERRUN}(function(e){var t;if(-1===b.indexOf(e))t=!1,f.debugEnabled()&&f.debug("media error with code "+e+" not recoverable, trigger error");else if(p>=A)t=!1,f.debugEnabled()&&f.debug("media error with code "+e+" recoverable, but recover threshold "+A+" reached within "+(p?(Math.floor(performance.now())-g[0])/1e3:0)+" seconds, trigger error");else{for(t=!0,g.push(Math.floor(performance.now()));g[g.length-1]-g[0]>6e4;)g.shift();p=g.length,m++;var n=6e4-(g[p-1]-g[0]),r=A-p;f.debugEnabled()&&f.debug("recovering from media error "+e+", recovery "+p+"/"+A+" within the last 60 seconds ("+m+" total)"),f.debugEnabled()&&f.debug(r+(r<=1?" recoveries":" recovery")+" allowed within remaining time of "+n/1e3+" seconds"),F(),h=setTimeout(I,1e4),c.emit(u.RECOVER,{count:p,max:A,total:m,code:e,remainingTime:n,reconnect:!!E.reconnect})}return t})(E.code)||(f.debugEnabled()&&f.debug('trigger "'+u.ERROR+'" with translated code '+E.code+' and message "'+E.message+'"'),c.emit(u.ERROR,E))}function O(e){f.debugEnabled()&&f.debug("recovery succeed from error "+e.data.code),F(),c.emit(u.RECOVERED,{data:{code:e.data.code}})}function I(){F(),f.debugEnabled()&&f.debug("recovery failed from error with translated code "+E.code+' and message "'+E.message+'"'),setTimeout(N,200)}function N(){c.emit(u.ERROR,E)}function C(){f.debugEnabled()&&f.debug("inSwitch has length "+_.length);var e=!1;return _.length&&(e=!0),e}function w(){f.debugEnabled()&&f.debug("inUpdate has length "+T.length);var e=!1;return T.length&&(e=!0),e}function D(){f.debugEnabled()&&f.debug("check if switch or update");var e=!1;return C()?(f.debugEnabled()&&f.debug("still in switch"),e=!0):w()&&(f.debugEnabled()&&f.debug("still in update"),e=!0),e}function U(e,t){var n=!1;return C()?(f.debugEnabled()&&f.debug("still in switch, queue error with transformed code "+t),function(e,t){f.debugEnabled()&&f.debug("media error with code "+t+'" queued during ongoing switch or update'),y||c.emit(u.QUEUED,{code:t}),y=e}(e,t),n=!0):w()&&(f.debugEnabled()&&f.debug("still in update, do nothing"),n=!0),n}function P(e){return"recover"===e}function L(){f.debugEnabled()&&f.debug("check queued media error"),D()?f.debugEnabled()&&f.debug("abort queued check, still ongoing switch or update"):y&&!S&&(f.debugEnabled()&&f.debug("set timeout for handling queued media error again"),S=setTimeout((function(){f.debugEnabled()&&f.debug("timeout fired queued media error"),clearTimeout(S),S=0,R(y),M()}),250))}function M(){y=null,c.emit(u.UNQUEUED)}function F(){clearTimeout(h),h=0}function k(e,t){return e.push(t),e}function x(e,t){for(var n=0;n<e.length;)e[n]===t?e.splice(n,1):++n;return e}return f.debugEnabled()&&f.debug("initialize"),r.add({target:c,listeners:v}),{destroy:function(){r.remove({target:c,listeners:v})}}}}}.apply(t,r))||(e.exports=i)},8207:function(e,t,n){var r,i;r=[n(8333),n(6637),n(8730),n(3778),n(492),n(5865),n(855),n(8250),n(92),n(9437),n(5751),n(6273),n(3135),n(1667),n(7426)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p){return{create:function(m){var h,g,b,_,T,y,S,A=e.create("MediaSourceHandler"),v=i.IDLE,R=!1,O=0,I=!1,N=!1,C=[{type:d.STREAM_INFO,listener:F},{type:d.STREAM_INFO_UPDATE,listener:F},{type:d.APPLICATION_STATE_CHANGE,listener:function(e){switch(v=e.data.state){case i.PAUSED:O=0,N=!1;break;case i.PLAYING:}}},{type:d.DOCUMENT_VISIBLE,listener:k},{type:d.DOCUMENT_HIDDEN,listener:k},{type:u.ELEMENT_CREATED,listener:function(){L()}},{type:c.ERROR,listener:function(e){(S=e.data.code)===r.MEDIA.DECODE_ERROR?T=!0:S===r.MEDIA.MEDIA_SOURCE_ENDED&&(y=!0)}},{type:c.RECOVER,listener:function(e){S=e.data.code,A.debugEnabled()&&A.debug("media error recover after error "+S),N=!0,I=!0,R=!0,M()}},{type:f.FIRST_FRAGMENT,listener:function(e){_=e.data}},{type:E.MEDIA_ERROR_RECOVER_STATS,listener:function(e){O=e.data.stats.buffer.end}}],w=[{type:a.SOURCE_OPEN,listener:function(){A.debugEnabled()&&A.debug("open"),N&&m.emit(l.MEDIA_ERROR_RECOVERED,{code:S})}},{type:a.SOURCE_ENDED,listener:function(){var e;A.debugEnabled()&&A.debug("ended"),m.emit(l.ERROR,{code:o.MEDIA_ERR_MEDIA_SOURCE_ENDED}),m.emit(l.SOURCE_ENDED,((e=e||{}).readyState=h.readyState,e))}},{type:a.SOURCE_CLOSED,listener:function(){A.debugEnabled()&&A.debug("closed")}}],D=[];function U(){A.debugEnabled()&&A.debug("create mediasource"),h=new window.MediaSource,t.add({target:h,listeners:w}),D.push(p.create(m,h,O,R?g:null,I?_:null,v)),R=!1,I=!1,T=!1,y=!1}function P(){if(h){for(A.debugEnabled()&&A.debug("destroy mediasource"),t.remove({target:h,listeners:w});D.length;)D.pop().destroy();h=null}}function L(){A.debugEnabled()&&A.debug("bind mediasource"),m.emit(s.VIDEO_SOURCE,{src:window.URL.createObjectURL(h),type:n.MP4})}function M(){A.debugEnabled()&&A.debug("resetMediaSourceOnTheFly");var e=h&&h.activeSourceBuffers.length,t=e&&h.activeSourceBuffers[h.activeSourceBuffers.length-1].buffered.length,n=t?h.activeSourceBuffers[h.activeSourceBuffers.length-1].buffered.end(h.activeSourceBuffers[h.activeSourceBuffers.length-1].buffered.length-1):0;A.debugEnabled()&&A.debug("hasActiveSourceBuffer "+e),A.debugEnabled()&&A.debug("hasActiveSourceBufferData "+t),A.debugEnabled()&&A.debug("lastBufferEndActiveSourceBuffer "+n),A.debugEnabled()&&A.debug("old lastBufferEnd "+O),n&&(A.debugEnabled()&&A.debug("set lastBufferEnd to lastBufferEndActiveSourceBuffer"),O=n),A.debugEnabled()&&A.debug("new lastBufferEnd "+O),A.debugEnabled()&&A.debug("resetMediaSource"),P(),U(),L()}function F(e){A.debugEnabled()&&A.debug("streamInfo"),A.debugEnabled()&&A.debug("lastBufferEnd "+O),g&&g.mimeType!==e.data.streamInfo.mimeType||T||y?(A.debugEnabled()&&A.debug(T?y?"was media source error":"was media decode error":"new mime type received"),g=e.data.streamInfo,R=!0,M()):g=e.data.streamInfo}function k(e){b=e.name===d.DOCUMENT_HIDDEN,A.debug(b?"hidden":"visible")}return A.debugEnabled()&&A.debug("create"),t.add({target:m,listeners:C}),U(),{destroy:function(){A.debugEnabled()&&A.debug("destroy"),P(),t.remove({target:m,listeners:C})}}}}}.apply(t,r))||(e.exports=i)},7380:function(e,t,n){var r,i;r=[n(1933),n(8333),n(6627),n(6637),n(1241),n(8250),n(9437),n(92),n(6273),n(855),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){return{create:function(c){var f,E,p=t.create("PauseHandler"),m=0,h=0,g=!1,b=!1,_=[],T=!1,y=!1,S=[{type:s.PAUSE,listener:v},{type:s.WAITING,listener:v},{type:s.ENDED,listener:v},{type:s.VOLUME_CHANGE,listener:function(e){T&&!e.data.muted&&(y=!0,setTimeout((function(){y=!1}),50)),T=e.data.muted}},{type:o.APPLICATION_STATE_CHANGE,listener:function(e){switch(f=e.data.state){case l.LOADING:case l.PAUSED:for(R(),O();_.length;)_.pop();b=!1}}},{type:o.CONFIG,listener:function(e){E=n.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&n.canUseHLS,T=e.data.config.playback.muted}},{type:o.DOCUMENT_VISIBLE,listener:A},{type:o.DOCUMENT_HIDDEN,listener:A},{type:u.RECOVER,listener:function(){p.debugEnabled()&&p.debug("media error recover"),b=!0}},{type:u.RECOVERED,listener:function(){p.debugEnabled()&&p.debug("media error recovered"),b=!1}}];function A(t){g=t.name===o.DOCUMENT_HIDDEN,p.debug(g?"hidden":"visible"),g&&e.mobile&&(f===l.PLAYING||f===l.LOADING||f===l.BUFFERING)&&c.emit(i.SUSPENDED)}function v(t){var n=t.data.buffered.length?t.data.buffered.end(0)-t.data.currentTime:"n.a.",r=t.name;if(p.debugEnabled()&&p.debug(r+" triggered while isRecovering = "+b.toString()+", pauseTimeout = "+(h?"true":"false")+", waitTimeout = "+(m?"true":"false")),b)p.debugEnabled()&&p.debug("skip check while recovery is ongoing");else{var o=_.length;g&&e.mobile&&f===l.PLAYING?(p.debugEnabled()&&p.debug("hidden, suspend"),c.emit(i.SUSPENDED)):E&&r===s.PAUSE?(p.debugEnabled()&&p.debug("hls pause"),y?(p.debugEnabled()&&p.debug("was unmuted right before pause, make play hook to check if allowed"),c.emit(a.PLAY,{external:!1})):(O(),p.debugEnabled&&p.debug("setPauseTimeout"),h=setTimeout((function(){R(),f===l.PLAYING||f===l.BUFFERING?(p.debugEnabled()&&p.debug("pause timeout fired"),_[0]===s.PAUSE?(p.debugEnabled()&&p.debug("last event still pause, emit error"),c.emit(i.ERROR,{code:d.MEDIA_ERR_HLS_BUFFER_UNDERRUN})):p.debugEnabled()&&p.debug("last event not pause after pause timeout fired ("+_[0]+")")):p.debugEnabled()&&p.debug("pause timeout fired, but not playing or buffering")}),200))):E&&r===s.ENDED&&o>=1&&_[0]===s.PAUSE?f===l.PLAYING||f===l.BUFFERING?(p.debugEnabled()&&p.debug("hls pause ended case, clear all timeouts"),R(),O(),c.emit(i.ERROR,{code:d.MEDIA_ERR_HLS_BUFFER_UNDERRUN})):p.debugEnabled()&&p.debug("pause ended case, but not playing or buffering"):m||E&&(!E||r!==s.WAITING)||(p.debugEnabled()&&p.debug("set wait timeout on "+r),R(),function(e,t){p.debugEnabled&&p.debug("setWaitTimeout"),m=setTimeout(function(e,t){O(),f===l.PLAYING?(p.debugEnabled()&&p.debug("nanoplayer: recover unexpected pause type "+e+", delay = "+t,3),c.emit(a.PLAY,{external:!1})):p.debugEnabled()&&p.debug("wait timeout fired, but not playing")}.bind(null,e,t),200)}(r,n)),_.unshift(r)}}function R(){p.debugEnabled&&p.debug("clearPauseTimeout"),clearTimeout(h),h=0}function O(){p.debugEnabled&&p.debug("clearWaitTimeout"),clearTimeout(m),m=0}return p.debugEnabled()&&p.debug("initialize"),r.add({target:c,listeners:S}),{destroy:function(){r.remove({target:c,listeners:S})}}}}}.apply(t,r))||(e.exports=i)},7611:function(e,t,n){var r,i;r=[n(8333),n(6637),n(492),n(9437),n(92),n(6273),n(549)],void 0===(i=function(e,t,n,r,i,a,o){return{create:function(s){var u,d,l=e.create("QualityHandler"),c={extreme:{ratioThreshold:.95,samplesRange:3,samplesMatchAllowed:1,level:4},high:{ratioThreshold:.2,samplesRange:5,samplesMatchAllowed:1,level:3},medium:{ratioThreshold:.1,samplesRange:10,samplesMatchAllowed:3,level:2},low:{ratioThreshold:.02,samplesRange:10,samplesMatchAllowed:1,level:1}},f={extreme:{ratioThreshold:.9,samplesRange:1,samplesMatchAllowed:0,level:4}},E=[],p=[],m=!1,h=["extreme","high","medium","low"],g=0,b=0,_=0,T=["extreme"],y=0,S=0,A=0,v=0,R=[{type:r.APPLICATION_STATE_CHANGE,listener:function(e){switch(d=e.data.state){case n.PLAYING:m&&C();break;case n.PAUSED:w()}}},{type:r.STREAM_INFO,listener:N},{type:r.STREAM_INFO_UPDATE,listener:function(e){N(e)}},{type:a.RECOVER,listener:function(){l.detailEnabled()&&l.detail("media error recover"),w()}},{type:a.RECOVERED,listener:function(){l.detailEnabled()&&l.detail("media error recovered"),C()}}],O=[{type:i.QUALITY_UPDATE,listener:function e(t){for(var r in t.data.quality)if(Object.prototype.hasOwnProperty.call(u,r)){if("totalVideoFrames"===r&&(u.totalVideoFramesCurrent=t.data.quality.totalVideoFrames-u.totalVideoFrames,v=u.totalVideoFramesCurrent),"droppedVideoFrames"===r){for(u.droppedVideoFramesCurrent=t.data.quality.droppedVideoFrames-u.droppedVideoFrames;E.length>9;)E.shift();E.push({count:u.droppedVideoFramesCurrent,time:t.data.currentTime})}if("corruptedVideoFrames"===r){for(u.corruptedVideoFramesCurrent=t.data.quality.corruptedVideoFrames-u.corruptedVideoFrames;p.length>0;)p.shift();p.push({count:u.corruptedVideoFramesCurrent,time:t.data.currentTime})}u[r]=t.data.quality[r]}if(u.droppedVideoFramesLevel=_,u.corruptedVideoFramesLevel=A,v<0)return I(),e(t);v>0&&function(){var e,t,r=0,i=0,a=0;if(d===n.PLAYING){for(l.detailEnabled()&&l.detail("check with current dropped level "+g),l.detailEnabled()&&l.detail("skip check lower "+(g+1)),r=0;r<h.length-g;r+=1)if(e=D(h[r])){l.detailEnabled()&&l.detail("frame drop detected with type '"+e.type+"'"),l.detail(e.frames.dropped+" frames in last "+e.samples.all.length+" seconds dropped"),s.emit(o.FRAME_DROP,e),i=e.level,l.detailEnabled()&&l.detail("change current dropped level "+g+" to "+i),g=i,b&&l.detailEnabled()&&l.detail("reset dropped level timeout running, clear timeout"),clearTimeout(b),b=setTimeout((function(){l.detailEnabled()&&l.detail("reset current dropped level "+g+" to 0"),b=0,g=0}),1e3*e.samples.all.length),l.detailEnabled()&&l.detail("reset dropped level in "+e.samples.all.length+" sec");break}for(l.detailEnabled()&&l.detail("check with current corrupted level "+y),l.detailEnabled()&&l.detail("skip check lower "+(y+1)),r=0;r<T.length-y;r+=1)if(t=U(T[r])){l.detailEnabled()&&l.detail("frame drop detected with type '"+t.type+"'"),l.detail(t.frames.corrupted+" frames in last "+t.samples.all.length+" seconds corrupted"),s.emit(o.FRAME_CORRUPTED,t),a=t.level,l.detailEnabled()&&l.detail("change current corrupted level "+y+" to "+a),y=a,S&&l.detailEnabled()&&l.detail("reset corrupted level timeout running, clear timeout"),clearTimeout(S),S=setTimeout((function(){l.detailEnabled()&&l.detail("reset current corrupted level "+y+" to 0"),S=0,y=0}),1e3*t.samples.all.length),l.detailEnabled()&&l.detail("reset corrupted level in "+t.samples.all.length+" sec");break}}_=i,A=a}(),s.emit(o.QUALITY_STATS,u)}}];function I(){for(u={droppedVideoFrames:0,droppedVideoFramesCurrent:0,droppedVideoFramesLevel:0,corruptedVideoFrames:0,corruptedVideoFramesCurrent:0,corruptedVideoFramesLevel:0,creationTime:0,totalVideoFrames:0,totalVideoFramesCurrent:0},clearTimeout(b),g=0,b=0,_=0;E.length;)E.pop();for(clearTimeout(S),y=0,S=0,A=0;p.length;)p.pop();v=0}function N(e){m=e.data.streamInfo.haveVideo}function C(){w(),t.add({target:s,listeners:O})}function w(){t.remove({target:s,listeners:O}),I()}function D(e){if(l.detailEnabled()&&l.detail("check dropped type '"+e+"'"),E.length>c[e].samplesRange-1){var t=E.slice(-c[e].samplesRange),n=t.filter((function(t){return t.count>=v*c[e].ratioThreshold}));if(n.length>c[e].samplesMatchAllowed){var r=0;return t.forEach((function(e){r+=e.count})),{type:e,level:c[e].level,framerate:v,samples:{all:t,matched:n,limit:Math.ceil(v*c[e].ratioThreshold)},frames:{dropped:r}}}}return!1}function U(e){if(l.detailEnabled()&&l.detail("check corrupted type '"+e+"'"),p.length>f[e].samplesRange-1){var t=p.slice(-f[e].samplesRange),n=t.filter((function(t){return t.count>=v*f[e].ratioThreshold}));if(n.length>f[e].samplesMatchAllowed){var r=0;return t.forEach((function(e){r+=e.count})),{type:e,level:f[e].level,framerate:v,samples:{all:t,matched:n,limit:Math.ceil(v*f[e].ratioThreshold)},frames:{corrupted:r}}}}return!1}return t.add({target:s,listeners:R}),{destroy:function(){t.remove({target:s,listeners:R}),t.remove({target:s,listeners:O}),I()}}}}}.apply(t,r))||(e.exports=i)},7426:function(e,t,n){var r,i;r=[n(8333),n(6627),n(6637),n(7990),n(5865),n(8250),n(92),n(9437),n(5751),n(3135),n(492)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){return{create:function(c,f,E,p,m,h){var g,b,_,T,y,S=e.create("SourceBufferQueue "+(new Date).getTime()),A=0,v=0,R=0,O=!1,I=[],N="append",C="remove",w="reset",D="seek",U="offset",P=!1,L=0,M=!1,F=-1,k=null,x="",B=0,H=0,G=!1,V=[{type:s.STREAM_INFO,listener:Z},{type:s.STREAM_INFO_UPDATE,listener:Z},{type:s.STREAM_FRAGMENT,listener:j},{type:s.RANDOM_ACCESS_POINT,listener:K}],W=[{type:o.TIME_UPDATE,listener:function(e){b=e.data.currentTime,h!==l.PAUSED&&S.detailEnabled()&&S.detail("current time "+b)}},{type:s.APPLICATION_STATE_CHANGE,listener:function(e){(h=e.data.state)===l.PAUSED&&function(){for(S.debugEnabled()&&S.debug("pause"),M=!1,G=!1,P=!1;I.length;)I.pop();z(0,1/0),A=0,E=0}()}}],Y=[{type:r.UPDATE_END,listener:$}];function j(e){if(T=e.data,!M){if(e.data.id)S.debugEnabled()&&S.debug("initializing from RAP with stored "+e.data.boxType+" "+e.data.id),M=!0,T=e.data.fragment;else{for(var t="",n=4;n<8;++n)t+=String.fromCharCode(e.data[n]);if(-1===["moov","ftyp"].indexOf(t))return void(S.debugEnabled()&&S.debug("dropping fragment: "+t));S.debugEnabled()&&S.debug("initialized with "+t),k={boxType:t,fragment:e.data,id:Math.floor(1e11*(Math.random()+1))},S.debugEnabled()&&S.debug("set & emit first fragment with id "+k.id),c.emit(d.FIRST_FRAGMENT,k),M=!0}M&&B&&(re(!0,'"initialized with prerollDuration"',!0),G=!0)}!function(e){X({command:N,fragment:e})}(T),G&&2==++H&&(G=!1,S.debugEnabled()&&S.debug("prerollFragment equals prerollFragmentsCount"),$())}function K(e){if(S.debugEnabled()&&S.debug("random access point streamtime "+e.data.onRandomAccessPoint.streamTime),!M&&x!==s.STREAM_INFO_UPDATE){k&&(S.debugEnabled()&&S.debug("not initialized & have first fragment at RAP with id "+k.id),j({data:k})),(F=Math.floor(e.data.onRandomAccessPoint.streamTime))>0&&F<=500&&(S.debugEnabled()&&S.debug("start time "+F+" within a range of 500 ms, normalize to 0"),F=0),F=Math.floor(F)/1e3,S.debugEnabled()&&S.debug("set start time to random access point");var t=parseFloat(F.toString());g&&g.buffered.length&&(F=-1),t>0?(S.debugEnabled()&&S.debug("time !== 0, from stream info update?"),F=E,Q(E-t)):0===t&&(S.debugEnabled()&&S.debug("time === 0, from stream info?"),g&&g.buffered.length?Q(g.buffered.end(g.buffered.length-1)):Q(E))}}function z(e,t){X({command:C,from:e,to:t})}function q(e){X({command:D,position:e})}function Q(e){X({command:U,offset:e})}function X(e){e.count=v++,O?function(e){if(S.detailEnabled()&&S.detail("queue "+e.command+" "+e.count),e.force)I.unshift(e);else if(e.command===N&&I.length&&I[I.length-1].command===N){if(R+1<30){var t=I[I.length-1].fragment,n=e.fragment,r=new Uint8Array(t.byteLength+n.byteLength);r.set(new Uint8Array(t),0),r.set(new Uint8Array(n),t.byteLength),I[I.length-1].fragment=r,I[I.length-1].count=I[I.length-1].count.toString()+" & "+e.count,R++}else R=0,S.detailEnabled()&&S.detail("joined fragments threshold (30) reached so begin new one"),I.push(e);S.detailEnabled()&&S.detail("joined fragments count "+R)}else R=0,S.detailEnabled()&&S.detail("joined fragments count "+R),I.push(e)}(e):J(e)}function J(e){if(S.detailEnabled()&&S.detail("execute "+e.command+" "+e.count),S.detailEnabled()){var t="ranges ";if(!g||g&&!g.buffered.length)t+="0";else for(var n=0,r=g.buffered.length;n<r;n+=1)t+=n+" "+g.buffered.start(n)+" - "+g.buffered.end(n),n!==r-1&&(t+=", ");S.detail(t)}switch(e.command){case N:if(re(!0,'"APPEND"'),P){S.detailEnabled()&&S.detail("append "+e.count);try{g.appendBuffer(e.fragment)}catch(e){S.debugEnabled()&&S.debug("sourceBuffer append exception"),S.debugEnabled()&&S.debug(e.message)}ne()||A++}else S.debugEnabled()&&S.debug("append disabled"),S.debugEnabled()&&S.debug("trigger manual update end from append"),$();break;case C:re(!0,'"REMOVE"'),g.buffered.length&&g.buffered.end(g.buffered.length-1)>0?(g.remove(e.from,e.to),S.debugEnabled()&&S.debug("removed buffer from "+e.from+" to "+e.to+" "+e.count)):(S.debugEnabled()&&S.debug("no buffered range or end is zero"),S.debugEnabled()&&S.debug("trigger manual update end from remove"),$());break;case w:re(!0,'"RESET"'),L=1,S.debugEnabled()&&S.debug("append enabled after reset"),P=!0,e.zeroing?(S.debugEnabled()&&S.debug("zeroing mse duration"),f.duration=0):(S.debugEnabled()&&S.debug("no zeroing of duration"),S.debugEnabled()&&S.debug("trigger manual update end from reset"),$());break;case D:re(!0,'"SEEK"'),S.debugEnabled()&&S.debug("seek to position "+e.position+" "+e.count),g.buffered.length&&g.buffered.start(g.buffered.length-1)>e.position&&(e.position=g.buffered.start(g.buffered.length-1),S.debugEnabled()&&S.debug("out of last buffer range, seek to buffer start "+e.position)),c.emit(a.SEEK,{position:e.position}),S.debugEnabled()&&S.debug("seeked"),S.debugEnabled()&&S.debug("trigger manual update end from seek"),$();break;case U:re(!0,'"OFFSET"'),g?(S.debugEnabled()&&S.debug("offset "+e.offset+" "+e.count),g.timestampOffset=e.offset,c.emit(d.TIME_OFFSET,{offset:Math.round(1e3*g.timestampOffset)}),S.debugEnabled()&&S.debug("offset added")):S.debugEnabled()&&S.debug("no source buffer for offset"),S.debugEnabled()&&S.debug("trigger manual update end from offset"),$()}}function $(){if(F>-1&&g&&g.buffered.length){var e=parseFloat(F.toString());F=-1,S.debugEnabled()&&S.debug("have buffer and start time on update end"),e>0?(S.debugEnabled()&&S.debug("update end && time !== 0, from stream info update?"),q(e)):0===e&&(S.debugEnabled()&&S.debug("update end && time === 0, from stream info?"),S.debugEnabled()&&S.debug("seek to lastBufferEnd"),q(E))}re(!1,'"onUpdateEnd"'),L&&(L=0,S.debugEnabled()&&S.debug("source ready"),c.emit(u.SOURCE_READY)),I.length?J(I.shift()):!y&&g&&g.buffered.length&&g.buffered.start(0)<b-30&&ne()&&(y=setTimeout((function(){clearTimeout(y),y=0}),1e4),S.debugEnabled()&&S.debug("remove buffer"),z(g.buffered.start(0),b-20))}function Z(e,n){S.debugEnabled()&&S.debug("stream info"),p=e.data.streamInfo,_=p&&p.mimeType?p.mimeType:'video/mp4; codecs="avc1.42E01E, mp4a.40.2"',x=e.name,B=p.prerollDuration?p.prerollDuration:0,H=0,G=!1,n?S.debugEnabled()&&S.debug("triggered streamInfo"):(S.debugEnabled()&&S.debug("untriggered streamInfo"),k&&(S.debugEnabled()&&S.debug("deleting stored first fragment"),k=null)),f&&"open"===f.readyState?(S.debugEnabled()&&S.debug("playerState "+Object.keys(l)[h-1]),S.debugEnabled()&&S.debug("sourceBuffer "+(g?"existing":"not existing")),-1!==[l.PLAYING,l.BUFFERING].indexOf(h)?(S.debugEnabled()&&S.debug("initialized false after stream info"),M=!1,G=!1):g?(S.debugEnabled()&&S.debug("reset after stream info"),X({command:w,zeroing:t.isTridentBrowser&&!isNaN(f.duration)&&f.duration>0})):(S.debugEnabled()&&S.debug("init sourcebuffer after stream info"),te())):(S.debugEnabled()&&S.debug("add source open listener after stream info"),f.addEventListener(i.SOURCE_OPEN,ee)),p.haveVideo||K({data:{onRandomAccessPoint:{streamTime:0}}})}function ee(){S.debugEnabled()&&S.debug("mediasource open"),f.removeEventListener(i.SOURCE_OPEN,ee),S.debugEnabled()&&S.debug("init sourcebuffer"),te()}function te(){!g&&f&&"open"===f.readyState&&(S.debugEnabled()&&S.debug("create source buffer with mime type '"+_+"'"),g=f.addSourceBuffer(_),n.add({target:c,listeners:W}),n.add({target:g,listeners:Y}),S.debugEnabled()&&S.debug("append enabled init source buffer"),P=!0,O&&$(),c.emit(u.SOURCE_READY))}function ne(){return 300<A}function re(e,t,n){O=e,n?S.debugEnabled()&&S.debug("set updating to "+e+" from "+(t||"unknown")):S.detailEnabled()&&S.detail("set updating to "+e+" from "+(t||"unknown"))}return S.debugEnabled()&&S.debug("init sourceBufferQueue"),S.debugEnabled()&&S.debug("lastBufferEnd "+E),S.debugEnabled()&&S.debug("streamInfo "+JSON.stringify(p)),S.debugEnabled()&&S.debug("firstFragment "+!!m),S.debugEnabled()&&S.debug("playerState "+Object.keys(l)[h-1]),m&&(k=m,S.debugEnabled()&&S.debug("created with first fragment "+k.id)),n.add({target:c,listeners:V}),p&&(re(!0,'"init with streamInfo"',!0),Z({data:{streamInfo:p}},!0)),{destroy:function(){for(;I.length;)I.pop();n.remove({target:c,listeners:V}),n.remove({target:c,listeners:W}),g&&n.remove({target:g,listeners:Y}),f.removeEventListener(i.SOURCE_OPEN,ee),g=null}}}}}.apply(t,r))||(e.exports=i)},4856:function(e,t,n){var r,i;r=[n(4556),n(6637),n(8333),n(6627),n(5337),n(492),n(9437),n(92),n(6273),n(1667)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d){return{create:function(l){var c,f,E,p,m,h,g,b,_,T,y,S,A,v,R,O=n.create("StatsCollector"),I=0,N=!1,C=!1,w=!1,D=[{type:o.APPLICATION_STATE_CHANGE,listener:function(e){switch(f=e.data.state){case a.PAUSED:O.debugEnabled()&&O.debug("remove stats listener"),t.remove({target:l,listeners:P});break;case a.PLAYING:break;case a.LOADING:F(),E&&(O.debugEnabled()&&O.debug("hls: add stats listener"),t.add({target:l,listeners:P}))}}},{type:o.CONFIG,listener:function(e){E=r.mustUseHLS||e.data.config.playback.allowSafariHlsFallback&&r.canUseHLS}}],U=[{type:o.STREAM_FRAGMENT,listener:function(e){v+=1,R+=e.data.byteLength}},{type:o.STREAM_INFO,listener:function(e){E||(O.debugEnabled()&&O.debug("wss: add stats listener"),t.add({target:l,listeners:P})),p=e.data.streamInfo.haveVideo}},{type:o.STREAM_INFO_UPDATE,listener:function(e){p=e.data.streamInfo.haveVideo}}],P=[{type:s.TIME_UPDATE,listener:function(t){b!==t.data.index&&(O.debugEnabled()&&O.debug("new mediaElementIndex, changed from "+b+" to "+t.data.index),b=t.data.index);var n=t.data.buffered.length-1,r=t.data.played.length-1;if(m=t.data.currentTime,h=t.data.playbackRate,t.data.timeOffset&&(g=t.data.timeOffset,m+=g.currentTime),f!==a.PLAYING&&f!==a.BUFFERING||0!==m?m=f===a.LOADING?0:m:O.detailEnabled()&&O.detail("playing but currentTime = 0"),0===m&&O.detailEnabled()&&O.detail("currentTime = 0"),r>=0&&(c.playout.start=0,c.playout.end=m),n>=0&&function(e,t){var n=!1;return E?k(c.buffer.end,e)?(N=k(c.currentTime,t),C=e-t<0,N||C?w||(N&&O.debugEnabled()&&O.debug("hls bufferEnd & currentTime not moving, do update (bufferEnd old/new: "+c.buffer.end+" - "+e+", currentTime old/new: "+c.currentTime+" - "+t+")"),C&&O.debugEnabled()&&O.debug("hls bufferEnd not moving & buffer dried out, do update (bufferEnd old/new: "+c.buffer.end+" - "+e+", currentTime old/new: "+c.currentTime+" - "+t+")"),w=!0,n=!0):w&&(O.debugEnabled()&&O.debug("hls bufferEnd not moving, but currentTime is no longer stuck (bufferEnd old/new: "+c.buffer.end+" - "+e+", currentTime old/new: "+c.currentTime+" - "+t+")"),w=!1)):(n=!0,w&&(O.debugEnabled()&&O.debug("hls bufferEnd is no longer stuck (bufferEnd old/new: "+c.buffer.end+"/"+e+")"),w=!1)):n=!0,n}(t.data.buffered.end(n),m)&&(c.buffer.update.current=t.data.buffered.end(n)-c.buffer.end,t.data.timeOffset&&(c.buffer.update.current+=g.currentTime),c.buffer.delay.before=c.buffer.end-m,c.buffer.start=t.data.buffered.start(n),c.buffer.end=t.data.buffered.end(n),t.data.timeOffset&&(c.buffer.start+=g.currentTime,c.buffer.end+=g.currentTime),c.buffer.delay.current=c.buffer.end-m,_.add(c.buffer.delay.current),M(_,c.buffer.delay),T.add(c.buffer.update.current),M(T,c.buffer.update),c.playbackrate.current=h,y.add(c.playbackrate.current),M(y,c.playbackrate)),e&&!E){var i=Math.floor(e.now()/1e3);I!==i&&(I=i,c.bitrate.current=8*R,S.add(c.bitrate.current),M(S,c.bitrate),p&&(c.framerate.current=v,A.add(c.framerate.current),M(A,c.framerate)),v=0,R=0)}c.currentTime=m,L(d.PLAY_STATS,t.data)}},{type:u.RECOVER,listener:function(){L(d.MEDIA_ERROR_RECOVER_STATS)}}];function L(e,t){l.emit(e,{stats:c,buffered:t?t.buffered:null,played:t?t.played:null,currentTime:t?t.currentTime:0,playbackRate:t?t.playbackRate:0,index:t?t.index:0})}function M(e,t){t.avg=e.arithmetic,t.min=e.minimum,t.max=e.maximum,t.deviation=e.deviation}function F(){c={currentTime:0,playout:{start:0,end:0},buffer:{start:0,end:0,delay:{before:0,current:0,avg:0,min:0,max:0,deviation:0},update:{current:0,avg:0,min:0,max:0,deviation:0}},buffergoal:{real:0,base:0,min:0,max:0},playbackrate:{current:0,avg:0,min:0,max:0,deviation:0},quality:{droppedVideoFrames:0,droppedVideoFramesCurrent:0,droppedVideoFramesLevel:0,corruptedVideoFrames:0,corruptedVideoFramesCurrent:0,creationTime:0,totalVideoFrames:0},bitrate:{current:0,avg:0,min:0,max:0,deviation:0},framerate:{current:0,avg:0,min:0,max:0,deviation:0}},_=i.create(10),T=i.create(10),y=i.create(10),S=i.create(10),A=i.create(10),v=0,R=0,L(d.PLAY_STATS,null)}function k(e,t){return e-t==0}return F(),t.add({target:l,listeners:D}),t.add({target:l,listeners:U}),{destroy:function(){t.remove({target:l,listeners:U}),t.remove({target:l,listeners:D})}}}}}.apply(t,r))||(e.exports=i)},4606:function(e,t,n){var r,i;r=[n(9437)],void 0===(i=function(e){return{create:function(t){function n(e){window.fragments||(window.fragments=[]),window.fragments.push(e.data)}var r;return r="nanoDump",-1!==document.cookie.indexOf(r+"=")&&document.cookie.split(r+"=")[1].split(";")[0]&&t.addListener(e.STREAM_FRAGMENT,n),{destroy:function(){t.removeListener(e.STREAM_FRAGMENT,n)}}}}}.apply(t,r))||(e.exports=i)},3683:function(e,t){var n;void 0===(n=function(){return{validateBuffer:function(e){var t=["min","start","target","limit","max"],n=t.filter((function(t){return!Object.prototype.hasOwnProperty.call(e,t)}));if(n.length)return{success:!1,reason:"The buffer config is invalid, it must contain "+n.join(", ")+". Reset to default."};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&"number"!=typeof e[t[r]])return{success:!1,reason:"The buffer config is invalid, the value '"+t[r]+"' have to be a number. Reset to default."};return{success:!0}},validateDynamic:function(e){var t=["offsetThreshold","offsetStep","cooldownTime"],n=t.filter((function(t){return!Object.prototype.hasOwnProperty.call(e,t)}));return n.length?{success:!1,reason:"The dynamic buffer config is invalid, it must contain "+n.join(", ")+". Reset to default."}:(t.forEach((function(t){if("number"!=typeof e[t]||0===e[t])return{success:!1,reason:"The dynamic buffer config is invalid, the value '"+t+"' have to be a number. Reset to default."}})),{success:!0})}}}.apply(t,[]))||(e.exports=n)},4367:function(e,t){var n;void 0===(n=function(){return{BASE:"message.",META_DATA:"message.metaData",SERVER_INFO:"message.serverInfo",STREAM_INFO:"message.streamInfo",STREAM_INFO_UPDATE:"message.streamInfoUpdate",STREAM_FRAGMENT:"message.streamFragment",RANDOM_ACCESS_POINT:"message.randomAccessPoint",RAW_PACKET:"message.raw",STREAM_STATUS:"message.streamStatus",UPDATE_SOURCE_SUCCESS:"message.updateSourceSuccess",UPDATE_SOURCE_ERROR:"message.updateSourceError",UPDATE_SOURCE_ABORT:"message.updateSourceAbort",STREAM_QUALITY:"message.streamQuality"}}.apply(t,[]))||(e.exports=n)},312:function(e,t){var n;void 0===(n=function(){var e="networkmanager.";return{BASE:e,CONNECT:e+"connect",DISCONNECT:e+"disconnect",PLAY:e+"play",PAUSE:e+"pause",CONFIG:e+"config",UPDATE_SOURCE:e+"updateSource",STATE_CHANGE:e+"stateChange",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",SWITCH_STREAM_SUCCESS:e+"switchStreamSuccess",SWITCH_STREAM_FAIL:e+"switchStreamFail",SWITCH_STREAM_ABORT:e+"switchStreamAbort"}}.apply(t,[]))||(e.exports=n)},1852:function(e,t){var n;void 0===(n=function(){var e="networkstatus";return{BASE:e,ONLINE:e+"online",OFFLINE:e+"offline"}}.apply(t,[]))||(e.exports=n)},9682:function(e,t){var n;void 0===(n=function(){return{BASE:"",OPEN:"open",CLOSE:"close",MESSAGE:"message",ERROR:"error"}}.apply(t,[]))||(e.exports=n)},4746:function(e,t){var n;void 0===(n=function(){var e="webSocketHandler.";return{BASE:e,CONNECTING:e+"connecting",CONNECTED:e+"connected",CONNECTION_OPEN:e+"connectionOpen",DISCONNECTED:e+"disconnected",RESUMING:e+"resuming",MESSAGE:e+"message",RECONNECTION_IMMINENT:e+"reconnectionImminent",RECONNECTING:e+"reconnecting",RECONNECTION_CONFIG_INVALID:e+"reconnectionConfigInvalid",CONNECTION_ERROR:e+"connectionError",INITIALIZATION_ERROR:e+"initializationError",DESTROYED:e+"destroyed",ERROR:e+"error",WARNING:e+"warning",STATE_CHANGE:e+"stateChange",META_DATA:e+"metaData",SERVER_INFO:e+"serverInfo",STREAM_INFO:e+"streamInfo",STREAM_INFO_UPDATE:e+"streamInfoUpdate",STREAM_FRAGMENT:e+"streamFragment",RANDOM_ACCESS_POINT:e+"randomAccessPoint",RAW_PACKET:e+"raw",STREAM_STATUS:e+"streamStatus",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_FAIL:e+"updateSourceFail",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",STREAM_QUALITY:e+"streamQuality"}}.apply(t,[]))||(e.exports=n)},8371:function(e,t){var n;void 0===(n=function(){var e="webSocketProxy.";return{BASE:e,CONNECTING:e+"connecting",CONNECTED:e+"connected",CONNECTION_OPEN:e+"connectionOpen",DISCONNECTED:e+"disconnected",RESUMING:e+"resuming",MESSAGE:e+"message",RECONNECTION_IMMINENT:e+"reconnectionImminent",RECONNECTING:e+"reconnecting",RECONNECTION_CONFIG_INVALID:e+"reconnectionConfigInvalid",CONNECTION_ERROR:e+"connectionError",INITIALIZATION_ERROR:e+"initializationError",DESTROYED:e+"destroyed",ERROR:e+"error",STATE_CHANGE:e+"stateChange",META_DATA:e+"metaData",SERVER_INFO:e+"serverInfo",STREAM_INFO:e+"streamInfo",STREAM_INFO_UPDATE:e+"streamInfoUpdate",STREAM_FRAGMENT:e+"streamFragment",RANDOM_ACCESS_POINT:e+"randomAccessPoint",RAW_PACKET:e+"raw",STREAM_STATUS:e+"streamStatus",UPDATE_SOURCE_SUCCESS:e+"updateSourceSuccess",UPDATE_SOURCE_ERROR:e+"updateSourceError",UPDATE_SOURCE_ABORT:e+"updateSourceAbort",STREAM_QUALITY:e+"streamQuality"}}.apply(t,[]))||(e.exports=n)},279:function(e,t,n){var r,i;r=[n(9658),n(6637),n(9336),n(9673),n(680),n(594),n(1376),n(5055),n(312),n(4746),n(1852),n(402),n(8941),n(5356)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E){"use strict";return{create:function(t){var n,a,c=new e,p=[],m=[{from:d.CONNECTING,to:o.CONNECTING},{from:d.CONNECTED,to:o.CONNECTED},{from:d.CONNECTION_OPEN,to:o.CONNECTION_OPEN},{from:d.RESUMING,to:o.RESUMING},{from:d.DESTROYED,to:o.DESTROYED},{from:d.RECONNECTING,to:o.RECONNECTING},{from:d.RECONNECTION_IMMINENT,to:o.RECONNECTION_IMMINENT},{from:d.RECONNECTION_CONFIG_INVALID,to:o.RECONNECTION_CONFIG_INVALID},{from:d.DISCONNECTED,to:o.DISCONNECTED},{from:d.INITIALIZATION_ERROR,to:o.INITIALIZATION_ERROR},{from:d.CONNECTION_ERROR,to:o.CONNECTION_ERROR},{from:d.ERROR,to:o.ERROR},{from:d.WARNING,to:o.WARNING},{from:d.STATE_CHANGE,to:o.STATE_CHANGE},{from:d.UPDATE_SOURCE_SUCCESS,to:o.UPDATE_SOURCE_SUCCESS},{from:d.UPDATE_SOURCE_FAIL,to:o.UPDATE_SOURCE_FAIL},{from:d.UPDATE_SOURCE_ABORT,to:o.UPDATE_SOURCE_ABORT},{from:d.META_DATA,to:o.META_DATA},{from:d.RANDOM_ACCESS_POINT,to:o.RANDOM_ACCESS_POINT},{from:d.SERVER_INFO,to:o.SERVER_INFO},{from:d.STREAM_INFO,to:o.STREAM_INFO},{from:d.STREAM_INFO_UPDATE,to:o.STREAM_INFO_UPDATE},{from:d.RAW_PACKET,to:o.RAW_PACKET},{from:d.STREAM_FRAGMENT,to:o.STREAM_FRAGMENT},{from:d.STREAM_STATUS,to:o.STREAM_STATUS},{from:d.STREAM_QUALITY,to:o.STREAM_QUALITY},{from:l.ONLINE,to:o.ONLINE},{from:l.OFFLINE,to:o.OFFLINE}],h=[{from:r.CONNECT,to:u.CONNECT},{from:r.DISCONNECT,to:u.DISCONNECT},{from:r.NETWORK_PLAY,to:u.PLAY},{from:r.PAUSE,to:u.PAUSE},{from:r.CONFIG,to:u.CONFIG},{from:r.NETWORK_UPDATE_SOURCE,to:u.UPDATE_SOURCE},{from:i.STATE_CHANGE,to:u.STATE_CHANGE},{from:i.UPDATE_SOURCE_SUCCESS,to:u.UPDATE_SOURCE_SUCCESS},{from:i.UPDATE_SOURCE_FAIL,to:u.UPDATE_SOURCE_FAIL},{from:i.UPDATE_SOURCE_ABORT,to:u.UPDATE_SOURCE_ABORT},{from:i.SWITCH_STREAM_SUCCESS,to:u.SWITCH_STREAM_SUCCESS},{from:i.SWITCH_STREAM_FAIL,to:u.SWITCH_STREAM_FAIL},{from:i.SWITCH_STREAM_ABORT,to:u.SWITCH_STREAM_ABORT}];return n=s.create(t,c,h),a=s.create(c,t,m),p.push(f.create(c)),p.push(E.create(c)),{destroy:function(){for(n.destroy(),n=null,a.destroy(),a=null;p.length;)p.pop().destroy()}}}}}.apply(t,r))||(e.exports=i)},402:function(e,t,n){var r,i;r=[n(6637),n(4367),n(8371)],void 0===(i=function(e,t,n){"use strict";return{create:function(r){var i=[],a=0,o=[{type:n.MESSAGE,listener:function(e){if(e){var n=e.data;if("string"==typeof n){var o=JSON.parse(n);if(o&&o.eventType){var u=o.eventType.substr(2,o.eventType.length-2),d=t.BASE+u[0].toLowerCase()+u.slice(1);-1!==d.indexOf(t.STREAM_INFO)&&s(),-1!==d.indexOf(t.SERVER_INFO)&&void 0===o.onServerInfo.hostname&&(o.onServerInfo.hostname=""),r.emit(d,o)}}else if(n instanceof ArrayBuffer&&n.byteLength>0){var l=new Uint8Array(n);if(r.emit(t.RAW_PACKET),n.byteLength>=8&&109===l[4]&&111===l[5]&&111===l[6]&&(102===l[7]||118===l[7])){for(var c=new Uint8Array(a),f=0,E=0;E<i.length;E++)c.set(i[E],f),f+=i[E].length;s(),r.emit(t.STREAM_FRAGMENT,c)}i.push(l),a+=n.byteLength}}}}];function s(){for(;i.length;)i.pop();a=0}return e.add({target:r,listeners:o}),{destroy:function(){e.remove({target:r,listeners:o})}}}}}.apply(t,r))||(e.exports=i)},5356:function(e,t,n){var r,i;r=[n(6637),n(8333),n(312),n(1852)],void 0===(i=function(e,t,n,r){return{create:function(i){var a,o=t.create("NetworkStatusProxy"),s=["online","offline"],u=!1,d=[{type:n.STATE_CHANGE,listener:function(e){a||(o.debugEnabled()&&o.debug("initial player state"),c()),a=e.data.state}}];function l(){c()}function c(){u=navigator.onLine,o.debug(u?"online":"offline"),i.emit(u?r.ONLINE:r.OFFLINE)}return o.debugEnabled()&&o.debug("initialize"),e.add({target:i,listeners:d}),s.forEach((function(e){window.addEventListener(e,l)})),{destroy:function(){e.remove({target:i,listeners:d}),s.forEach((function(e){window.addEventListener(e,l)}))}}}}}.apply(t,r))||(e.exports=i)},8941:function(e,t,n){var r,i;r=[n(8333),n(6627),n(8197),n(6637),n(8405),n(492),n(5493),n(312),n(4746),n(8371),n(7609)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){var c=e.create("WebSocketHandler");return{create:function(e){var n,i=[{type:s.CONNECT,listener:p},{type:s.DISCONNECT,listener:function(){if(c.debugEnabled()&&c.debug("on disconnect"),1!==E.length||!h().isWaitForReconnect())for(c.debugEnabled()&&c.debug("not waiting for reconnect");E.length;)E.pop().destroy()}},{type:s.PLAY,listener:function(){c.debugEnabled()&&c.debug("on play"),1===E.length&&E[0].play()}},{type:s.PAUSE,listener:function(){c.debugEnabled()&&c.debug("on pause"),1===E.length&&E[0].pause()}},{type:s.UPDATE_SOURCE,listener:function(t){if(c.debugEnabled()&&c.debug("on update source"),0===E.length)t.data.update.options.method="client",p(t);else{for(;E.length>1;){var n=g().getConnectData().update.options;e.emit(d.UPDATE_SOURCE_ABORT,{reason:"superseded",tag:n.tag,count:n.count}),T()}var r=E[0].getConnectData(),i=E[0].getServerInfo(),a=r.config.server.websocket!==t.data.config.server.websocket,o=!i,s=!o&&-1===i.capabilities.indexOf("onUpdateSource"),u="client"===t.data.update.options.method;a||o||s||u?(t.data.update.options.method="client",p(t)):(t.data.update.options.method="server",E[0].updateSource(t))}}},{type:s.STATE_CHANGE,listener:function(e){e.data.state===a.PAUSING&&n!==a.RECONNECTION_IMMINENT&&E.length&&h().reset(),n=e.data.state}},{type:s.CONFIG,listener:function(n){(t.mustUseHLS||n.data.config.playback.allowSafariHlsFallback&&t.canUseHLS)&&(f=f.filter((function(e){return e.type===d.META_DATA}))),r.add({target:e,listeners:f})}},{type:s.UPDATE_SOURCE_SUCCESS,listener:function(){c.debugEnabled()&&c.debug("onExternalUpdateSourceSuccess"),E.length>1&&(_(),g().setActive(!0))}},{type:s.UPDATE_SOURCE_FAIL,listener:function(){c.debugEnabled()&&c.debug("onExternalUpdateSourceFail"),T()}},{type:s.SWITCH_STREAM_SUCCESS,listener:function(){c.debugEnabled()&&c.debug("onExternalSwitchStreamSuccess"),E.length>1&&(_(),g().setActive(!0))}},{type:s.SWITCH_STREAM_FAIL,listener:function(){c.debugEnabled()&&c.debug("onExternalSwitchStreamFail"),T()}}],o=[{type:d.CONNECTING,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.CONNECTED,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.CONNECTION_OPEN,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.DISCONNECTED,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),t.update&&e.emit(u.UPDATE_SOURCE_FAIL,{tag:t.update.update.options.tag,count:t.update.update.options.count,code:t.data.code,message:t.data.message,type:t.update.update.type,id:t.update.update.id}),(b()||t.active)&&m(t),T()}},{type:d.RESUMING,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.MESSAGE,listener:function(e){c.detailEnabled()&&c.detail("fired "+e.name),e.active&&m(e)}},{type:d.RECONNECTION_IMMINENT,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.RECONNECTING,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.RECONNECTION_CONFIG_INVALID,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),t.update&&e.emit(u.UPDATE_SOURCE_FAIL,{tag:t.update.update.options.tag,count:t.update.update.options.count,code:t.data.code,message:t.data.message,type:t.update.update.type,id:t.update.update.id}),t.active&&m(t)}},{type:d.CONNECTION_ERROR,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),(b()||e.active)&&m(e),T()}},{type:d.INITIALIZATION_ERROR,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),t.update&&e.emit(u.UPDATE_SOURCE_FAIL,{tag:t.update.update.options.tag,count:t.update.update.options.count,code:t.data.code,message:t.data.message,type:t.update.update.type,id:t.update.update.id}),(b()||t.active)&&m(t),T()}},{type:d.DESTROYED,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.ERROR,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),t.update&&e.emit(u.UPDATE_SOURCE_FAIL,{tag:t.update.update.options.tag,count:t.update.update.options.count,code:t.data.code,message:t.data.message,type:t.update.update.type,id:t.update.update.id}),(b()||t.active)&&m(t),T()}},{type:d.STATE_CHANGE,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}}],f=[{type:d.SERVER_INFO,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.STREAM_INFO,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.STREAM_INFO_UPDATE,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.STREAM_FRAGMENT,listener:function(e){e.active&&m(e)}},{type:d.RANDOM_ACCESS_POINT,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.RAW_PACKET,listener:function(e){e.active&&m(e)}},{type:d.STREAM_STATUS,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&!e.update&&m(e)}},{type:d.STREAM_QUALITY,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}},{type:d.UPDATE_SOURCE_SUCCESS,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),t.active||(_(),t.active=!0,g().setActive(!0)),c.debugEnabled()&&c.debug("trigger update source success"),e.emit(u.UPDATE_SOURCE_SUCCESS,{tag:t.data.tag,count:t.data.count,type:t.data.type,id:t.data.id})}},{type:d.UPDATE_SOURCE_ERROR,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),c.debugEnabled()&&c.debug("trigger update source fail"),e.emit(u.UPDATE_SOURCE_FAIL,{tag:t.data.tag,count:t.data.count,code:t.data.code,message:t.data.message,type:t.data.type,id:t.data.id}),b()&&(c.debugEnabled()&&c.debug("trigger disconnected"),e.emit(u.DISCONNECTED,{code:t.data.code,message:t.data.message})),T()}},{type:d.UPDATE_SOURCE_ABORT,listener:function(t){c.debugEnabled()&&c.debug("fired "+t.name),c.debugEnabled()&&c.debug("trigger update source abort"),e.emit(u.UPDATE_SOURCE_ABORT,{tag:t.data.tag,count:t.data.count,reason:t.data.reason,type:t.data.type,id:t.data.id})}},{type:d.META_DATA,listener:function(e){c.debugEnabled()&&c.debug("fired "+e.name),e.active&&m(e)}}],E=[];function p(t){c.debugEnabled()&&c.debug("on connect"),1===E.length&&h().isWaitForReconnect()?h().connect(t):1===E.length?(E.push(l.create(e)),g().connect(t)):E.length||(E[0]=l.create(e),E[0].setActive(!0),h().connect(t))}function m(t){t.name=t.name.replace(d.BASE,u.BASE),e.emit(t.name,t)}function h(){c.debugEnabled()&&c.debug("get active connection");for(var e=!1,t=0;t<E.length;t+=1)if(E[t].isActive()){c.debugEnabled()&&c.debug("found active connection"),e=E[t];break}return e}function g(){var e=!1;return E.length&&(e=E[E.length-1]),e}function b(){var e=!!g()&&g().shouldPauseOnError();return c.debugEnabled()&&c.debug("should pause on error: "+e),e}function _(){c.debugEnabled()&&c.debug("drop active connection?"),E.length>1&&(c.debugEnabled()&&c.debug("drop active connection!"),E.shift().destroy())}function T(){c.debugEnabled()&&c.debug("drop inactive connection?"),E.length>1&&(c.debugEnabled()&&c.debug("drop inactive connection!"),E.pop().destroy())}return r.add({target:e,listeners:i}),r.add({target:e,listeners:o}),{destroy:function(){for(var t in c.debugEnabled()&&c.debug("destroy"),E)Object.prototype.hasOwnProperty.call(E,t)&&(E[t].destroy(),delete E[t]);r.remove({target:e,listeners:i}),r.remove({target:e,listeners:o}),r.remove({target:e,listeners:f})}}}}}.apply(t,r))||(e.exports=i)},7609:function(e,t,n){var r,i;r=[n(9658),n(8333),n(6627),n(8197),n(6637),n(8405),n(3778),n(3890),n(728),n(8371),n(4367),n(9682),n(402),n(4046),n(7402),n(4677)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m){var h=5e3;return{create:function(g){var b,_,T,y,S,A,v,R=t.create("WebSocketProxy "+(new Date).getTime()),O=new e,I=[4102,4103,4105,4106,4107,4108,4109,4111,4115,4500,4503],N=[{type:l.META_DATA,listener:function(e){ne(e)}},{type:l.SERVER_INFO,listener:function(e){v=e.data.onServerInfo,ne(e)}},{type:l.STREAM_INFO,listener:function(e){if(G()&&-1===v.events.indexOf("onUpdateSourceSuccess")){var t=A.update.options.tag,n=A.update.options.count;g.emit(d.UPDATE_SOURCE_SUCCESS,te({tag:t,count:n,type:A.update.type,id:A.update.id})),k[n]?(S=r.copy(k[n]),A===k[n]&&(A=void 0),delete k[n]):(S=A,A=void 0)}ne(e)}},{type:l.STREAM_INFO_UPDATE,listener:function(e){ne(e)}},{type:l.STREAM_FRAGMENT,listener:function(e){ne(e,!0)}},{type:l.RANDOM_ACCESS_POINT,listener:function(e){ne(e)}},{type:l.RAW_PACKET,listener:function(e){ne(e,!0)}},{type:l.STREAM_STATUS,listener:function(e){G()&&"client"===A.update.options.method&&"stopped"===e.data.onStreamStatus.status&&g.emit(d.UPDATE_SOURCE_ERROR,te({tag:A.update.options.tag,count:A.update.options.count,code:o.NETWORK.SOURCE_STREAM_STOPPED,message:s.NETWORK.SOURCE_STREAM_STOPPED,type:A.update.type,id:A.update.id})),ne(e)}},{type:l.STREAM_QUALITY,listener:function(e){ne(e)}},{type:l.UPDATE_SOURCE_SUCCESS,listener:function(e){if(G()&&F.requestId>=e.data.onUpdateSourceSuccess.requestId||y&&-1!==e.data[e.data.eventType].tag.indexOf(y.update.id)){y&&-1!==e.data[e.data.eventType].tag.indexOf(y.update.id)&&(A=y,y=null);var t=e.data[e.data.eventType].tag,n=e.data[e.data.eventType].count,i=k[n]?k[n].update.type:A.update.type,a=k[n]?k[n].update.id:A.update.id;0===n&&(t=A.update.options.tag,n=A.update.options.count),g.emit(d.UPDATE_SOURCE_SUCCESS,te({tag:t,count:n,type:i,id:a})),k[n]?(S=r.copy(k[n]),A===k[n]&&(A=void 0),delete k[n]):(S=A,A=void 0)}}},{type:l.UPDATE_SOURCE_ERROR,listener:function(e){if(G()&&F.requestId>=e.data.onUpdateSourceError.requestId){var t=e.data.onUpdateSourceError.code,n="Unknown reason.";Object.prototype.hasOwnProperty.call(u,+t)&&(n=u[+t]),t>1e3&&t<4e3&&(t+=3100);var r=e.data[e.data.eventType].tag,i=e.data[e.data.eventType].count,a=k[i]?k[i].update.type:A.update.type,o=k[i]?k[i].update.id:A.update.id;0===i&&(r=A.update.options.tag,i=A.update.options.count),g.emit(d.UPDATE_SOURCE_ERROR,te({tag:r,count:i,code:t,message:n,type:a,id:o})),A===k[i]&&(A=void 0),delete k[i]}}},{type:l.UPDATE_SOURCE_ABORT,listener:function(e){if(G()&&F.requestId>=e.data.onUpdateSourceAbort.requestId){var t=e.data[e.data.eventType].tag,n=e.data[e.data.eventType].count,r=k[n]?k[n].update.type:A.update.type,i=k[n]?k[n].update.id:A.update.id;0===n&&(t=A.update.options.tag,n=A.update.options.count);var a=e.data[e.data.eventType].reason;g.emit(d.UPDATE_SOURCE_ABORT,te({tag:t,count:n,reason:a,type:r,id:i})),A===k[n]&&(A=void 0),delete k[n]}}}],C=null,w=a.UNINITIALIZED,D=0,U=!1,P=!1,L={minDelay:2,maxDelay:10,delaySteps:10,maxRetries:10,delays:[],retryCount:0,randomFactor:1.5},M=[],F={requestId:0},k={};function x(){var e=w;return(w=null!==C?C.readyState+2:a.UNINITIALIZED)!==e&&g.emit(d.STATE_CHANGE,te({connectionState:w})),w}function B(){return S}function H(){return U}function G(){return A||!1}function V(){return B().config.query.cid}function W(){R.debugEnabled()&&R.debug("connection open"),setTimeout((function(){g.emit(d.CONNECTION_OPEN,te())}),0),setTimeout((function(){P||null===C||(clearTimeout(D),g.emit(d.CONNECTED,te({count:L.retryCount})),L.retryCount=0)}),200)}function Y(e){R.debugEnabled()&&R.debug("connection close"),clearTimeout(D);var t=e;if(n.isTridentBrowser&&1005===e.code&&((t=r.copy(e)).code=parseInt(e.reason.split(" ")[1],10)),4200===t.code)J(!0);else{var i=t.code,a="Unknown reason.";Object.prototype.hasOwnProperty.call(u,+i)&&(a=u[+i]),i>1e3&&i<4e3&&(i+=3100),Q(t,d.DISCONNECTED,te({code:i,message:a}))}}function j(e){O.emit(d.MESSAGE,te(e.data))}function K(e){R.debugEnabled()&&R.debug("connection error"),clearTimeout(_),_=setTimeout((function(){if(!P&&null!==C){clearTimeout(D);var t="Connection error.",n=e||void 0;n&&n.message&&(t=n.message),n&&n.code&&(t+=" (code:"+n.code+")"),n&&n.name&&(t=n.name+": "+t),Q(e,d.CONNECTION_ERROR,te({code:o.NETWORK.CONNECTION_ERROR,message:t}))}}),200)}function z(){R.debugEnabled()&&R.debug("connection timeout"),P||null===C||S&&-1!==S.config.query.flags.indexOf("metastreamonly")||Q(null,d.INITIALIZATION_ERROR,te({code:o.NETWORK.COULD_NOT_ESTABLISH_CONNECTION,message:"Could not open connection. Timeout reached."}))}function q(){if(G())for(g.emit(d.UPDATE_SOURCE_ERROR,te({tag:A.update.options.tag,count:A.update.options.count,code:o.NETWORK.SOURCE_TIMEOUT,message:s.NETWORK.SOURCE_TIMEOUT,type:A.update.type,id:A.update.id})),y=A,A=void 0;Object.keys(k).length;)delete k[Object.keys(k)[0]]}function Q(e,t,n){if(!P)if(clearTimeout(b),b=0,H()&&(e&&e.data&&-1!==I.indexOf(e.data.code)||n&&n.data&&-1!==I.indexOf(n.data.code))&&L.retryCount<L.maxRetries){if(P=!0,!L.retryCount){for(;L.delays.length;)L.delays.pop();L.delays=p.calculate(L.minDelay,L.maxDelay,L.delaySteps,L.maxRetries,L.randomFactor)}var r=L.delays[L.retryCount];L.retryCount++,S.count=L.retryCount;var i=e&&e.data&&e.data.code?e.data.code:e&&e.code?e.code:0;i&&(i=n&&n.data&&n.data.code?n.data.code:n&&n.code?n.code:0),g.emit(d.RECONNECTION_IMMINENT,te({delay:r,count:L.retryCount,code:i}))}else g.emit(t,n&&n.data?n:te(n))}function X(e){R.debugEnabled()&&R.debug("create connection"),$(),(C=new WebSocket(e)).binaryType="arraybuffer",-1===S.config.query.flags.indexOf("metastreamonly")&&(C["on"+c.OPEN]=W,C["on"+c.CLOSE]=Y,C["on"+c.ERROR]=K),C["on"+c.MESSAGE]=j,D=setTimeout(z,h)}function J(e){R.debugEnabled()&&R.debug("destroy connection"+(e?" silent":"")),A=void 0,$(),g.emit(d.DESTROYED,te({silent:!!e}))}function $(){clearTimeout(D),clearTimeout(b),clearTimeout(_),clearTimeout(T),null!==C&&(C["on"+c.OPEN]=null,C["on"+c.CLOSE]=null,C["on"+c.MESSAGE]=null,C["on"+c.ERROR]=null,C.close(),C=null)}function Z(e,t){if(R.debugEnabled()&&R.debug("send "+e),t)try{t=JSON.stringify(t)}catch(e){t="{}"}else t="{}";var n=F.requestId+1;(F={}).eventType=e,F[e]=JSON.parse(t),F.requestId=n;var r=JSON.stringify(F);try{x()===a.OPEN&&C.send(r)}catch(e){}}function ee(e){return{code:e.code,name:e.name,message:e.message}}function te(e){return{data:e||void 0,connectionState:x(),connectionId:S.config.query.cid,active:H(),update:G()}}function ne(e,t){var n=e.name.replace(l.BASE,d.BASE);t?R.detailEnabled()&&R.detail("fired "+n):R.debugEnabled()&&R.debug("fired "+n),g.emit(n,te(e.data))}return R.debugEnabled()&&R.debug("init"),M.push(f.create(O)),i.add({target:O,listeners:N}),{connect:function(e){if(R.debugEnabled()&&R.debug("on connect"),P=!1,clearTimeout(b),(S=e.data).count=L.retryCount,S.reconnect){var t=E.validate(S.reconnect);if(t.success)for(var n in S.reconnect)Object.prototype.hasOwnProperty.call(S.reconnect,n)&&(L[n]=S.reconnect[n]);else g.emit(d.RECONNECTION_CONFIG_INVALID,te({reason:t.reason}))}if(S.timeouts){var r=1e3*S.timeouts.connecting;h=Math.max(Math.min(r,3e4),5e3)}try{A=e.data.update?e.data:void 0,X(S.url),g.emit(e.name?d.CONNECTING:d.RECONNECTING,te(S)),G()&&(clearTimeout(T),T=setTimeout(q,1e3*e.data.update.options.timeout))}catch(e){var i="Connection initialization error.",a=e?ee(e):void 0;a&&a.message&&(i=a.message),a&&a.code&&(i+=" (code:"+a.code+")"),a&&a.name&&(i=a.name+": "+i),g.emit(d.INITIALIZATION_ERROR,te({code:o.NETWORK.COULD_NOT_ESTABLISH_CONNECTION,message:i}))}},disconnect:function(e){J(e.data.silent)},play:function(){R.debugEnabled()&&R.debug("on play");try{S=m.create(S,!1),null===C?X(S.url):(R.debugEnabled()&&R.debug("sending play to server"),Z("onPlay"),g.emit(d.RESUMING,te()))}catch(e){g.emit(d.ERROR,te(ee(e)))}},pause:function(){R.debugEnabled()&&R.debug("on pause");try{null!==C&&(R.debugEnabled()&&R.debug("sending pause to server"),S=m.create(S,!0),Z("onPause"))}catch(e){g.emit(d.ERROR,te(ee(e)))}},updateSource:function(e){if(R.debugEnabled()&&R.debug("on update source"),v&&-1!==v.capabilities.indexOf("onUpdateSource")&&"server"===e.data.update.options.method&&null!==C){clearTimeout(T),T=setTimeout(q,1e3*e.data.update.options.timeout),k[e.data.update.options.count]=e.data,(A=e.data).count=L.retryCount;var t=A.update,n={options:{faststart:!1},tag:t.options.tag,count:t.options.count};if(t.entry.h5live.rtmp&&t.entry.h5live.rtmp.url&&t.entry.h5live.rtmp.streamname&&(n.rtmp={url:t.entry.h5live.rtmp.url,stream:t.entry.h5live.rtmp.streamname}),t.entry.h5live.token&&(n.token=t.entry.h5live.token),t.entry.h5live.params&&(n.params=t.entry.h5live.params),t.entry.h5live.security&&"object"==typeof t.entry.h5live.security)for(var r in n.security={},t.entry.h5live.security)Object.prototype.hasOwnProperty.call(t.entry.h5live.security,r)&&(n.security[r]=t.entry.h5live.security[r]);Z("onUpdateSource",n)}},reset:function(){P=!1,L.retryCount=0},getState:x,getConnectData:B,getServerInfo:function(){return v},getWebSocket:function(){return C},setActive:function(e){U=!!e},isActive:H,isUpdate:G,isWaitForReconnect:function(){return P},getConnectionId:V,isConnectionId:function(e){return V()===e},shouldPauseOnError:function(){return B().update&&B().update.options.pauseOnError||A&&A.update.options.pauseOnError},destroy:function(){for(R.debugEnabled()&&R.debug("destroy");M.length;)M.pop().destroy();$(),i.remove({target:O,listeners:N})}}}}}.apply(t,r))||(e.exports=i)},4046:function(e,t){var n;void 0===(n=function(){return{validate:function(e){for(var t=["minDelay","maxDelay","delaySteps","maxRetries"],n=0;n<t.length;++n){if(!Object.prototype.hasOwnProperty.call(e,t[n]))return{success:!1,reason:"The reconnect config is invalid, it must contain 'minDelay', 'maxDelay', 'delaySteps', and 'maxRetries'. Reset to default."};if("number"!=typeof e[t[n]])return{success:!1,reason:"The reconnect config is invalid, the value '"+t[n]+"' have to be a number. Reset to default."}}return e[t[0]]>e[t[1]]?{success:!1,reason:"The reconnect config is invalid, the value '"+t[0]+"' have to be lower then '"+t[1]+"'. Reset to default."}:e[t[0]]<1?{success:!1,reason:"The reconnect config is invalid, the value '"+t[0]+"' have to be greater or equal then 1 sec. Reset to default."}:{success:!0}}}}.apply(t,[]))||(e.exports=n)},7402:function(e,t){var n;void 0===(n=function(){return{calculate:function(e,t,n,r,i){var a=[],o=Math.sqrt(e);n>r&&(n=r);for(var s=(Math.sqrt(t)-o)/(n-1),u=0;u<r;u+=1){var d=i*(Math.random()-.5),l=o+s*Math.min(n-1,u)+(!u&&o-Math.abs(d)<=.5?Math.abs(d)+i*Math.random():d),c=Math.round(l*l*1e3);a.push(c)}return a}}}.apply(t,[]))||(e.exports=n)},4677:function(e,t){var n;void 0===(n=function(){return{create:function(e,t){var n=e.url.split("&flags=");if(n.length>1&&(n[1]=decodeURIComponent(n[1])),t)n.length>1&&-1===n[1].indexOf("paused")?e.url=n[0]+"&flags="+encodeURIComponent("paused,"+n[1]):1===n.length&&(e.url+="&flags=paused");else if(n.length>1&&-1!==n[1].indexOf("paused")){var r=n[1].split(",");if(1===r.length)e.url=e.url.replace("&flags=paused","");else{e.url=n[0]+"&flags=";for(var i=0;i<r.length;i+=1)"paused"!==r[i]&&(e.url+=encodeURIComponent(r[i]),e.url+=encodeURIComponent(r.length>2&&i<r.length-1?",":""))}}return e}}}.apply(t,[]))||(e.exports=n)},9336:function(e,t){var n;void 0===(n=function(){return{PLAY:"api.play",PAUSE:"api.pause"}}.apply(t,[]))||(e.exports=n)},9673:function(e,t){var n;void 0===(n=function(){return{BASE:"control.",CONNECT:"control.connect",DISCONNECT:"control.disconnect",NETWORK_PLAY:"control.networkPlay",NETWORK_PAUSE:"control.networkPause",PLAY:"control.play",PAUSE:"control.pause",MUTE:"control.mute",UNMUTE:"control.unmute",SET_VOLUME:"control.setVolume",SEEK:"control.seek",SET_RATE:"control.playbackRate",CONFIG:"control.config",CREATE_VIDEO:"control.createVideo",DESTROY_VIDEO:"control.destroyVideo",VIDEO_SOURCE:"control.videoSource",UPDATE_SOURCE:"control.updateSource",NETWORK_UPDATE_SOURCE:"control.networkUpdateSource",SWITCH_STREAM:"control.switchStream",SET_ADAPTION:"control.setAdaption"}}.apply(t,[]))||(e.exports=n)},680:function(e,t){var n;void 0===(n=function(){return{BASE:"logic.",STATE_CHANGE:"logic.stateChange",LOADING_TIMEOUT:"logic.loadingTimeout",BUFFERING_TIMEOUT:"logic.bufferingTimeout",STREAM_INFO:"logic.streamInfo",STREAM_INFO_UPDATE:"logic.streamInfoUpdate",SERVER_INFO:"logic.serverInfo",STREAM_URL:"logic.streamUrl",NO_KEEP_CONNECTION:"logic.noKeepConnection",MIME_TYPE_UNSUPPORTED:"logic.mimeTypeUnsupported",META_DATA_RECEIVED:"logic.metaDataReceived",META_DATA_ERROR:"logic.metaDataError",NO_SERVER_ERROR:"logic.noServerError",MISSING_RTMP_ERROR:"logic.missingRtmpError",STARTUP_STATS:"logic.startupStats",DOCUMENT_VISIBLE:"logic.documentVisible",DOCUMENT_HIDDEN:"logic.documentHidden",ERROR:"logic.error",UPDATE_SOURCE_INIT:"logic.updateSourceInit",UPDATE_SOURCE_SUCCESS:"logic.updateSourceSuccess",UPDATE_SOURCE_FAIL:"logic.updateSourceFail",UPDATE_SOURCE_ABORT:"logic.updateSourceAbort",SWITCH_STREAM_INIT:"logic.switchStreamInit",SWITCH_STREAM_SUCCESS:"logic.switchStreamSuccess",SWITCH_STREAM_FAIL:"logic.switchStreamFail",SWITCH_STREAM_ABORT:"logic.switchStreamAbort",ADAPTIVE_STATS:"logic.adaptiveStats",STREAM_SWITCH:"logic.streamSwitch",BUFFER_GOAL_STATS:"logic.bufferGoalStats"}}.apply(t,[]))||(e.exports=n)},594:function(e,t){var n;void 0===(n=function(){return{BASE:"media.",LOAD_START:"media.loadstart",PROGRESS:"media.progress",SUSPEND:"media.suspend",ABORT:"media.abort",EMPTIED:"media.emptied",STALLED:"media.stalled",PLAY:"media.play",PAUSE:"media.pause",LOADED_META_DATA:"media.loadedmetadata",LOADED_DATA:"media.loadeddata",WAITING:"media.waiting",MEDIA_ERROR:"media.mediaerror",MEDIA_ERROR_RECOVER:"media.mediaerrorrecover",MEDIA_ERROR_RECOVERED:"media.mediaerrorrecovered",PLAYING:"media.playing",CAN_PLAY:"media.canplay",CAN_PLAY_THROUGH:"media.canplaythrough",SEEKING:"media.seeking",SEEKED:"media.seeked",TIME_UPDATE:"media.timeupdate",ENDED:"media.ended",RATE_CHANGE:"media.ratechange",DURATION_CHANGE:"media.durationchange",VOLUME_CHANGE:"media.volumechange",ELEMENT_CREATED:"media.elementCreated",QUALITY_STATS:"media.qualitystats",PLAY_STATS:"media.playStats",MEDIA_ERROR_RECOVER_STATS:"media.mediaErrorRecoverStats",FRAME_DROP:"media.framedrop",PLAYBACK_FINISHED:"media.playbackFinished",PLAYBACK_ERROR:"media.playbackError",PLAYBACK_STARTED:"media.playbackStarted",PLAYBACK_SUSPENDED:"media.playbackSuspended",BUFFERING:"media.buffering",PLAY_START_SUCCESS:"media.playStartSuccess",PLAY_START_ERROR:"media.playStartError",BUFFER_TWEAKS_CREATED:"media.bufferTweaksCreated",BUFFER_TWEAKS_ERROR:"media.bufferTweaksError",VIEWPORT_VISIBLE:"media.viewportvisible",VIEWPORT_HIDDEN:"media.viewporthidden",ELEMENTS_UPDATED:"media.elementsUpdated",TIME_OFFSET:"media.timeOffset",UPDATE_SOURCE_ABORT:"media.updateSourceAbort",UPDATE_SOURCE_FAIL:"media.updateSourceFail",UPDATE_SOURCE_TIMEOUT:"media.updateSourceTimeout",ACTIVE_VIDEO_ELEMENT_CHANGE:"media.activeVideoElementChange",OFFLINE_BUFFER_UNDERRUN:"media.offlinebufferunderrun"}}.apply(t,[]))||(e.exports=n)},1376:function(e,t){var n;void 0===(n=function(){return{BASE:"network.",CONNECTING:"network.connecting",CONNECTED:"network.connected",CONNECTION_OPEN:"network.connectionOpen",DISCONNECTED:"network.disconnected",RESUMING:"network.resuming",RECONNECTION_IMMINENT:"network.reconnectionImminent",RECONNECTING:"network.reconnecting",RECONNECTION_CONFIG_INVALID:"network.reconnectionConfigInvalid",CONNECTION_ERROR:"network.connectionError",INITIALIZATION_ERROR:"network.initializationError",ERROR:"network.error",WARNING:"network.warning",DESTROYED:"network.destroyed",STATE_CHANGE:"network.stateChange",META_DATA:"network.metaData",SERVER_INFO:"network.serverInfo",STREAM_INFO:"network.streamInfo",STREAM_INFO_UPDATE:"network.streamInfoUpdate",STREAM_FRAGMENT:"network.streamFragment",RANDOM_ACCESS_POINT:"network.randomAccessPoint",RAW_PACKET:"network.raw",STREAM_STATUS:"network.streamStatus",UPDATE_SOURCE_SUCCESS:"network.updateSourceSuccess",UPDATE_SOURCE_FAIL:"network.updateSourceFail",UPDATE_SOURCE_ABORT:"network.updateSourceAbort",STREAM_QUALITY:"network.streamQuality",ONLINE:"network.online",OFFLINE:"network.offline"}}.apply(t,[]))||(e.exports=n)},7354:function(e,t){var n;void 0===(n=function(){return{BASE:"public.",READY:"public.ready",ERROR:"public.error",PAUSE:"public.pause",LOADING:"public.loading",START_BUFFERING:"public.startBuffering",STOP_BUFFERING:"public.stopBuffering",PLAY:"public.play",METADATA:"public.metaData",STATS:"public.stats",PLAYBACK_FINISHED:"public.playbackFinished",MEDIA:"public.media",STREAM_INFO:"public.streamInfo",STREAM_INFO_UPDATE:"public.streamInfoUpdate",MUTE:"public.mute",UNMUTE:"public.unmute",VOLUME_CHANGE:"public.volumeChange",STATE_CHANGE:"public.stateChange",WARNING:"public.warning",DESTROY:"public.destroy",UPDATE_SOURCE_INIT:"public.updateSourceInit",UPDATE_SOURCE_SUCCESS:"public.updateSourceSuccess",UPDATE_SOURCE_FAIL:"public.updateSourceFail",UPDATE_SOURCE_ABORT:"public.updateSourceAbort",SWITCH_STREAM_INIT:"public.switchStreamInit",SWITCH_STREAM_SUCCESS:"public.switchStreamSuccess",SWITCH_STREAM_FAIL:"public.switchStreamFail",SWITCH_STREAM_ABORT:"public.switchStreamAbort",SERVER_INFO:"public.serverInfo",ACTIVE_VIDEO_ELEMENT_CHANGE:"public.activeVideoElementChange"}}.apply(t,[]))||(e.exports=n)},1128:function(e,t,n){var r,i;r=[n(1969),n(6637),n(5055),n(2682),n(1933),n(8333),n(1473),n(9658),n(8197),n(6725),n(6627),n(8478),n(6965),n(5810),n(1493),n(828),n(492),n(8405),n(9583),n(3778),n(3890),n(3284),n(5493),n(6704),n(1797),n(651),n(2909),n(279),n(1358),n(9673),n(680),n(594),n(1376),n(9336),n(7354)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b,_,T,y,S,A,v,R,O,I,N,C,w,D,U,P,L){"use strict";var M=a.create("H5Live");function F(e){d.validatePlayerDivId(e),this._setBaseValues(e)}F.getSupportedTechniques=function(){var e=[];return l.isH5LiveKnownUnsupported||(l.isMediaSourceH264Supported&&e.push(y.H5LIVE_WSS),(l.mustUseHLS||l.canUseHLS)&&e.push(y.H5LIVE_HLS)),e},F.supportedTechniques=F.getSupportedTechniques(),F.isSupported=function(){return F.supportedTechniques.length>0};var k=F.prototype=Object.create(e.prototype);return k._setBaseValues=function(e){this.version=r.CORE,this.type="h5live",this.networkState=this.NETWORK_STATE.UNINITIALIZED,this._info=i,this.config=f.create(),this._playerDivId="",this._playing=!1,this._updating=!1,this._serverInfo=null,this._useHLS=!1,this._pauseReason="",this._playerDivId=e,this._mediaElementIds=[],this._componentEmitter=new s,this._componentListeners={logic:[{type:w.SERVER_INFO,listener:this._onServerInfo.bind(this)},{type:w.NO_KEEP_CONNECTION,listener:this._onNoKeepConnection.bind(this)},{type:w.MIME_TYPE_UNSUPPORTED,listener:this._onMimeTypeUnsupported.bind(this)},{type:w.ERROR,listener:this._onLogicError.bind(this)},{type:w.UPDATE_SOURCE_INIT,listener:this._onUpdateSourceInit.bind(this)},{type:w.SWITCH_STREAM_INIT,listener:this._onSwitchStreamInit.bind(this)}],network:[{type:U.CONNECTING,listener:this._onConnectionConnecting.bind(this)},{type:U.CONNECTED,listener:this._onConnectionOpen.bind(this)},{type:U.DISCONNECTED,listener:this._onConnectionClosed.bind(this)},{type:U.RECONNECTING,listener:this._onConnectionReconnecting.bind(this)},{type:U.RECONNECTION_IMMINENT,listener:this._onConnectionReconnectionImminent.bind(this)},{type:U.DESTROYED,listener:this._onConnectionDestroyed.bind(this)},{type:U.INITIALIZATION_ERROR,listener:this._onConnectionInitializationError.bind(this)},{type:U.CONNECTION_ERROR,listener:this._onConnectionError.bind(this)},{type:U.ERROR,listener:this._onConnectionError.bind(this)},{type:U.WARNING,listener:this._onConnectionWarning.bind(this)},{type:U.STATE_CHANGE,listener:this._onConnectionStateChange.bind(this)}],media:[{type:D.BUFFER_TWEAKS_CREATED,listener:this._onBufferTweaksCreated.bind(this)},{type:D.MEDIA_ERROR_RECOVER,listener:this._onMediaErrorRecover.bind(this)},{type:D.PLAYBACK_STARTED,listener:this._onMediaPlaying.bind(this)},{type:D.PLAYBACK_SUSPENDED,listener:this._onMediaSuspended.bind(this)},{type:D.BUFFERING,listener:this._onMediaBuffering.bind(this)},{type:D.PLAY_START_ERROR,listener:this._onPlayStartError.bind(this)},{type:D.UPDATE_SOURCE_TIMEOUT,listener:this._onUpdateSourceTimeout.bind(this)},{type:D.OFFLINE_BUFFER_UNDERRUN,listener:this._onOfflineBufferUnderrun.bind(this)}]},this._setState(this.STATE.IDLE)},k._baseSetup=function(e,r){this._useHLS=l.mustUseHLS||r.playback.allowSafariHlsFallback&&l.canUseHLS;var i=this._checkSource(r.source);r.source=i;var a,s=o.check(r,E.create());if(o.merge(r,this.config),o.clean(this.config),a=d.validateConfig(e,this.config))return a;this._setListeners(this.config.events,this),s.forEach(function(e){this._emitWarning(e)}.bind(this));var u=document.getElementById(this._playerDivId);this._metaDataEnabled=this.config.playback.metadata;var c=this.config.source.entries[this.config.source.startIndex].h5live.params,f=this.config.source.entries[this.config.source.startIndex].h5live.rtmp,p=c&&c.url?c.url:f&&f.url?f.url:null,m=c&&c.stream?c.stream:f&&f.streamname?f.streamname:null;for(var h in this.config.url=p&&m?p+"/"+m:"",this.config.type=this.type,this.config.id=this.id,this._components={network:I.create(this._componentEmitter),media:N.create(this._componentEmitter),logic:O.create(this._componentEmitter),propagator:v.create(this._componentEmitter),error:R.create(this._componentEmitter),state:A.create(this._componentEmitter)},this._components)Object.prototype.hasOwnProperty.call(this._componentListeners,h)&&this._componentListeners[h].length&&t.add({target:this._componentEmitter,listeners:this._componentListeners[h]});for(var g in b)(this._outwardEvents||(this._outwardEvents=[]))&&this._outwardEvents.push({from:b[g],to:b[g]}),(this._inwardEvents||(this._inwardEvents=[]))&&this._inwardEvents.push({from:b[g],to:L[g]});return this._outwardTranslator=n.create(this._componentEmitter,this,this._outwardEvents),this._inwardTranslator=n.create(this,this._componentEmitter,this._inwardEvents),this._emitConfig(),this._setupVideoElement(u),a},k.setup=function(e){return new Promise(function(t,n){try{var r=this._baseSetup(F.supportedTechniques[0],e);r?n(r):(setTimeout(function(){this._setState(this.STATE.READY),this.emit(b.READY,{config:this.config}),!this._pauseReason.length&&this.config.playback.autoplay&&this.play()}.bind(this),20),t(this.config))}catch(e){n(e)}}.bind(this))},k._destroy=function(){for(var e in this._setState(this.STATE.DESTROYING),this._playing&&this.pause(this.state),this.emit(b.DESTROY),this._outwardTranslator&&this._outwardTranslator.destroy(),this._outwardTranslator=null,this._inwardTranslator&&this._inwardTranslator.destroy(),this._inwardTranslator=null,this._components)Object.prototype.hasOwnProperty.call(this._components,e)&&(this._components[e].destroy(),delete this._components[e]);for(var n in this._componentListeners)Object.prototype.hasOwnProperty.call(this._componentListeners,n)&&this._componentListeners[n].length&&t.remove({target:this._componentEmitter,listeners:this._componentListeners[n]});this.removeAllListeners()},k.destroy=k._destroy,k._emitConfig=function(){this._componentEmitter.emit(C.CONFIG,{config:u.copy(this.config)})},k._setState=function(e){M.debugEnabled()&&M.debug("set state");var t=arguments.length>1?arguments[1]:{};e!==this.state?(this.state=e,M.debugEnabled()&&M.debug("state changed to "+this._getState()+" ("+e+")"),t.state=e,this._componentEmitter.emit(w.STATE_CHANGE,t)):M.debugEnabled()&&M.debug("state already at "+this._getState()+" ("+e+")")},k._setListeners=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&"function"==typeof e[n]){var r=n.replace("on",""),i=e[n];t.on(r,i)}},k._setupVideoElement=function(e){var t="h5live-"+this._playerDivId;if(this.config.playback.videoId){if("string"==typeof this.config.playback.videoId)this._mediaElementIds.push(this.config.playback.videoId);else if(Array.isArray(this.config.playback.videoId))for(var n=0;n<this.config.playback.videoId.length&&(this._mediaElementIds.push("string"==typeof this.config.playback.videoId[n]?this.config.playback.videoId[n]:t+(n?"-"+n:"")),l.mustUseHLS||l.canUseHLS&&this.config.playback.allowSafariHlsFallback);n+=1);else this._mediaElementIds.push(t);(l.mustUseHLS||l.canUseHLS&&this.config.playback.allowSafariHlsFallback)&&2!==this._mediaElementIds.length&&(this._mediaElementIds.push(t+"-1"),this._emitWarning("Two external video elements should be used for hls playback with multi stream configuration. Please pass two element ids within a string array."))}else this._mediaElementIds.push(t),(l.mustUseHLS||l.canUseHLS&&this.config.playback.allowSafariHlsFallback)&&this._mediaElementIds.push(t+"-1");this._componentEmitter.emit(C.CREATE_VIDEO,{elementIds:this._mediaElementIds,container:e})},k._onLogicError=function(e){switch(e.data.code){case _.PLAYER.VISIBILITY_HIDDEN:this._setState(h.VISIBILITY_HIDDEN);break;case _.STREAM.NOT_ENOUGH_DATA:this._setState(h.NOT_ENOUGH_DATA);break;case _.STREAM.SOURCE_STOPPED:this._setState(h.SOURCE_STREAM_STOPPED);break;case _.MEDIA.ABORTED:case _.MEDIA.DOWNLOAD_ERROR:case _.MEDIA.DECODE_ERROR:case _.MEDIA.HLS_VIDEO_DECODE_ERROR:case _.MEDIA.NOT_SUPPORTED:case _.MEDIA.MEDIA_SOURCE_ENDED:case _.PLAYER.PLAYBACK_ERROR:case _.MEDIA.HLS_BUFFER_UNDERRUN:this._setState(h.PLAYBACK_ERROR)}this.emit(b.ERROR,e.data),this.pause(this.state)},k._onUpdateSourceInit=function(e){this._maybePlayOnUpdateOrSwitch(e)},k._onSwitchStreamInit=function(e){this._maybePlayOnUpdateOrSwitch(e)},k._maybePlayOnUpdateOrSwitch=function(e){e.data.options.forcePlay&&-1!==[this.STATE.READY,this.STATE.PAUSED].indexOf(this.state)&&setTimeout(this.play.bind(this),200)},k._onPlayStartError=function(e){switch(M.debugEnabled()&&M.debug(e.data.error.name+": "+e.data.error.message),e.data.error.name){case"NotAllowedError":e.data.automuted?this._emitWarning("Unmuted autoplay failed by autoplay policy, but 'automute' is enabled. Try to play muted."):(this._setState(this.STATE.PLAYBACK_NOT_STARTED),this._emitError(_.PLAYER.INTERACTION_REQUIRED,"Playback must be initialized by user gesture."))}},k._onBufferTweaksCreated=function(e){this.config.tweaks=this.config.tweaks||{},this.config.tweaks.buffer=e.data.tweaks.buffer,this.config.playback.latencyControlMode=e.data.latencyControlMode},k._onMediaErrorRecover=function(e){this._emitWarning("Recovering from media error "+e.data.code+", recovery "+e.data.count+"/"+e.data.max+" within the last 60 seconds ("+e.data.total+" total).")},k._onOfflineBufferUnderrun=function(){this._emitWarning("Network status is offline during buffering or loading. Wait for becoming online again to recover.")},k._preparePlay=function(e){this._setState(this.STATE.LOADING,{connectDelay:e}),this.emit(b.LOADING,{connectDelay:e}),setTimeout(function(){this.state===this.STATE.LOADING&&(this._componentEmitter.emit(P.PLAY),this._componentEmitter.emit(C.PLAY,{external:!0}))}.bind(this),e)},k.play=function(){var e=arguments.length?arguments[0]:0;M.debugEnabled()&&M.debug("play in state "+this.state),this.state<this.STATE.READY?this._emitError(_.PLAYER.NOT_CONFIGURED,"Could not play because player has not been configured."):this.state===this.STATE.READY||this.state===this.STATE.PAUSED?(this._playing=!0,this._preparePlay(e)):this.state===this.STATE.PAUSING&&(M.debugEnabled()&&M.debug("play in PAUSING state, setting flag to resume"),this._resumeOnPause=!0)},k._onConnectionInitializationError=function(e){M.debugEnabled()&&M.debug('error init connection with state "'+this._getState()+'", networkstate "'+this._getNetworkState()+'"',2),this._setState(this.STATE.CONNECTION_ERROR),this._emitError(e.data.code,e.data.message)},k._onConnectionError=function(e){M.debugEnabled()&&M.debug('error connection with state "'+this._getState()+'", networkstate "'+this._getNetworkState()+'"',2),this._setState(this.STATE.CONNECTION_ERROR),this._emitError(e.data.code,e.data.message)},k._onConnectionConnecting=function(){M.debugEnabled()&&M.debug("connecting")},k._onConnectionStateChange=function(e){this.networkState=e.data.connectionState},k._onConnectionWarning=function(e){var t="Connection warning!",n=e?e.data:void 0;n&&n.message&&(t+=" "+n.message),n&&n.code&&(t+=" (code:"+n.code+")"),this._emitWarning(t)},k._onConnectionOpen=function(e){e.data.count?M.debugEnabled()&&M.debug("connection open after "+e.data.count+" reconnect tries"):M.debugEnabled()&&M.debug("connection open")},k._onConnectionClosed=function(e){M.debugEnabled()&&M.debug("connection closed"),M.debugEnabled()&&M.debug('closed connection with state "'+this._getState()+'", networkstate "'+this._getNetworkState()+'"',2),this._setState(this.STATE.CONNECTION_ERROR),this._emitError(e.data.code,e.data.message)},k._onConnectionDestroyed=function(e){M.debugEnabled()&&M.debug("connection destroyed"+(e.data.silent?", but silent":"")),this.state===this.STATE.READY||e.data.silent||this.pause(this.state)},k._onConnectionReconnecting=function(e){M.debugEnabled()&&M.debug("reconnect attempt "+e.data.count+" started")},k._onConnectionReconnectionImminent=function(e){var t=e.data.code;M.debugEnabled()&&M.debug("connection "+e.data.count+(e.data.count>1?" times":" time")+" unexpectedly closed with code "+t+", but a reconnect will be prepared",1),M.debugEnabled()&&M.debug("reconnect attempt "+e.data.count+" starts in "+Math.round(e.data.delay)+" ms",1),("iOS"!==i.os||"iOS"===i.os&&e.data.count>1)&&this._emitWarning("Connection error: closed "+e.data.count+(e.data.count>1?" times":" time")+" unexpectedly with code "+t+", but a reconnect will be prepared in "+(e.data.delay/1e3).toFixed(3)+" s"),this._playing&&"iOS"!==i.os&&(this._setState(this.STATE.RECONNECTION_IMMINENT),this.pause(this.state),this.play(e.data.delay))},k._onServerInfo=function(e){M.debugEnabled()&&M.debug("onServerInfo");try{this._serverInfo=e.data.onServerInfo;var t=JSON.stringify(e.data);M.debugEnabled()&&M.debug(t)}catch(e){}},k._onNoKeepConnection=function(e){M.debugEnabled()&&M.debug("onNoKeepConnection"),this._emitWarning(e.data.message)},k._onMimeTypeUnsupported=function(){this._setState(this.STATE.PLAYBACK_ERROR),this._emitError(_.MEDIA.NOT_SUPPORTED,"The received audio/video is not supported")},k._onMediaSuspended=function(){i.mobile||l.mustUseHLS||l.canUseHLS&&this.config.playback.allowSafariHlsFallback?(this._setState(this.STATE.PLAYBACK_SUSPENDED),this._emitError(_.PLAYER.PLAYBACK_SUSPENDED,"Playback suspended by external reason.")):(this._setState(h.PLAYBACK_RESTARTING),this.pause(this.state),setTimeout(this.play.bind(this),0))},k._emit=k.emit,k.emit=function(e,t){var n={};t&&t.name&&t.data?n=t:t?(n.data=t,n.name=e||"unknown"):(n.data={},n.name=e||"unknown"),n.player=this._playerDivId,n.id=this.id,n.version=this.version,n.state=this.state,"Error"===e&&n.data.code&&n.data.message&&M.debugEnabled()&&M.debug("error "+n.data.code+" "+n.data.message);var r=u.copy(n);r.name="public."+r.name.substr(0,1).toLowerCase()+r.name.substr(1),this._componentEmitter.emit(r.name,r),this._emit(n.name,n)},k._emitError=function(e,t){this.emit(b.ERROR,{code:e,message:t}),this.state>=h.READY&&this.pause(this.state)},k._emitWarning=function(e){this.emit(b.WARNING,{message:e})},k._getPauseReason=function(e){var t="";if(e){var n=!this._useHLS&&this.networkState!==this.NETWORK_STATE.OPEN&&this.networkState!==this.NETWORK_STATE.UNINITIALIZED;switch(e){case this.STATE.READY:t=S.SERVER_NOT_FOUND;break;case this.STATE.LOADING:t=n?S.CONNECTION_CLOSE:S.STREAM_NOT_FOUND;break;case this.STATE.BUFFERING:t=n?S.CONNECTION_CLOSE:S.BUFFER;break;case this.STATE.UNKNOWN:t=n?S.CONNECTION_CLOSE:S.UNKNOWN;break;case this.STATE.PLAYING:t=n?S.CONNECTION_CLOSE:S.NORMAL;break;case this.STATE.PLAYBACK_NOT_STARTED:t=S.INTERACTION_REQUIRED;break;case this.STATE.PLAYBACK_SUSPENDED:t=S.PLAYBACK_SUSPENDED;break;case this.STATE.PLAYBACK_ERROR:t=S.PLAYBACK_ERROR;break;case this.STATE.RECONNECTION_IMMINENT:t=S.RECONNECTION_IMMINENT;break;case this.STATE.CONNECTION_ERROR:t=S.CONNECTION_CLOSE;break;case this.STATE.DESTROYING:t=S.DESTROY;break;case this.STATE.PLAYBACK_RESTARTING:t=S.PLAYBACK_RESTART;break;case this.STATE.VISIBILITY_HIDDEN:t=S.VISIBILITY_HIDDEN;break;case this.STATE.NOT_ENOUGH_DATA:t=S.NOT_ENOUGH_DATA;break;case this.STATE.SOURCE_STREAM_STOPPED:t=S.SOURCE_STREAM_STOPPED;break;default:t=S.NORMAL}}else t=S.NORMAL;return t},k._triggerPause=function(){this.state!==this.STATE.PAUSED?(M.debugEnabled()&&M.debug('trigger pause with reason "'+this._pauseReason+'", state "'+this._getState()+'", networkstate "'+this._getNetworkState()+'"'),this._setState(this.STATE.PAUSED,{reason:this._pauseReason}),this.emit(b.PAUSE,{reason:this._pauseReason}),this._resumeOnPause&&(this._resumeOnPause=!1,this.play())):M.debugEnabled()&&M.debug("trigger pause but already in state paused")},k.pause=function(){var e=arguments.length?arguments[0]:0;M.debugEnabled()&&M.debug("pause in state "+this.state),this.state<this.STATE.READY&&!e?this._emitWarning("A pause call has been ignored since the player has not been set up successfully."):this.state!==this.STATE.PAUSED&&this.state!==this.STATE.PAUSING&&(this._pauseReason=this._getPauseReason(e),this._setState(this.STATE.PAUSING),this._playing=!1,this._resumeOnPause=!1,this._componentEmitter.emit(P.PAUSE),this._componentEmitter.emit(C.PAUSE),this._triggerPause())},k.mute=function(){this._componentEmitter.emit(C.MUTE)},k.unmute=function(){this._componentEmitter.emit(C.UNMUTE)},k.setVolume=function(e){this._componentEmitter.emit(C.SET_VOLUME,{volume:e})},k.updateSource=function(e){return new Promise(function(t,n){if(this._updating)n(new c("Another 'updateSource' call is pending."));else{this._updating=!0;var r=function(e,t){this._updating=!1,e.apply(null,t)}.bind(this);try{var i,a=this._checkSource(e);if(this.config.source=a,o.clean(this.config.source),i=d.validateConfig(F.supportedTechniques[0],this.config))i.message.replace("create","update"),this._emitError(i.code,i.message),r(n,[i]);else{var s=this.config.source.entries[this.config.source.startIndex].h5live.params,l=this.config.source.entries[this.config.source.startIndex].h5live.rtmp,f=s&&s.url?s.url:l&&l.url?l.url:null,E=s&&s.stream?s.stream:l&&l.streamname?l.streamname:null;this.config.url=f&&E?f+"/"+E:"",delete this.config.source.h5live,this._componentEmitter.emit(C.UPDATE_SOURCE,{source:u.copy(this.config.source),options:u.copy(this.config.source.options.switch)}),r(t,[this.config])}}catch(e){r(n,[new c(e.message,e.code)])}}}.bind(this))},k.switchStream=function(e){return new Promise(function(t,n){if(isNaN(e)||e<0||e>=this.config.source.entries.length){var r=new c(T.PLAYER.INVALID_ENTRY_INDEX,_.PLAYER.INVALID_ENTRY_INDEX);this.config.source.options.switch.pauseOnError&&this._emitError(r.code,r.message),n(r)}else this._componentEmitter.emit(C.SWITCH_STREAM,{index:e,type:"direct"}),t(u.copy(this.config))}.bind(this))},k.setAdaption=function(e){var t=!0;if(e&&e.rule||(t=!1,this._emitWarning("The adaption object is invalid. The adaption will not be updated.")),-1!==["deviationOfMean","deviationOfMean2"].indexOf(e.rule)?this.config.source.entries.length<2&&(t=!1,this._emitWarning("Only one entry. The adaption will not be updated.")):-1===["none"].indexOf(e.rule)&&(t=!1,this._emitWarning("The adaption rule is not valid. Valid rules are 'deviationOfMean', 'deviationOfMean2' and 'none' (disable). The adaption will not be updated.")),t){var n=1;isNaN(e.downStep)?this._emitWarning("The adaption down step is not valid. The default down step '1' will be set."):(n=parseInt(e.downStep,10)||n,n=Math.max(1,Math.min(this.config.source.entries.length-1,n))),e.downStep=n,this._componentEmitter.emit(C.SET_ADAPTION,{adaption:e})}},k._parseUrl=function(e){var t={url:"",streamname:""},n=e.lastIndexOf("/"),r=e.substr(0,n),i=e.substr(n,e-n);return t.url=r,t.streamname=i,t},k._getState=function(){return u.findPropertyByValue(this.STATE,this.state)},k._getNetworkState=function(){return u.findPropertyByValue(this.NETWORK_STATE,this.networkState)},k._onMediaPlaying=function(e){var t=this.state;this._setState(h.PLAYING),this.emit(t===h.BUFFERING?b.STOP_BUFFERING:b.PLAY,e.data),M.debugEnabled()&&M.debug("state is PLAYING")},k._onMediaBuffering=function(){this._setState(h.BUFFERING),this.emit(b.START_BUFFERING)},k._checkSource=function(e){var t,n,r,i={method:this._useHLS?"client":"server",pauseOnError:!1,forcePlay:!0,fastStart:!1,timeout:20,tag:""},a={rule:"none",downStep:1};if(e&&e.entries&&e.entries.length>0){for(var s=0;s<e.entries.length;s+=1)if(n=e.entries[s],t=o.check(n,m.create()),n.index!==s){t.push("Configuration warning. The config entries property 'index' is not configured properly.");break}}else n=p.create(),o.merge(e,n),e.entries=[n],e.startIndex=0,e.options&&(e.options.adaption=a);if(isNaN(e.startIndex)||"string"!=typeof e.startIndex||(e.startIndex=parseInt(e.startIndex,10)),(void 0===e.startIndex||isNaN(e.startIndex)||e.startIndex<0||e.startIndex>e.entries.length-1)&&(t.push("Configuration warning. The config property 'startIndex' is not configured properly."),e.startIndex=f.create().source.startIndex),e.options){if(e.options.switch){for(r in i)if(Object.prototype.hasOwnProperty.call(e.options.switch,r)&&typeof e.options.switch[r]==typeof i[r])switch(r){case"method":this._useHLS||-1===["server","client"].indexOf(e.options.switch[r])?this._emitWarning("The switch method is not valid. Valid methods are 'server' and 'client'. The default method 'server' will be set."):i[r]=e.options.switch[r];break;case"timeout":e.options.switch[r]>=5&&e.options.switch[r]<=30?i[r]=e.options.switch[r]:this._emitWarning("The switch timeout value is not in the valid range. the valid range is between 5 and 30. The default value 20 will be set.");break;default:i[r]=e.options.switch[r]}e.options.switch=i}else e.options.switch=i;if(e.options.adaption){for(r in a)if(Object.prototype.hasOwnProperty.call(e.options.adaption,r)&&typeof e.options.adaption[r]==typeof a[r])switch(r){case"rule":-1!==["deviationOfMean","none","deviationOfMean2"].indexOf(e.options.adaption[r])?e.entries.length>1&&(a[r]=e.options.adaption[r]):this._emitWarning("The adaption rule is not valid. Valid rules are 'deviationOfMean', 'deviationOfMean2' and 'none' (disable). The default rule 'none' will be set.");break;case"downStep":if(isNaN(e.options.adaption[r]))this._emitWarning("The adaption down step is not valid. The default down step '1' will be set.");else{var u=a[r];u=parseInt(e.options.adaption[r],10)||u,u=Math.max(1,Math.min(e.entries.length-1,u)),a[r]=u}break;default:a[r]=e.options.adaption[r]}e.options.adaption=a}else e.options.adaption=a}else e.options={switch:i,adaption:a};return e.bintu&&1===e.entries.length&&(e.h5live=e.entries[0].h5live),t&&t.length&&t.forEach(function(e){this._emitWarning(e)}.bind(this)),e},k._onUpdateSourceTimeout=function(e){M.debugEnabled()&&M.debug("updateSource timeout"),this._emitError(e.data.code,e.data.message)},k.STATE=h,k.NETWORK_STATE=g,k.PAUSE_REASON=S,F}.apply(t,r))||(e.exports=i)},8985:function(e,t,n){var r,i;r=[n(1969),n(3284),n(2682),n(9088),n(1473),n(1933)],void 0===(i=function(e,t,n,r,i,a){"use strict";function o(e){this.version=n.CORE,this.type="native",this.config={playback:{autoplay:!0,muted:!1},source:{hls:void 0}},this._playerDivId=e}o.isSupported=function(){return"iOS"===a.os||"Safari"===a.browser},o.supportedTechniques=[t.NATIVE];var s=o.prototype=Object.create(e.prototype);return s.setup=function(e){var t=this;return new Promise((function(n,r){try{i.merge(e,t.config),i.clean(t.config),window.location=t.config.source.hls,n(t.config)}catch(e){r(e)}}))},s.play=function(){},s.pause=function(){},s.mute=function(){},s.unmute=function(){},s.setVolume=function(){},o}.apply(t,r))||(e.exports=i)},1933:function(e,t,n){var r,i;r=[n(8333)],void 0===(i=function(e){"use strict";var t=e.create("BrowserInfo"),n="Unknown",r="";screen.width&&(r+=(screen.width?screen.width:"")+" x "+(screen.height?screen.height:""));var i,a,o,s=navigator.appVersion,u=navigator.userAgent,d=navigator.appName,l=""+parseFloat(navigator.appVersion),c=parseInt(navigator.appVersion,10),f=n,E=navigator.plugins,p=!!window.MediaSource,m=p&&window.MediaSource.isTypeSupported&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E, mp4a.40.2"');if(-1!==(a=u.indexOf("Opera"))&&(d="Opera",l=u.substring(a+6),-1!==(a=u.indexOf("Version"))&&(l=u.substring(a+8)),f="Chromium"),-1!==(a=u.indexOf("SamsungBrowser")))d="Samsung Browser",l=u.substring(a+15),f="Chromium";else if(-1!==(a=u.indexOf("Edg/")))d="Microsoft Edge Chromium",l=u.substring(a+4),f="Chromium";else if(-1!==(a=u.indexOf("UCBrowser")))d="UC Browser",l=u.substring(a+10),f="Blink";else if(-1!==(a=u.indexOf("YaBrowser")))d="Yandex Browser",l=u.substring(a+10),f="Blink";else if(-1!==(a=u.indexOf("QIHU 360EE"))||-1!==(a=u.indexOf("360Browser")))d="Qihoo 360 Browser",l="0",f="Blink";else if(-1!==(a=u.indexOf("Iron")))d="Iron Browser",l=u.substring(a+5),f="Chromium";else if(-1!==(a=u.indexOf("Vivaldi")))d="Vivaldi Browser",l=u.substring(a+8),f="Chromium";else if(-1!==(a=u.indexOf("OPR/")))d="Opera",l=u.substring(a+4),f="Chromium";else if(-1!==(a=u.indexOf("MSIE")))d="Microsoft Internet Explorer",l=u.substring(a+5),f="Trident";else if("Netscape"===d&&-1!==(a=u.indexOf("Trident/")))d="Microsoft Internet Explorer",l=u.substring(a+8),-1!==(a=u.indexOf("rv:"))&&(l=u.substring(a+3)),f="Trident";else if("Netscape"===d&&-1!==(a=u.indexOf("Edge/")))d="Microsoft Edge Trident",l=u.substring(a+5),f="Trident";else if(-1!==(a=u.indexOf("Chrome"))){var h=["Chromium PDF Viewer","Native Client","Chromium PDF Plugin"];if(d="Chrome",l=u.substring(a+7),f="Chromium",p&&!m&&parseInt(l,10)>=54)d="Chromium";else for(var g=0;g<E.length;g++){h.includes(E[g].name)&&(d="Chromium");break}}else-1!==(a=u.indexOf("PhantomJS"))?(d="PhantomJS",l=u.substring(a+10),f="Webkit"):-1!==(a=u.indexOf("Safari"))?(d="Safari",l=u.substring(a+7),-1!==(a=u.indexOf("Version"))&&(l=u.substring(a+8)),-1!==(a=u.indexOf("CriOS"))&&(d="Chrome",l=u.substring(a+6)),f="Webkit"):-1!==(a=u.indexOf("Firefox"))?(d="Firefox",l=u.substring(a+8),f="Firefox"):(i=u.lastIndexOf(" ")+1)<(a=u.lastIndexOf("/"))&&(d=u.substring(i,a),l=u.substring(a+1),d.toLowerCase()===d.toUpperCase()&&(d=navigator.appName));-1!==(o=l.indexOf(";"))&&(l=l.substring(0,o)),-1!==(o=l.indexOf(" "))&&(l=l.substring(0,o)),-1!==(o=l.indexOf(")"))&&(l=l.substring(0,o)),c=parseInt(""+l,10),isNaN(c)&&(l=""+parseFloat(navigator.appVersion),c=parseInt(navigator.appVersion,10));var b=/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(s),_=!!navigator.cookieEnabled;void 0!==navigator.cookieEnabled||_||(document.cookie="testcookie",_=-1!==document.cookie.indexOf("testcookie"));var T=n,y=[{s:"Windows 3.11",r:/Win16/},{s:"Windows 95",r:/(Windows 95|Win95|Windows_95)/},{s:"Windows ME",r:/(Win 9x 4.90|Windows ME)/},{s:"Windows 98",r:/(Windows 98|Win98)/},{s:"Windows CE",r:/Windows CE/},{s:"Windows 2000",r:/(Windows NT 5.0|Windows 2000)/},{s:"Windows XP",r:/(Windows NT 5.1|Windows XP)/},{s:"Windows Server 2003",r:/Windows NT 5.2/},{s:"Windows Vista",r:/Windows NT 6.0/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 10",r:/(Windows 10|Windows NT 10.0)/},{s:"Windows NT 4.0",r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},{s:"Windows ME",r:/Windows ME/},{s:"Android",r:/Android/},{s:"Open BSD",r:/OpenBSD/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"QNX",r:/QNX/},{s:"UNIX",r:/UNIX/},{s:"BeOS",r:/BeOS/},{s:"OS/2",r:/OS\/2/},{s:"Search Bot",r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}];for(var S in y){var A=y[S];if(A.r.test(u)){T=A.s;break}}var v=n,R=void 0;switch(T){case"Mac OS X":null!==(R=/Mac OS X (\d+)_(\d+)_?(\d+)?/.exec(u)||/Mac OS X ([._\d]+)/.exec(u))&&(v=R[1]+"."+(0|R[2])+"."+(0|R[3]));break;case"Android":null!==(R=/Android ([._\d]+)/.exec(u))&&(v=R[1]);break;case"iOS":null!==(R=/OS (\d+)_(\d+)_?(\d+)?/.exec(s))&&(v=R[1]+"."+R[2]+"."+(0|R[3]));break;default:/Windows/.test(T)&&(null!==(R=/Windows (.*)/.exec(T))&&(v=R[1]),T="Windows")}var O={screen:r,browser:d,browserVersion:l,mobile:b,os:T,osVersion:v,cookies:_,browserEngine:f,hasMediaSource:p,isH264Supported:m};for(var I in O)Object.prototype.hasOwnProperty.call(O,I)&&t.debug(I+": "+O[I]);return O}.apply(t,r))||(e.exports=i)},6627:function(e,t,n){var r,i;r=[n(1933),n(8333)],void 0===(i=function(e,t){var n=t.create("Conditions"),r=e.os,i=e.osVersion,a=e.browser,o=parseInt(e.browserVersion.split(".")[0],10),s=e.browserEngine,u="Microsoft Edge Trident"===a,d="Microsoft Internet Explorer"===a||u,l="Webkit"===s,c="Chromium"===s&&o>57,f="iOS"===r,E=f&&o>=11,p=f&&15===o,m=f&&16===o,h=f&&17===o,g="Mac OS X"===r,b="Chrome"===a,_="Firefox"===a,T="Safari"===a,y=T&&12.1===parseFloat(e.browserVersion)&&"iOS"!==r,S=f&&parseInt(i,10)>=10,A=y,v=!!e.hasMediaSource,R=!f&&v&&(_&&o>=48||u||e.isH264Supported),O=!v&&g&&(T&&o>=13||b&&o>=85),I=function(){var e=!1;try{(("Chrome"===a||"Chromium"===a)&&o<54||"Firefox"===a&&o<48||"Microsoft Internet Explorer"===a&&(o<11||11===o&&7===parseInt(i,10))||"Safari"===a&&o<10)&&(e=!0)}catch(e){}return e}();!S&&!v&&g&&(T&&o>=10||b)&&(S=!0);var N={mustUseHLS:S,canUseHLS:A,isTridentBrowser:d,isWebkitBrowser:l,useFakeAudio:c,isMediaSourceH264Supported:R,isIOS:f,isIOS11:E,isExactIOS15:p,isExactIOS16:m,isExactIOS17:h,isIOSDesktopMode:O,isMacOS:g,isFirefox:_,isSafari:T,isSafari121MacOSX:y,isH5LiveKnownUnsupported:I};for(var C in N)Object.prototype.hasOwnProperty.call(N,C)&&n.debug(C+": "+N[C]);return N}.apply(t,r))||(e.exports=i)},1473:function(e,t){var n;void 0===(n=function(){function e(t,n){var r;for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&(!n[r]&&(n[r]={}),"object"!=typeof t[r]||Array.isArray(t[r])?n[r]=t[r]:e(t[r],n[r]))}return{merge:function t(n,r){var i;for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&Object.prototype.hasOwnProperty.call(r,i)&&("object"!=typeof n[i]||null===n[i]||!Object.keys(n[i]).length||"object"==typeof r[i]&&Object.keys(r[i]).length||Array.isArray(n[i])?"object"!=typeof n[i]||Array.isArray(n[i])?r[i]=n[i]:t(n[i],r[i]):e(n[i],r[i]))},clean:function e(t){var n;for(n in t)Object.prototype.hasOwnProperty.call(t,n)&&(void 0===t[n]||null===t[n]||"string"==typeof t[n]&&0===t[n].length)&&delete t[n],Object.prototype.hasOwnProperty.call(t,n)&&t[n]instanceof Object&&e(t[n]);for(n in t)(Object.prototype.hasOwnProperty.call(t,n)&&(void 0===t[n]||null===t[n]||"string"==typeof t[n]&&0===t[n].length)||"object"==typeof t[n]&&"function"!=typeof t[n]&&0===Object.keys(t[n]).length)&&delete t[n]},check:function e(t,n,r,i){var a;for(a in r=r||"",i=i||[],t)Object.prototype.hasOwnProperty.call(t,a)&&Object.prototype.hasOwnProperty.call(n,a)&&("object"==typeof t[a]&&"object"==typeof n[a]?i=e(t[a],n[a],r+a+".",i):typeof t[a]!==n[a]&&"*"!==n[a]&&void 0!==t[a]&&""!==t[a]&&i.push("Configuration warning. The config property '$property$' must be of type '$type$'.".replace("$property$",r+a).replace("$type$",n[a]))),Object.prototype.hasOwnProperty.call(t,a)&&!Object.prototype.hasOwnProperty.call(n,a)&&i.push("Configuration warning. The config property '$property$' is not a valid property. Please remove from config.".replace("$property$",r+a));return i},extend:e}}.apply(t,[]))||(e.exports=n)},6725:function(e,t,n){var r,i;r=[n(8478),n(1313),n(3284),n(3778)],void 0===(i=function(e,t,n,r){return{validateConfig:function(i,a){for(var o,s=!1,u=a.source.entries,d=0;d<u.length;d+=1){if(!(o=u[d])||!o.h5live){s=new e(t.CONFIG_SOURCE,r.SETUP.SOURCE_NOT_SUPPORTED);break}if(!(o.h5live.rtmp&&o.h5live.rtmp.url&&o.h5live.rtmp.streamname||o.h5live.params)){s=new e(t.CONFIG_RTMP,r.SETUP.CONFIG_RTMP);break}if(i===n.H5LIVE_WSS&&(!o.h5live.server||o.h5live.server&&!o.h5live.server.websocket)){s=new e(t.CONFIG_WSS,r.SETUP.CONFIG_WSS);break}if(i===n.H5LIVE_HLS){if(!o.h5live.server||o.h5live.server&&!o.h5live.server.hls){s=new e(t.CONFIG_HLS,r.SETUP.CONFIG_HLS);break}if(a.playback&&a.playback.metadata&&!o.h5live.server.websocket){s=new e(t.CONFIG_METADATA,r.SETUP.CONFIG_METADATA);break}}if(!(o.h5live.server&&(o.h5live.server.websocket||o.h5live.server.hls)||o.h5live.token)){s=new e(t.CONFIG_TOKEN,r.SETUP.CONFIG_TOKEN);break}}return s},validatePlayerDivId:function(t){if(!("string"==typeof t||t instanceof String))throw new e('The param "playerDivId" must be of type "String".');var n=document.getElementById(t);if(!n)throw new e('The param "playerDivId" must be the id of an existing "div" element.');if("DIV"!==n.tagName)throw new e('The element with the id "'+t+'" is not a "div" element.');return!0}}}.apply(t,r))||(e.exports=i)},9088:function(e,t){var n;void 0===(n=function(){var e=a(),t=o(),n=s(),r=0,i=void 0;function a(){var e=isNaN(d("debug"))?0:+d("debug"),t=+u("nanoDebug");return Math.max(e,t)}function o(){var e=d("debugFilter")?d("debugFilter"):"*";return(u("nanoDebugFilter")||e).split("|")}function s(){var e=isNaN(d("debugClear"))?0:+d("debugClear"),t=+u("nanoDebugClear");return Math.max(e,t)}function u(e){return-1!==document.cookie.indexOf(e+"=")?document.cookie.split(e+"=")[1].split(";")[0]:null}function d(e){if(!i){i=[];var t=document.location.search.substr(1,document.location.search.length);if(""===t&&-1!==document.location.href.indexOf("?")){var n=document.location.href.indexOf("?")+1;t=document.location.href.slice(n)}if(""!==t)for(var r=t.split("&"),a=0;a<r.length;++a){var o="",s=r[a].split("="),u=s[0];s.length>1&&(o=s[1]),i[decodeURIComponent(u)]=decodeURIComponent(o)}}try{return i[e]}catch(e){return}}return setInterval((function(){e=a(),t=o(),n=s()}),5e3),{log:function(i,a){try{e>=a&&("*"===t[0]||t.filter((function(e){return i.includes(e)})).length)&&(console.debug((new Date).toISOString()+": "+i),r++),n&&r>n&&(r=0,console.clear())}catch(e){}},getHTTPParam:d,enabled:function(t){return e>=t}}}.apply(t,[]))||(e.exports=n)},8478:function(e,t){var n;void 0===(n=function(){return function(e,t){var n=new window.Error(e||"");return n.code=t||0,n}}.apply(t,[]))||(e.exports=n)},9658:function(e,t){var n;void 0===(n=function(){"use strict";function e(){}var t=e.prototype;function n(e,t){for(var n=e.length;n--;)if(e[n].listener===t)return n;return-1}function r(e){return function(){return this[e].apply(this,arguments)}}return t.getListeners=function(e){var t,n,r=this._getEvents();if(e instanceof RegExp)for(n in t={},r)Object.prototype.hasOwnProperty.call(r,n)&&e.test(n)&&(t[n]=r[n]);else t=r[e]||(r[e]=[]);return t},t.flattenListeners=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n.push(e[t].listener);return n},t.getListenersAsObject=function(e){var t,n=this.getListeners(e);return n instanceof Array&&((t={})[e]=n),t||n},t.addListener=function(e,t){var r,i=this.getListenersAsObject(e),a="object"==typeof t;for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&-1===n(i[r],t)&&i[r].push(a?t:{listener:t,once:!1});return this},t.on=r("addListener"),t.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},t.once=r("addOnceListener"),t.defineEvent=function(e){return this.getListeners(e),this},t.defineEvents=function(e){for(var t=0;t<e.length;t+=1)this.defineEvent(e[t]);return this},t.removeListener=function(e,t){var r,i,a=this.getListenersAsObject(e);for(i in a)Object.prototype.hasOwnProperty.call(a,i)&&-1!==(r=n(a[i],t))&&a[i].splice(r,1);return this},t.off=r("removeListener"),t.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},t.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},t.manipulateListeners=function(e,t,n){var r,i,a=e?this.removeListener:this.addListener,o=e?this.removeListeners:this.addListeners;if("object"!=typeof t||t instanceof RegExp)for(r=n.length;r--;)a.call(this,t,n[r]);else for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&(i=t[r])&&("function"==typeof i?a.call(this,r,i):o.call(this,r,i));return this},t.removeEvent=function(e){var t,n=typeof e,r=this._getEvents();if("string"===n)delete r[e];else if(e instanceof RegExp)for(t in r)Object.prototype.hasOwnProperty.call(r,t)&&e.test(t)&&delete r[t];else delete this._events;return this},t.removeAllListeners=r("removeEvent"),t.emitEvent=function(e,t){var n,r,i,a,o=this.getListenersAsObject(e);for(a in o)if(Object.prototype.hasOwnProperty.call(o,a))for(i=(n=o[a].slice(0)).length;i--;)!0===(r=n[i]).once&&this.removeListener(e,r.listener),r.listener.apply(this,t||[])===this._getOnceReturnValue()&&this.removeListener(e,r.listener);return this},t.trigger=r("emitEvent"),t.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return t.length?t.length&&"object"!=typeof t[0]||t.length&&!Object.prototype.hasOwnProperty.call(t[0],"data")?t[0]={name:e,data:t[0]}:t[0].name=e:t.push({name:e,data:{}}),this.emitEvent(e,t)},t.emitSimple=function(e,t,n){return this.emitEvent(e,[{name:e||"AnonymousEvent",target:n||this,data:t||{}}])},t.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},t._getOnceReturnValue=function(){return!Object.prototype.hasOwnProperty.call(this,"_onceReturnValue")||this._onceReturnValue},t._getEvents=function(){return this._events||(this._events={})},e}.apply(t,[]))||(e.exports=n)},8053:function(e,t){var n;void 0===(n=function(){var e=function(){var t,n,r,i,a,o,s="undefined",u="object",d="Shockwave Flash",l="application/x-shockwave-flash",c="SWFObjectExprInst",f="onreadystatechange",E=window,p=document,m=navigator,h=!1,g=[],b=[],_=[],T=[],y=!1,S=!1,A=!0,v=!1,R=function(){var e=typeof p.getElementById!==s&&typeof p.getElementsByTagName!==s&&typeof p.createElement!==s,t=m.userAgent.toLowerCase(),n=m.platform.toLowerCase(),r=/win/.test(n||t),i=/mac/.test(n||t),a=!!/webkit/.test(t)&&parseFloat(t.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")),o="Microsoft Internet Explorer"===m.appName,c=[0,0,0],f=null;if(typeof m.plugins!==s&&typeof m.plugins[d]===u)(f=m.plugins[d].description)&&typeof m.mimeTypes!==s&&m.mimeTypes[l]&&m.mimeTypes[l].enabledPlugin&&(h=!0,o=!1,f=f.replace(/^.*\s+(\S+\s+\S+$)/,"$1"),c[0]=H(f.replace(/^(.*)\..*$/,"$1")),c[1]=H(f.replace(/^.*\.(.*)\s.*$/,"$1")),c[2]=/[a-zA-Z]/.test(f)?H(f.replace(/^.*[a-zA-Z]+(.*)$/,"$1")):0);else if(typeof E.ActiveXObject!==s)try{var g=new ActiveXObject("ShockwaveFlash.ShockwaveFlash");g&&(f=g.GetVariable("$version"))&&(o=!0,c=[H((f=f.split(" ")[1].split(","))[0]),H(f[1]),H(f[2])])}catch(e){}return{w3:e,pv:c,wk:a,ie:o,win:r,mac:i}}();function O(){if(!y&&document.getElementsByTagName("body")[0]){try{var e,t=B("span");t.style.display="none",(e=p.getElementsByTagName("body")[0].appendChild(t)).parentNode.removeChild(e),e=null,t=null}catch(e){return}y=!0;for(var n=g.length,r=0;r<n;r++)g[r]()}}function I(e){y?e():g[g.length]=e}function N(e){var t=null,n=x(e);return n&&"OBJECT"===n.nodeName.toUpperCase()&&(t=typeof n.SetVariable!==s?n:n.getElementsByTagName(u)[0]||n),t}function C(){return!S&&G("6.0.65")&&(R.win||R.mac)&&!(R.wk&&R.wk<312)}function w(e,a,o,u){var d=x(o);if(o=k(o),S=!0,r=u||null,i={success:!1,id:o},d){"OBJECT"===d.nodeName.toUpperCase()?(t=U(d),n=null):(t=d,n=o),e.id=c,(typeof e.width===s||!/%$/.test(e.width)&&H(e.width)<310)&&(e.width="310"),(typeof e.height===s||!/%$/.test(e.height)&&H(e.height)<137)&&(e.height="137");var l=R.ie?"ActiveX":"PlugIn",f="MMredirectURL="+encodeURIComponent(E.location.toString().replace(/&/g,"%26"))+"&MMplayerType="+l+"&MMdoctitle="+encodeURIComponent(p.title.slice(0,47)+" - Flash Player Installation");if(typeof a.flashvars!==s?a.flashvars+="&"+f:a.flashvars=f,R.ie&&4!==d.readyState){var m=B("div");o+="SWFObjectNew",m.setAttribute("id",o),d.parentNode.insertBefore(m,d),d.style.display="none",M(d)}P(e,a,o)}}function D(e){if(R.ie&&4!==e.readyState){e.style.display="none";var t=B("div");e.parentNode.insertBefore(t,e),t.parentNode.replaceChild(U(e),t),M(e)}else e.parentNode.replaceChild(U(e),e)}function U(e){var t=B("div");if(R.win&&R.ie)t.innerHTML=e.innerHTML;else{var n=e.getElementsByTagName(u)[0];if(n){var r=n.childNodes;if(r)for(var i=r.length,a=0;a<i;a++)1===r[a].nodeType&&"PARAM"===r[a].nodeName||8===r[a].nodeType||t.appendChild(r[a].cloneNode(!0))}}return t}function P(e,t,n){var r,i,a,o,d=x(n);if(n=k(n),R.wk&&R.wk<312)return r;if(d){var c,f,E,p=R.ie?B("div"):B(u);for(E in typeof e.id===s&&(e.id=n),t)Object.prototype.hasOwnProperty.call(t,E)&&"movie"!==E.toLowerCase()&&L(p,E,t[E]);for(c in R.ie&&(i=e.data,a=p.innerHTML,(o=B("div")).innerHTML="<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000'><param name='movie' value='"+i+"'>"+a+"</object>",p=o.firstChild),e)Object.prototype.hasOwnProperty.call(e,c)&&("styleclass"===(f=c.toLowerCase())?p.setAttribute("class",e[c]):"classid"!==f&&"data"!==f&&p.setAttribute(c,e[c]));R.ie?_[_.length]=e.id:(p.setAttribute("type",l),p.setAttribute("data",e.data)),d.parentNode.replaceChild(p,d),r=p}return r}function L(e,t,n){var r=B("param");r.setAttribute("name",t),r.setAttribute("value",n),e.appendChild(r)}function M(e){var t=x(e);t&&"OBJECT"===t.nodeName.toUpperCase()&&(R.ie?(t.style.display="none",function e(){if(4===t.readyState){for(var n in t)"function"==typeof t[n]&&(t[n]=null);t.parentNode.removeChild(t)}else setTimeout(e,10)}()):t.parentNode.removeChild(t))}function F(e){return e&&e.nodeType&&1===e.nodeType}function k(e){return F(e)?e.id:e}function x(e){if(F(e))return e;var t=null;try{t=p.getElementById(e)}catch(e){}return t}function B(e){return p.createElement(e)}function H(e){return parseInt(e,10)}function G(e){e+="";var t=R.pv,n=e.split(".");return n[0]=H(n[0]),n[1]=H(n[1])||0,n[2]=H(n[2])||0,!!(t[0]>n[0]||t[0]===n[0]&&t[1]>n[1]||t[0]===n[0]&&t[1]===n[1]&&t[2]>=n[2])}function V(e,t,n,r){var i=p.getElementsByTagName("head")[0];if(i){var u="string"==typeof n?n:"screen";if(r&&(a=null,o=null),!a||o!==u){var d=B("style");d.setAttribute("type","text/css"),d.setAttribute("media",u),a=i.appendChild(d),R.ie&&typeof p.styleSheets!==s&&p.styleSheets.length>0&&(a=p.styleSheets[p.styleSheets.length-1]),o=u}a&&(typeof a.addRule!==s?a.addRule(e,t):typeof p.createTextNode!==s&&a.appendChild(p.createTextNode(e+" {"+t+"}")))}}function W(e,t){if(A){var n=t?"visible":"hidden",r=x(e);y&&r?r.style.visibility=n:"string"==typeof e&&V("#"+e,"visibility:"+n)}}function Y(e){return null!==/[\\"<>.;]/.exec(e)&&typeof encodeURIComponent!==s?encodeURIComponent(e):e}return R.w3&&((typeof p.readyState!==s&&("complete"===p.readyState||"interactive"===p.readyState)||typeof p.readyState===s&&(p.getElementsByTagName("body")[0]||p.body))&&O(),y||(typeof p.addEventListener!==s&&p.addEventListener("DOMContentLoaded",O,!1),R.ie&&(p.attachEvent(f,(function e(){"complete"===p.readyState&&(p.detachEvent(f,e),O())})),E==top&&function e(){if(!y){try{p.documentElement.doScroll("left")}catch(t){return void setTimeout(e,0)}O()}}()),R.wk&&function e(){y||(/loaded|complete/.test(p.readyState)?O():setTimeout(e,0))}())),g[0]=function(){h&&function(){var e=b.length;if(e>0)for(var t=0;t<e;t++){var n=b[t].id,r=b[t].callbackFn,i={success:!1,id:n};if(R.pv[0]>0){var a=x(n);if(a)if(!G(b[t].swfVersion)||R.wk&&R.wk<312)if(b[t].expressInstall&&C()){var o={};o.data=b[t].expressInstall,o.width=a.getAttribute("width")||"0",o.height=a.getAttribute("height")||"0",a.getAttribute("class")&&(o.styleclass=a.getAttribute("class")),a.getAttribute("align")&&(o.align=a.getAttribute("align"));for(var u={},d=a.getElementsByTagName("param"),l=d.length,c=0;c<l;c++)"movie"!==d[c].getAttribute("name").toLowerCase()&&(u[d[c].getAttribute("name")]=d[c].getAttribute("value"));w(o,u,n,r)}else D(a),r&&r(i);else W(n,!0),r&&(i.success=!0,i.ref=N(n),i.id=n,r(i))}else if(W(n,!0),r){var f=N(n);f&&typeof f.SetVariable!==s&&(i.success=!0,i.ref=f,i.id=f.id),r(i)}}}()},R.ie&&window.attachEvent("onunload",(function(){for(var t=T.length,n=0;n<t;n++)T[n][0].detachEvent(T[n][1],T[n][2]);for(var r=_.length,i=0;i<r;i++)M(_[i]);for(var a in R)R[a]=null;for(var o in R=null,e)e[o]=null;e=null})),{registerObject:function(e,t,n,r){if(R.w3&&e&&t){var i={};i.id=e,i.swfVersion=t,i.expressInstall=n,i.callbackFn=r,b[b.length]=i,W(e,!1)}else r&&r({success:!1,id:e})},getObjectById:function(e){if(R.w3)return N(e)},embedSWF:function(e,t,n,r,i,a,o,d,l,c){var f=k(t),E={success:!1,id:f};R.w3&&!(R.wk&&R.wk<312)&&e&&t&&n&&r&&i?(W(f,!1),I((function(){n+="",r+="";var p={};if(l&&typeof l===u)for(var m in l)p[m]=l[m];p.data=e,p.width=n,p.height=r;var h={};if(d&&typeof d===u)for(var g in d)h[g]=d[g];if(o&&typeof o===u)for(var b in o)if(Object.prototype.hasOwnProperty.call(o,b)){var _=v?encodeURIComponent(b):b,T=v?encodeURIComponent(o[b]):o[b];typeof h.flashvars!==s?h.flashvars+="&"+_+"="+T:h.flashvars=_+"="+T}if(G(i)){var y=P(p,h,t);p.id===f&&W(f,!0),E.success=!0,E.ref=y,E.id=y.id}else{if(a&&C())return p.data=a,void w(p,h,t,c);W(f,!0)}c&&c(E)}))):c&&c(E)},switchOffAutoHideShow:function(){A=!1},enableUriEncoding:function(e){v=typeof e===s||e},ua:R,getFlashPlayerVersion:function(){return{major:R.pv[0],minor:R.pv[1],release:R.pv[2]}},hasFlashPlayerVersion:G,createSWF:function(e,t,n){return R.w3?P(e,t,n):void 0},showExpressInstall:function(e,t,n,r){R.w3&&C()&&w(e,t,n,r)},removeSWF:function(e){R.w3&&M(e)},createCSS:function(e,t,n,r){R.w3&&V(e,t,n,r)},addDomLoadEvent:I,addLoadEvent:function(e){if(typeof E.addEventListener!==s)E.addEventListener("load",e,!1);else if(typeof p.addEventListener!==s)p.addEventListener("load",e,!1);else if(typeof E.attachEvent!==s)!function(e,t,n){e.attachEvent(t,n),T[T.length]=[e,t,n]}(E,"onload",e);else if("function"==typeof E.onload){var t=E.onload;E.onload=function(){t(),e()}}else E.onload=e},getQueryParamValue:function(e){var t=p.location.search||p.location.hash;if(t){if(/\?/.test(t)&&(t=t.split("?")[1]),!e)return Y(t);for(var n=t.split("&"),r=0;r<n.length;r++)if(n[r].substring(0,n[r].indexOf("="))===e)return Y(n[r].substring(n[r].indexOf("=")+1))}return""},expressInstallCallback:function(){if(S){var e=x(c);e&&t&&(e.parentNode.replaceChild(t,e),n&&(W(n,!0),R.ie&&(t.style.display="block")),r&&r(i)),S=!1}},version:"2.3"}}();return e}.apply(t,[]))||(e.exports=n)},8333:function(e,t,n){var r,i;r=[n(9088)],void 0===(i=function(e){function t(t,n,r){e.log("["+n+"] "+r,t)}function n(t){return e.enabled(t)}return{create:function(e){return{debug:t.bind(this,3,e),info:t.bind(this,2,e),warn:t.bind(this,1,e),error:t.bind(this,0,e),detail:t.bind(this,4,e),debugEnabled:n.bind(this,3),infoEnabled:n.bind(this,2),warnEnabled:n.bind(this,1),errorEnabled:n.bind(this,0),detailEnabled:n.bind(this,4)}}}}.apply(t,r))||(e.exports=i)},803:function(e,t){var n;void 0===(n=function(){var e,t;return{play:function(n){return new Promise((function(r,i){var a=n.play();Promise.all([a]).then((function(){r({media:n})})).catch((function(r){e=r&&r.name?r.name:"Unknown",t=r&&r.message?r.message:"Message not available.",i({media:n,error:{name:e,message:t}})}))}))}}}.apply(t,[]))||(e.exports=n)},8197:function(e,t){var n;void 0===(n=function(){var e=Object.prototype.toString;return{copy:function t(n){var r;switch(typeof n){case"object":if(null===n)r=null;else if(n&&n.tagName&&n.nodeName&&n instanceof Element)r=n;else switch(e.call(n)){case"[object Array]":r=n.map(t);break;case"[object Date]":r=new Date(n);break;case"[object RegExp]":r=new RegExp(n);break;default:r=Object.keys(n).reduce((function(e,r){return e[r]=t(n[r]),e}),{})}break;case"undefined":r=n;break;default:r=n.valueOf()}return r},findPropertyByValue:function(e,t){if("object"==typeof e)for(var n in e)if(e[n]===t)return n;return!1}}}.apply(t,[]))||(e.exports=n)},6028:function(e,t){var n;void 0===(n=function(){return{create:function(e,t,n){function r(e){n.emit(e.name,e)}return function(){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.on(t[n],r)}(),{destroy:function(){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.off(t[n],r)}}}}}.apply(t,[]))||(e.exports=n)},7176:function(e,t){var n;void 0===(n=function(){return{normalize:function(e,t,n){for(var r=function(e){var t=e.length-1,n=e.substr(0,1).toUpperCase()+e.substr(1,t);return["webkit"+n,"moz"+n,"ms"+n,e]}(t),i="",a=0;a<r.length;a+=1)if((i=r[a])in e.style){e.style[i]=n;break}}}}.apply(t,[]))||(e.exports=n)},5337:function(e,t){var n;void 0===(n=function(){function e(e){return e.reduce((function(e,t){return e+t}),0)/e.length}return{create:function(t,n){var r=[],i={};function a(e){return{minimum:o(i.minimum,e),maximum:o(i.maximum,e),arithmetic:o(i.arithmetic,e),geometric:o(i.geometric,e),harmonic:o(i.harmonic,e),deviation:o(i.deviation,e),count:o(i.count,e)}}function o(e,t){return isNaN(t)?e:Math.round(e*Math.pow(10,t))/Math.pow(10,t)}return i={add:function(o){for(r.push(o);r.length>t;)r.shift();var s,u;return i.count++,i.changed=!1,(!n||n&&0%n==0)&&(s=JSON.parse(JSON.stringify(r)).sort((function(e,t){return e-t}),0),i.arithmetic=e(s),i.geometric=(u=s,Math.pow(u.reduce((function(e,t){return e?t?e*t:e:t}),0),1/u.length)),i.harmonic=function(e){var t=e.reduce((function(e,t,n){return t?n-1?e+1/t:(e?1/e:0)+1/t:e}),0);return t?e.length/t:0}(s),i.minimum=s[0],i.maximum=s[s.length-1],i.deviation=function(t,n){var r=n||e(t);return t.length>1?Math.sqrt(t.reduce((function(e,t){return e+Math.pow(t-r,2)}),0)/(t.length-1)):0}(s,i.arithmetic),i.changed=!0),a()},get:a,minimum:0,maximum:0,first:0,last:0,arithmetic:0,geometric:0,harmonic:0,deviation:0,count:0,values:r,changed:!1}}}}.apply(t,[]))||(e.exports=n)},4556:function(e,t){var n;void 0===(n=function(){var e=Date.now?Date.now():+new Date,t=window.performance||{},n=[],r={},i=function(e,t){for(var r=0,i=n.length,a=[];r<i;r++)n[r][e]===t&&a.push(n[r]);return a},a=function(e,t){for(var r,i=n.length;i--;)(r=n[i]).entryType!==e||void 0!==t&&r.name!==t||n.splice(i,1)};return t.now||(t.now=t.webkitNow||t.mozNow||t.msNow||function(){return(Date.now?Date.now():+new Date)-e}),t.mark||(t.mark=t.webkitMark||function(e){var i={name:e,entryType:"mark",startTime:t.now(),duration:0};n.push(i),r[e]=i}),t.measure||(t.measure=t.webkitMeasure||function(e,t,i){t=r[t].startTime,i=r[i].startTime,n.push({name:e,entryType:"measure",startTime:t,duration:i-t})}),t.getEntriesByType||(t.getEntriesByType=t.webkitGetEntriesByType||function(e){return i("entryType",e)}),t.getEntriesByName||(t.getEntriesByName=t.webkitGetEntriesByName||function(e){return i("name",e)}),t.clearMarks||(t.clearMarks=t.webkitClearMarks||function(e){a("mark",e)}),t.clearMeasures||(t.clearMeasures=t.webkitClearMeasures||function(e){a("measure",e)}),void 0===window.performance&&(window.performance=t),window.performance}.apply(t,[]))||(e.exports=n)},2874:function(e,t){var n;void 0===(n=function(){var e=setTimeout;function t(){}function n(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],u(e,this)}function r(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,n._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void a(t.promise,e)}i(t.promise,r)}else(1===e._state?i:a)(t.promise,e._value)}))):e._deferreds.push(t)}function i(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var r=t.then;if(t instanceof n)return e._state=3,e._value=t,void o(e);if("function"==typeof r)return void u((i=r,s=t,function(){i.apply(s,arguments)}),e)}e._state=1,e._value=t,o(e)}catch(t){a(e,t)}var i,s}function a(e,t){e._state=2,e._value=t,o(e)}function o(e){2===e._state&&0===e._deferreds.length&&n._immediateFn((function(){e._handled||n._unhandledRejectionFn(e._value)}));for(var t=0,i=e._deferreds.length;t<i;t++)r(e,e._deferreds[t]);e._deferreds=null}function s(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function u(e,t){var n=!1;try{e((function(e){n||(n=!0,i(t,e))}),(function(e){n||(n=!0,a(t,e))}))}catch(e){if(n)return;n=!0,a(t,e)}}return n.prototype.catch=function(e){return this.then(null,e)},n.prototype.then=function(e,n){var i=new this.constructor(t);return r(this,new s(e,n,i)),i},n.all=function(e){var t=Array.prototype.slice.call(e);return new n((function(e,n){if(0===t.length)return e([]);var r=t.length;function i(a,o){try{if(o&&("object"==typeof o||"function"==typeof o)){var s=o.then;if("function"==typeof s)return void s.call(o,(function(e){i(a,e)}),n)}t[a]=o,0==--r&&e(t)}catch(e){n(e)}}for(var a=0;a<t.length;a++)i(a,t[a])}))},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n((function(t){t(e)}))},n.reject=function(e){return new n((function(t,n){n(e)}))},n.race=function(e){return new n((function(t,n){for(var r=0,i=e.length;r<i;r++)e[r].then(t,n)}))},n._immediateFn="function"==typeof window.setImmediate&&function(e){window.setImmediate(e)}||function(t){e(t,0)},n._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},n._setImmediateFn=function(e){n._immediateFn=e},n._setUnhandledRejectionFn=function(e){n._unhandledRejectionFn=e},void 0===window.Promise&&(window.Promise=n),window.Promise}.apply(t,[]))||(e.exports=n)},130:function(e,t){var n;void 0===(n=function(){return{NANOPLAYER:"4.22.1"}}.apply(t,[]))||(e.exports=n)},2058:function(e,t){var n;void 0===(n=function(){return{create:function(){return{style:{width:void 0,height:void 0,aspectratio:void 0,controls:!0,interactive:!0,view:!0,scaling:"letterbox",keepFrame:!0,displayAudioOnly:!0,audioPlayer:!1,displayMutedAutoplay:!0,backgroundColor:"black",fullScreenControl:!0,centerView:!0,symbolColor:"rgba(244,233,233,1)",controlBarColor:"rgba(0,0,0,0.5)",buttonAnimation:!0,buttonHighlighting:!0,buttonCursor:"pointer",poster:void 0,fullScreenBackgroundColor:"inherit"},events:{onFullscreenChange:void 0}}}}}.apply(t,[]))||(e.exports=n)},4355:function(e,t,n){var r,i;r=[n(6182)],void 0===(i=function(e){"use strict";return{create:function(t,n){var r=document.getElementById(t),i=0,a=0,o=0,s=n;return o=setInterval((function(){!r||void 0===r.clientWidth||"undefined"===r.clientHeight||r.clientWidth===i&&r.clientHeight===a||(i=r.clientWidth,a=r.clientHeight,s.emit(e.PLAYER_RESIZE,{width:i,height:a,aspectratio:i/a}))}),100),{destroy:function(){o&&clearInterval(o),o=0,r=null}}}}}.apply(t,r))||(e.exports=i)},5109:function(e,t){var n;void 0===(n=function(){return{create:function(){return{style:{width:"string",height:"string",aspectratio:"string",controls:"boolean",interactive:"boolean",view:"boolean",scaling:"string",keepFrame:"boolean",displayAudioOnly:"boolean",audioPlayer:"boolean",displayMutedAutoplay:"boolean",backgroundColor:"string",fullScreenControl:"boolean",centerView:"boolean",symbolColor:"string",controlBarColor:"string",buttonAnimation:"boolean",buttonHighlighting:"boolean",buttonCursor:"string",poster:"string",fullScreenBackgroundColor:"string"},events:{onFullscreenChange:"function"}}}}}.apply(t,[]))||(e.exports=n)},4869:function(e,t){var n;void 0===(n=function(){return Object.freeze({NONE:"none",LOADING:"loading",PLAYBUTTON:"playbutton",ERROR:"error",AUDIO:"audio"})}.apply(t,[]))||(e.exports=n)},5403:function(e,t){var n;void 0===(n=function(){var e="view.controls.";return{PLAY:e+"play",PAUSE:e+"pause",MUTE:e+"mute",UNMUTE:e+"unmute",VOLUME_CHANGE:e+"volumeChange",FULLSCREEN_CHANGE:e+"fullscreenChange",OPTIONS_OPEN:e+"optionsOpen",OPTIONS_CLOSE:e+"optionsClose"}}.apply(t,[]))||(e.exports=n)},2833:function(e,t){var n;void 0===(n=function(){return{CLICK:"click",DOUBLE_CLICK:"dblclick",TOUCH_START:"touchstart",TOUCH_END:"touchend",TOUCH_MOVE:"touchmove",MOUSE_DOWN:"mousedown",MOUSE_UP:"mouseup",MOUSE_ENTER:"mouseenter",MOUSE_LEAVE:"mouseleave",MOUSE_MOVE:"mousemove"}}.apply(t,[]))||(e.exports=n)},8718:function(e,t){var n;void 0===(n=function(){return{FULLSCREEN_CHANGE:"fullscreenchange",WEBKIT_FULLSCREEN_CHANGE:"webkitfullscreenchange",MOZ_FULLSCREEN_CHANGE:"mozfullscreenchange",MS_FULLSCREEN_CHANGE:"MSFullscreenChange",MS_FULLSCREEN_CHANGE_LOWERCASE:"msfullscreenchange"}}.apply(t,[]))||(e.exports=n)},5833:function(e,t){var n;void 0===(n=function(){return{FULLSCREEN_ERROR:"fullscreenerror",WEBKIT_FULLSCREEN_ERROR:"webkitfullscreenerror",MOZ_FULLSCREEN_ERROR:"mozfullscreenerror",MS_FULLSCREEN_ERROR:"MSFullscreenError",MS_FULLSCREEN_ERROR_LOWERCASE:"msfullscreenerror"}}.apply(t,[]))||(e.exports=n)},9087:function(e,t){var n;void 0===(n=function(){var e="view.fullscreen.";return{FULLSCREEN_CHANGE:e+"change",FULLSCREEN_ERROR:e+"error"}}.apply(t,[]))||(e.exports=n)},4124:function(e,t){var n;void 0===(n=function(){return Object.freeze({ENTERED:"entered",EXITED:"exited"})}.apply(t,[]))||(e.exports=n)},1117:function(e,t){var n;void 0===(n=function(){var e="view.interaction.";return{PLAYER_SINGLE_CLICK:e+"playerSingleClick",PLAYER_DOUBLE_CLICK:e+"playerDoubleClick",PLAYBUTTON_SINGLE_CLICK:e+"playbuttonSingleClick",AUDIO_SINGLE_CLICK:e+"audioSingleClick"}}.apply(t,[]))||(e.exports=n)},426:function(e,t){var n;void 0===(n=function(){return Object.freeze({MUTED:"muted",UNMUTED:"unmuted"})}.apply(t,[]))||(e.exports=n)},1710:function(e,t){var n;void 0===(n=function(){return Object.freeze({OPEN:"open",CLOSED:"closed"})}.apply(t,[]))||(e.exports=n)},5459:function(e,t){var n;void 0===(n=function(){return Object.freeze({UNINITIALIZED:1,IDLE:2,READY:3,LOADING:4,PLAYING:5,PAUSED:6,BUFFERING:7,UNKNOWN:8,PLAYBACK_NOT_STARTED:9,PLAYBACK_SUSPENDED:10,PAUSING:11,PLAYBACK_ERROR:12,RECONNECTION_IMMINENT:13,CONNECTION_ERROR:14,DESTROYING:15,PLAYBACK_RESTARTING:16})}.apply(t,[]))||(e.exports=n)},7245:function(e,t){var n;void 0===(n=function(){return Object.freeze({PLAY:"play",PAUSE:"pause"})}.apply(t,[]))||(e.exports=n)},8741:function(e,t){var n;void 0===(n=function(){return Object.freeze({FULLSCREEN_CHANGE:"FullscreenChange"})}.apply(t,[]))||(e.exports=n)},505:function(e,t){var n;void 0===(n=function(){return Object.freeze({NONE:"none",LETTERBOX:"letterbox",CROP:"crop",FILL:"fill"})}.apply(t,[]))||(e.exports=n)},8812:function(e,t){var n;void 0===(n=function(){return{controls:{fullscreen:{viewBox:[0,0,24,24],paths:{exited:"M6 14c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1h3c.55 0 1-.45 1-1s-.45-1-1-1H7v-2c0-.55-.45-1-1-1Zm0-4c.55 0 1-.45 1-1V7h2c.55 0 1-.45 1-1s-.45-1-1-1H6c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1Zm11 7h-2c-.55 0-1 .45-1 1s.45 1 1 1h3c.55 0 1-.45 1-1v-3c0-.55-.45-1-1-1s-1 .45-1 1v2ZM14 6c0 .55.45 1 1 1h2v2c0 .55.45 1 1 1s1-.45 1-1V6c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1Z",entered:"M6 16h2v2c0 .55.45 1 1 1s1-.45 1-1v-3c0-.55-.45-1-1-1H6c-.55 0-1 .45-1 1s.45 1 1 1Zm2-8H6c-.55 0-1 .45-1 1s.45 1 1 1h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1s-1 .45-1 1v2Zm7 11c.55 0 1-.45 1-1v-2h2c.55 0 1-.45 1-1s-.45-1-1-1h-3c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1Zm1-11V6c0-.55-.45-1-1-1s-1 .45-1 1v3c0 .55.45 1 1 1h3c.55 0 1-.45 1-1s-.45-1-1-1h-2Z"}},time:{viewBox:[0,0,72,24],paths:{}},mute:{viewBox:[0,0,24,24],paths:{muted:"M7 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L11 9H8c-.55 0-1 .45-1 1z",up:"M3 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L7 9H4c-.55 0-1 .45-1 1zm13.5 2c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 4.45v.2c0 .38.25.71.6.85C17.18 6.53 19 9.06 19 12s-1.82 5.47-4.4 6.5c-.36.14-.6.47-.6.85v.2c0 .63.63 1.07 1.21.85C18.6 19.11 21 15.84 21 12s-2.4-7.11-5.79-8.4c-.58-.23-1.21.22-1.21.85z",down:"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L9 9H6c-.55 0-1 .45-1 1z",off:"M3.63 3.63c-.39.39-.39 1.02 0 1.41L7.29 8.7 7 9H4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71v-4.17l4.18 4.18c-.49.37-1.02.68-1.6.91-.36.15-.58.53-.58.92 0 .72.73 1.18 1.39.91.8-.33 1.55-.77 2.22-1.31l1.34 1.34c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L5.05 3.63c-.39-.39-1.02-.39-1.42 0zM19 12c0 .82-.15 1.61-.41 2.34l1.53 1.53c.56-1.17.88-2.48.88-3.87 0-3.83-2.4-7.11-5.78-8.4-.59-.23-1.22.23-1.22.86v.19c0 .38.25.71.61.85C17.18 6.54 19 9.06 19 12zm-8.71-6.29l-.17.17L12 7.76V6.41c0-.89-1.08-1.33-1.71-.7zM16.5 12c0-1.77-1.02-3.29-2.5-4.03v1.79l2.48 2.48c.01-.08.02-.16.02-.24z"}},playpause:{viewBox:[0,0,24,24],paths:{play:"M8 6.82v10.36c0 .79.87 1.27 1.54.84l8.14-5.18c.62-.39.62-1.29 0-1.69L9.54 5.98C8.87 5.55 8 6.03 8 6.82z",pause:"M8 19c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2v10c0 1.1.9 2 2 2zm6-12v10c0 1.1.9 2 2 2s2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2z"}},volume:{viewBox:[0,0,72,24],paths:{line:"M13 11c-.55 0-1 .45-1 1s.45 1 1 1h46c.55 0 1-.45 1-1s-.45-1-1-1z",right:"",knob:""}},options:{viewBox:[0,0,24,24],paths:{settings:"M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z",tune:"M3 18c0 .55.45 1 1 1h5v-2H4c-.55 0-1 .45-1 1zM3 6c0 .55.45 1 1 1h9V5H4c-.55 0-1 .45-1 1zm10 14v-1h7c.55 0 1-.45 1-1s-.45-1-1-1h-7v-1c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1s1-.45 1-1zM7 10v1H4c-.55 0-1 .45-1 1s.45 1 1 1h3v1c0 .55.45 1 1 1s1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1zm14 2c0-.55-.45-1-1-1h-9v2h9c.55 0 1-.45 1-1zm-5-3c.55 0 1-.45 1-1V7h3c.55 0 1-.45 1-1s-.45-1-1-1h-3V4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1z"}}},centerView:{playButton:{viewBox:[0,0,24,24],paths:{play:"M10.8 15.9l4.67-3.5c.27-.2.27-.6 0-.8L10.8 8.1c-.33-.25-.8-.01-.8.4v7c0 .41.47.65.8.4zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"}},loading:{viewBox:[0,0,24,24],paths:{circleFull:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8s8 3.59 8 8s-3.59 8-8 8z",circleHalf:"M22 12c0 4.73-3.3 8.71-7.73 9.74c-.62.15-1.22-.34-1.22-.98c0-.46.31-.86.75-.97c3.55-.82 6.2-4 6.2-7.79s-2.65-6.97-6.2-7.79c-.44-.1-.75-.51-.75-.97c0-.64.6-1.13 1.22-.98C18.7 3.29 22 7.27 22 12z",circleQuarter:"M20 12c0-3.79-2.65-6.97-6.2-7.79c-.44-.1-.75-.51-.75-.97c0-.64.6-1.13 1.22-.98c4.43 1.03 7.73 5.01 7.73 9.74c0 .55-.45 1-1 1c-.55 0-1-.45-1-1z"}},error:{viewBox:[0,0,24,24],paths:{error:"M12 7c.55 0 1 .45 1 1v4c0 .55-.45 1-1 1s-1-.45-1-1V8c0-.55.45-1 1-1zm-.01-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm1-3h-2v-2h2v2z",warning:"M4.47 21h15.06c1.54 0 2.5-1.67 1.73-3L13.73 4.99c-.77-1.33-2.69-1.33-3.46 0L2.74 18c-.77 1.33.19 3 1.73 3zM12 14c-.55 0-1-.45-1-1v-2c0-.55.45-1 1-1s1 .45 1 1v2c0 .55-.45 1-1 1zm1 4h-2v-2h2v2z"}},audio:{viewBox:[0,0,24,24],paths:{muted:"M7 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L11 9H8c-.55 0-1 .45-1 1z",up:"M3 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L7 9H4c-.55 0-1 .45-1 1zm13.5 2c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 4.45v.2c0 .38.25.71.6.85C17.18 6.53 19 9.06 19 12s-1.82 5.47-4.4 6.5c-.36.14-.6.47-.6.85v.2c0 .63.63 1.07 1.21.85C18.6 19.11 21 15.84 21 12s-2.4-7.11-5.79-8.4c-.58-.23-1.21.22-1.21.85z",down:"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L9 9H6c-.55 0-1 .45-1 1z",off:"M3.63 3.63c-.39.39-.39 1.02 0 1.41L7.29 8.7 7 9H4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71v-4.17l4.18 4.18c-.49.37-1.02.68-1.6.91-.36.15-.58.53-.58.92 0 .72.73 1.18 1.39.91.8-.33 1.55-.77 2.22-1.31l1.34 1.34c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L5.05 3.63c-.39-.39-1.02-.39-1.42 0zM19 12c0 .82-.15 1.61-.41 2.34l1.53 1.53c.56-1.17.88-2.48.88-3.87 0-3.83-2.4-7.11-5.78-8.4-.59-.23-1.22.23-1.22.86v.19c0 .38.25.71.61.85C17.18 6.54 19 9.06 19 12zm-8.71-6.29l-.17.17L12 7.76V6.41c0-.89-1.08-1.33-1.71-.7zM16.5 12c0-1.77-1.02-3.29-2.5-4.03v1.79l2.48 2.48c.01-.08.02-.16.02-.24z"}}}}}.apply(t,[]))||(e.exports=n)},6182:function(e,t){var n;void 0===(n=function(){return{PAUSE:"view.pause",PLAY:"view.play",FULLSCREEN_CHANGE:"view.fullscreenChange",PLAYER_RESIZE:"view.playerResize",MUTE:"view.mute",UNMUTE:"view.unmute",VOLUME_CHANGE:"view.volumeChange"}}.apply(t,[]))||(e.exports=n)},6643:function(e,t,n){var r,i;r=[n(1933),n(2833),n(7245),n(426),n(4124),n(5559),n(4586),n(7615),n(3703),n(2103),n(5770)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l){"use strict";return{create:function(c,f,E){var p=document.getElementById(c),m="controls-"+c,h=document.createElement("div"),g=0,b=E.style.audioPlayer,_=E.style.fullScreenControl,T=E.style.optionsControl,y=E.style.controlBarColor,S={symbolColor:E.style.symbolColor,buttonAnimation:E.style.buttonAnimation,buttonHighlighting:E.style.buttonHighlighting,buttonCursor:E.style.buttonCursor},A={playPauseControl:void 0,muteControl:void 0,volumeControl:void 0,fullscreenControl:void 0,timeControl:void 0,optionsControl:void 0};function v(){h&&(h.style.visibility="visible")}function R(){h&&(h.style.visibility="hidden")}function O(e){e.button&&0!==e.button||e.target.id&&-1!==e.target.id.indexOf("playButton-")||I()}function I(){g&&clearTimeout(g),g=0,g=setTimeout(R,5e3),v()}return!document.getElementById(m)&&p&&(h.id=m,h.style.width="100%",h.style.height=(b?"100":Math.round(e.mobile?100/6:100/12))+"%",h.style.maxHeight=Math.floor(p.clientWidth/9)+"px",h.style.position="absolute",h.style.left="0",h.style.bottom="0",h.style.zIndex=2352346258378,h.style.backgroundColor=y,!b&&R(),p.appendChild(h),A.playPauseControl=a.create(h,f,S),A.muteControl=o.create(h,f,S,b),A.volumeControl=s.create(h,f,S,b),A.timeControl=d.create(h,S),!b&&_&&(A.fullscreenControl=u.create(h,f,S)),T&&(A.optionsControl=l.create(h,f,S)),!b&&p.addEventListener(t.MOUSE_MOVE,O,!0)),{size:function(){h.style.width="100%";var t=Math.round(e.mobile?p.clientHeight/6:p.clientHeight/12),n=Math.round(e.mobile?h.clientWidth/8:h.clientWidth/12);for(var r in h.style.height=(b?"100":Math.min(t,n))+"px",h.style.maxHeight=Math.floor(p.clientWidth/9)+"px",A)A[r]&&A[r].update()},appear:I,show:v,hide:R,play:function(){A.playPauseControl.update(n.PLAY)},pause:function(){A.playPauseControl.update(n.PAUSE)},fullscreen:function(e){!b&&_&&(h.style.zIndex=2352346258378,e?A.fullscreenControl.update(i.ENTERED):A.fullscreenControl.update(i.EXITED))},mute:function(e){A.muteControl.update(r.MUTED,e)},unmute:function(e){A.muteControl.update(r.UNMUTED,e)},volume:function(e){A.volumeControl.update(e)},time:function(e){A.timeControl.update(e)},destroy:function(){for(var e in g&&clearTimeout(g),A)void 0!==A[e]&&A[e].destroy(),delete A[e];b&&p&&p.removeEventListener(t.MOUSE_MOVE,O),h&&h.parentNode===p&&p.removeChild(h),h=null,p=null}}}}}.apply(t,r))||(e.exports=i)},3703:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(4124),n(8812)],void 0===(i=function(e,t,n,r,i,a,o){"use strict";return{create:function(s,u,d){var l,c,f,E="http://www.w3.org/2000/svg",p=t.create(d.symbolColor),m=p.get(),h=document.createElement("div"),g=a.EXITED,b="fullscreen-"+s.id,_=document.createElementNS(E,"path"),T=s.clientHeight,y=o.controls.fullscreen.paths,S=o.controls.fullscreen.viewBox;d.buttonHighlighting&&(f="rect",c=1,l=document.createElementNS(E,f));var A=0;return d.buttonHighlighting&&("rect"===f?(n.setAttribute(l,"x",c),n.setAttribute(l,"y",c),n.setAttribute(l,"width",S[2]-2*c),n.setAttribute(l,"height",S[3]-2*c),n.setAttribute(l,"rx",4),n.setAttribute(l,"ry",4),n.setAttribute(l,"fill",p.getGradientUrl("vertical"))):(n.setAttribute(l,"cx",S[2]/2),n.setAttribute(l,"cy",S[3]/2),n.setAttribute(l,"r",Math.min(S[2],S[3])-c),n.setAttribute(l,"fill",p.getGradientUrl("radial"))),n.setAttribute(l,"opacity","0"),m.appendChild(l)),n.setAttribute(m,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(m,"transform","scale(0.9)"),n.setAttribute(m,"viewBox",S.join(" ")),n.setAttribute(m,"fill",d.symbolColor),h.id=b,h.style.float="right",h.style.display="inline-block",h.style.width=T+"px",h.style.height=T+"px",h.style.cursor=d.buttonCursor,h.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.8)")})),h.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(A&&clearTimeout(A)&&(A=0),A=setTimeout((function(){m&&-1!==n.getAttribute(m,"transform").indexOf("0.8")&&n.setAttribute(m,"transform","scale(1.0)")}),50),u.emit(i.FULLSCREEN_CHANGE))})),h.addEventListener(r.MOUSE_ENTER,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0.15"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(1.0)")})),h.addEventListener(r.MOUSE_LEAVE,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.9)")})),m.appendChild(_),h.appendChild(m),s.appendChild(h),{update:function(e){e&&(g=e),T=s.clientHeight,h.style.width=T+"px",h.style.height=T+"px",g===a.ENTERED?n.setAttribute(_,"d",y.entered):g===a.EXITED&&n.setAttribute(_,"d",y.exited)},destroy:function(){A&&clearTimeout(A)&&(A=0),null!==s&&null!==m&&null!==m.parentNode&&m.parentNode===s&&s.removeChild(m),m=null}}}}}.apply(t,r))||(e.exports=i)},4586:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(426),n(8812)],void 0===(i=function(e,t,n,r,i,a,o){"use strict";return{create:function(s,u,d){var l,c,f,E="http://www.w3.org/2000/svg",p=t.create(d.symbolColor),m=p.get(),h=document.createElement("div"),g=a.UNMUTED,b=1,_="mute-"+s.id,T=document.createElementNS(E,"path"),y=s.clientHeight,S=o.controls.mute.paths,A=o.controls.mute.viewBox;d.buttonHighlighting&&(f="rect",c=1,l=document.createElementNS(E,f));var v=0;return d.buttonHighlighting&&("rect"===f?(n.setAttribute(l,"x",c),n.setAttribute(l,"y",c),n.setAttribute(l,"width",A[2]-2*c),n.setAttribute(l,"height",A[3]-2*c),n.setAttribute(l,"rx",4),n.setAttribute(l,"ry",4),n.setAttribute(l,"fill",p.getGradientUrl("vertical"))):(n.setAttribute(l,"cx",A[2]/2),n.setAttribute(l,"cy",A[3]/2),n.setAttribute(l,"r",Math.min(A[2],A[3])-c),n.setAttribute(l,"fill",p.getGradientUrl("radial"))),n.setAttribute(l,"opacity","0"),m.appendChild(l)),n.setAttribute(m,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(m,"transform","scale(0.9)"),n.setAttribute(m,"viewBox",A.join(" ")),n.setAttribute(m,"fill",d.symbolColor),h.id=_,h.style.float="left",h.style.display="inline-block",h.style.width=y+"px",h.style.height=y+"px",h.style.cursor=d.buttonCursor,h.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.8)")})),h.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(v&&clearTimeout(v)&&(v=0),v=setTimeout((function(){m&&-1!==n.getAttribute(m,"transform").indexOf("0.8")&&n.setAttribute(m,"transform","scale(1.0)"),g===a.MUTED?u.emit(i.UNMUTE):g===a.UNMUTED&&u.emit(i.MUTE)}),50))})),h.addEventListener(r.MOUSE_ENTER,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0.15"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(1.0)")})),h.addEventListener(r.MOUSE_LEAVE,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.9)")})),m.appendChild(T),h.appendChild(m),s.appendChild(h),{update:function(e,t){e&&(g=e),void 0!==t&&(b=t),y=s.clientHeight,h.style.width=y+"px",h.style.height=y+"px",g===a.MUTED?n.setAttribute(T,"d",S.off):g===a.UNMUTED&&(b>=.5?n.setAttribute(T,"d",S.up):b>0?n.setAttribute(T,"d",S.down):n.setAttribute(T,"d",S.muted))},destroy:function(){v&&clearTimeout(v)&&(v=0),null!==s&&null!==m&&null!==m.parentNode&&m.parentNode===s&&s.removeChild(m),m=null}}}}}.apply(t,r))||(e.exports=i)},5770:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(1710),n(8812)],void 0===(i=function(e,t,n,r,i,a,o){"use strict";return{create:function(s,u,d){var l,c,f,E="http://www.w3.org/2000/svg",p=t.create(d.symbolColor),m=p.get(),h=document.createElement("div"),g="options-"+s.id,b=a.CLOSED,_=document.createElementNS(E,"path"),T=s.clientHeight,y=o.controls.options.paths,S=o.controls.options.viewBox;d.buttonHighlighting&&(f="rect",c=1,l=document.createElementNS(E,f));var A=0;return d.buttonHighlighting&&("rect"===f?(n.setAttribute(l,"x",c),n.setAttribute(l,"y",c),n.setAttribute(l,"width",S[2]-2*c),n.setAttribute(l,"height",S[3]-2*c),n.setAttribute(l,"rx",4),n.setAttribute(l,"ry",4),n.setAttribute(l,"fill",p.getGradientUrl("vertical"))):(n.setAttribute(l,"cx",S[2]/2),n.setAttribute(l,"cy",S[3]/2),n.setAttribute(l,"r",Math.min(S[2],S[3])-c),n.setAttribute(l,"fill",p.getGradientUrl("radial"))),n.setAttribute(l,"opacity","0"),m.appendChild(l)),n.setAttribute(m,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(m,"transform","scale(0.9)"),n.setAttribute(m,"viewBox",S.join(" ")),n.setAttribute(m,"fill",d.symbolColor),h.id=g,h.style.float="right",h.style.display="inline-block",h.style.width=T+"px",h.style.height=T+"px",h.style.cursor=d.buttonCursor,h.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.8)")})),h.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(A&&clearTimeout(A)&&(A=0),A=setTimeout((function(){m&&-1!==n.getAttribute(m,"transform").indexOf("0.8")&&n.setAttribute(m,"transform","scale(1.0)"),b===a.CLOSED?u.emit(i.OPTIONS_OPEN):u.emit(i.OPTIONS_CLOSE)}),50))})),h.addEventListener(r.MOUSE_ENTER,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0.15"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(1.0)")})),h.addEventListener(r.MOUSE_LEAVE,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.9)")})),m.appendChild(_),h.appendChild(m),s.appendChild(h),{update:function(e){e&&(b=e),T=s.clientHeight,h.style.width=T+"px",h.style.height=T+"px",n.setAttribute(_,"d",Math.random()>.5?y.settings:y.tune)},destroy:function(){A&&clearTimeout(A)&&(A=0),null!==s&&null!==m&&null!==m.parentNode&&m.parentNode===s&&s.removeChild(m),m=null}}}}}.apply(t,r))||(e.exports=i)},5559:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(7245),n(8812)],void 0===(i=function(e,t,n,r,i,a,o){"use strict";return{create:function(s,u,d){var l,c,f,E="http://www.w3.org/2000/svg",p=t.create(d.symbolColor),m=p.get(),h=document.createElement("div"),g=a.PLAY,b="playpause-"+s.id,_=document.createElementNS(E,"path"),T=s.clientHeight,y=o.controls.playpause.paths,S=o.controls.playpause.viewBox;d.buttonHighlighting&&(f="rect",c=1,l=document.createElementNS(E,f));var A=0;return d.buttonHighlighting&&("rect"===f?(n.setAttribute(l,"x",c),n.setAttribute(l,"y",c),n.setAttribute(l,"width",S[2]-2*c),n.setAttribute(l,"height",S[3]-2*c),n.setAttribute(l,"rx",4),n.setAttribute(l,"ry",4),n.setAttribute(l,"fill",p.getGradientUrl("vertical"))):(n.setAttribute(l,"cx",S[2]/2),n.setAttribute(l,"cy",S[3]/2),n.setAttribute(l,"r",Math.min(S[2],S[3])-c),n.setAttribute(l,"fill",p.getGradientUrl("radial"))),n.setAttribute(l,"opacity","0"),m.appendChild(l)),n.setAttribute(m,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(m,"transform","scale(0.9)"),n.setAttribute(m,"viewBox",S.join(" ")),n.setAttribute(m,"fill",d.symbolColor),h.id=b,h.style.float="left",h.style.display="inline-block",h.style.width=T+"px",h.style.height=T+"px",h.style.cursor=d.buttonCursor,h.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.8)")})),h.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(A&&clearTimeout(A)&&(A=0),A=setTimeout((function(){m&&-1!==n.getAttribute(m,"transform").indexOf("0.8")&&n.setAttribute(m,"transform","scale(1.0)"),g===a.PLAY?u.emit(i.PLAY):g===a.PAUSE&&u.emit(i.PAUSE)}),50))})),h.addEventListener(r.MOUSE_ENTER,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0.15"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(1.0)")})),h.addEventListener(r.MOUSE_LEAVE,(function(){d.buttonHighlighting&&n.setAttribute(l,"opacity","0"),d.buttonAnimation&&n.setAttribute(m,"transform","scale(0.9)")})),m.appendChild(_),h.appendChild(m),s.appendChild(h),{update:function(e){e&&(g=e),T=s.clientHeight,h.style.width=T+"px",h.style.height=T+"px",g===a.PLAY?n.setAttribute(_,"d",y.play):g===a.PAUSE&&n.setAttribute(_,"d",y.pause)},destroy:function(){A&&clearTimeout(A)&&(A=0),null!==s&&null!==m&&null!==m.parentNode&&m.parentNode===s&&s.removeChild(m),m=null}}}}}.apply(t,r))||(e.exports=i)},2103:function(e,t,n){var r,i;r=[n(7251),n(3740),n(8143),n(8812)],void 0===(i=function(e,t,n,r){"use strict";return{create:function(i,a){var o=e.create(a.symbolColor).get(),s=document.createElement("div"),u="time-"+i.id,d=document.createElementNS("http://www.w3.org/2000/svg","text"),l=i.clientHeight,c="0:00",f=r.controls.time.viewBox,E=(Math.min(f[2],f[3])-12)/2,p=Math.min(f[2],f[3])-E;return t.setAttribute(d,"x",E),t.setAttribute(d,"y",p),t.setAttribute(d,"font-size","12pt"),t.setAttribute(d,"font-weight","100"),t.setAttribute(d,"font-family","Arial, Helvetica, sans-serif;"),t.setAttribute(d,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),t.setAttribute(o,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),t.setAttribute(o,"transform","scale(0.9)"),t.setAttribute(o,"viewBox",f.join(" ")),t.setAttribute(o,"fill",a.symbolColor),s.id=u,s.style.float="left",s.style.display="inline-block",s.style.cursor="default",o.appendChild(d),s.appendChild(o),i.appendChild(s),{update:function(e){e&&(c=n.format(e)),l=i.clientHeight,s.style.width=f[2]/f[3]*l+"px",s.style.height=l+"px",d.textContent=c},destroy:function(){null!==i&&null!==o&&null!==o.parentNode&&o.parentNode===i&&i.removeChild(o),o=null}}}}}.apply(t,r))||(e.exports=i)},7615:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(8812)],void 0===(i=function(e,t,n,r,i,a){"use strict";return{create:function(o,s,u){var d,l,c,f="http://www.w3.org/2000/svg",E=t.create(u.symbolColor),p=E.get(),m=document.createElement("div"),h=1,g="volume-"+o.id,b=document.createElementNS(f,"path"),_=document.createElementNS(f,"path"),T=document.createElementNS(f,"circle"),y=o.clientHeight,S=a.controls.volume.paths,A=a.controls.volume.viewBox,v=12,R=A[2]-24,O=R+v,I=!1;u.buttonHighlighting&&(c="circle",l=1,d=document.createElementNS(f,c));var N=0;function C(t){t&&void 0!==t.button&&0!==t.button||(P(t)?(u.buttonAnimation&&u.buttonHighlighting&&"rect"!==c&&n.setAttribute(d,"r",.8*(v-l)),document.addEventListener(e.mobile?r.TOUCH_MOVE:r.MOUSE_MOVE,D),document.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,w),I=!0):w({button:0}))}function w(t){if(!t||void 0===t.button||0===t.button){N&&clearTimeout(N)&&(N=0),N=setTimeout((function(){u.buttonAnimation&&u.buttonHighlighting&&"rect"!==c&&n.setAttribute(d,"r",v-l)}),50),document.removeEventListener(e.mobile?r.TOUCH_MOVE:r.MOUSE_MOVE,D),I=!1;var i=M(t),a=p.getBoundingClientRect();(i.clientX<=a.left||i.clientX>=a.right||i.clientY<=a.top||i.clientY>=a.bottom)&&U()}}function D(e){e&&void 0!==e.button&&0!==e.button||I&&(P(e)||w({button:0}))}function U(){I||(u.buttonHighlighting&&n.setAttribute(d,"opacity","0"),u.buttonAnimation&&n.setAttribute(p,"transform","scale(0.9)"))}function P(e){var t=M(e),n=p.getBoundingClientRect();t.clientX<=n.left||t.clientX>=n.right||t.clientY<=n.top||(t.clientY,n.bottom);var r=n.right-n.left,a=(R+24)/r,o=(t.clientX-n.left)*a;o>=O?o=O:o<=12&&(o=12),L(o);var u=(o-12)/(O-12);return u=Math.round(100*u)/100,s.emit(i.VOLUME_CHANGE,{volume:u}),!0}function L(e){T.setAttribute("cx",e),u.buttonHighlighting&&("rect"===c?n.setAttribute(d,"x",e-(v-l)):n.setAttribute(d,"cx",e)),F(_,S.line,e-v,v)}function M(e,t,n){var r={clientX:t||0,clientY:n||0};return void 0!==e.clientX&&void 0!==e.clientY?(r.clientX=e.clientX,r.clientY=e.clientY):e.changedTouches&&e.changedTouches.length?(r.clientX=e.changedTouches[0].clientX,r.clientY=e.changedTouches[0].clientY):e.touches&&e.touches.length&&(r.clientX=e.touches[0].clientX,r.clientY=e.touches[0].clientY),r}function F(e,t,n,r){var i=t.split("h"),a=i[0].split("c"),o=a[0].split(" ");o[0]="M"+(r+1),a[0]=o.join(" "),i[0]=a.join("c");var s=i[1].split("c");s[0]=n-2,i[1]=s.join("c");var u=i.join("h");e.setAttribute("d",u)}return F(b,S.line,O-v,v),b.setAttribute("opacity","0.5"),F(_,S.line,O-v,v),_.setAttribute("opacity","1"),T.setAttribute("r",4),T.setAttribute("cx",O),T.setAttribute("cy",A[3]/2),T.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,C),T.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,w),u.buttonHighlighting&&("rect"===c?(n.setAttribute(d,"x",O-(v-l)),n.setAttribute(d,"y",l),n.setAttribute(d,"width",2*(v-l)),n.setAttribute(d,"height",2*(v-l)),n.setAttribute(d,"rx",4),n.setAttribute(d,"ry",4),n.setAttribute(d,"fill",E.getGradientUrl("vertical"))):(n.setAttribute(d,"cx",O),n.setAttribute(d,"cy",A[3]/2),n.setAttribute(d,"r",v-l),n.setAttribute(d,"fill",E.getGradientUrl("vertical"))),n.setAttribute(d,"opacity","0"),p.appendChild(d)),n.setAttribute(p,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(p,"transform","scale(0.9)"),n.setAttribute(p,"viewBox",A.join(" ")),n.setAttribute(p,"fill",u.symbolColor),m.id=g,m.style.float="left",m.style.display="inline-block",m.style.width=A[2]/A[3]*y+"px",m.style.height=y+"px",m.style.cursor=u.buttonCursor,m.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,C),m.addEventListener(r.MOUSE_ENTER,(function(){u.buttonHighlighting&&n.setAttribute(d,"opacity","0.15"),u.buttonAnimation&&n.setAttribute(p,"transform","scale(1.0)")})),m.addEventListener(r.MOUSE_LEAVE,U),p.appendChild(b),p.appendChild(_),p.appendChild(T),m.appendChild(p),o.appendChild(m),{update:function(e){void 0!==e&&(h=e),y=o.clientHeight,m.style.width=A[2]/A[3]*y+"px",m.style.height=y+"px",L(R*h+v)},destroy:function(){N&&clearTimeout(N)&&(N=0),null!==o&&null!==p&&null!==p.parentNode&&p.parentNode===o&&o.removeChild(p),p=null}}}}}.apply(t,r))||(e.exports=i)},6444:function(e,t,n){var r,i;r=[n(6627),n(7176),n(4124),n(9087)],void 0===(i=function(e,t,n,r){"use strict";return{create:function(t,i,a){var o,s,u,d=document.getElementById(t),l=n.EXITED,c=!1,f={},E="",p="",m="",h=a.style.backgroundColor,g=a.style.fullScreenBackgroundColor,b=0,_=[],T=1,y=0,S=null,A="",v={width:"10000px",height:"10000px",left:"-4000px",top:"-4000px",padding:"0px",margin:"0px",position:"fixed","background-color":g,"z-index":"0"},R=void 0;function O(e){e?(document.body.style.setProperty("border-width",p),document.body.style.setProperty("background-color",E),d.style.setProperty("border-width",m),d.style.setProperty("background-color",h)):(p=document.body.style.borderWidth,document.body.style.setProperty("border-width","0px","important"),E=document.body.style.backgroundColor,document.body.style.setProperty("background-color",g,"important"),m=d.style.borderWidth,d.style.setProperty("border-width","0px","important"),d.style.setProperty("background-color",g,"important"))}function I(){return l===n.ENTERED}function N(){try{var e,t;if(I()){for(U();_.length;)(t=_.shift()).element.style.cssText=t.cssText,t.border&&(t.element.frameBorder=t.border);S=null,l=n.EXITED,O(!0),document.body.removeChild(s),function(){if(c&&o&&f){var e,t="";for(e in f)t.length?t+=", "+e+"="+f[e]:t+=e+"="+f[e];o.content=t}}(),d.removeChild(u),i.emit(r.FULLSCREEN_CHANGE,{entered:!1}),R&&Object.prototype.hasOwnProperty.call(R,"x")&&Object.prototype.hasOwnProperty.call(R,"y")&&window.scroll(R.x,R.y),R=void 0}else{for(R||(R={y:window.pageYOffset,x:window.pageXOffset}),_.unshift({window:window,document:window.document,element:d,cssText:d.style.cssText,border:null}),P(window),U(),b=setInterval(D,300),e=0;e<_.length;e+=1)(t=_[e]).element.style.setProperty("position","fixed","important"),t.element.style.setProperty("left","0px","important"),t.element.style.setProperty("top","0px","important"),t.element.style.setProperty("right","0px","important"),t.element.style.setProperty("bottom","0px","important"),t.element.style.setProperty("z-index","1000","important"),t.border&&(t.element.frameBorder="0");C(),l=n.ENTERED,d.appendChild(u),document.body.appendChild(s),O(),i.emit(r.FULLSCREEN_CHANGE,{entered:!0})}}catch(e){i.emit(r.FULLSCREEN_ERROR,{reason:"denied"})}}function C(){_.forEach(w)}function w(t){!S&&(S=_[0]),T=S.document.documentElement.clientWidth/S.window.innerWidth,y=(y=(0===S.window.orientation?S.window.screen.height:S.window.screen.width)-S.window.innerWidth*T)>1&&!c?y:0,0===S.window.orientation?t.element.style.paddingTop="env(safe-area-inset-top)":t.element.style.paddingTop=0,(window.pageXOffset<0||window.pageYOffset<0)&&window.scroll(0,0),t.element.style.width=S.window.innerWidth*T+"px",t.element.style.height=(e.isSafari?S.window.innerHeight*T:Math.max(S.document.documentElement.clientHeight,S.window.innerHeight*T))-y+"px"}function D(){try{I()&&C()}catch(e){i.emit(r.FULLSCREEN_ERROR,{reason:"denied"})}}function U(){clearInterval(b),b=0}function P(e){if(e.parent!==e.self)for(var t=e.parent.frames,n=0;n<t.length;n++){var r=t[n];if(e.self===r&&r.frameElement&&r.frameElement.tagName&&"IFRAME"===r.frameElement.tagName){for(var i=r.frameElement.attributes,a=!1,o=0;o<i.length;o+=1)if(i[o].nodeName&&-1!==i[o].nodeName.indexOf("allowfullscreen")&&"false"!==i[o].nodeValue){a=!0;break}a&&(_.unshift({window:e.parent,document:e.parent.document,element:r.frameElement,cssText:r.frameElement.style.cssText,border:r.frameElement.frameBorder}),P(e.parent))}}}return function(){var e,t,n,r,i,a=document.querySelectorAll("meta");for(e=0;e<a.length;++e){var d=a[e].getAttribute("name"),l=a[e].getAttribute("content");if(d&&"viewport"===d.toLowerCase()){if(c=!0,l)for(l=l.split(","),t=0;t<l.length;t++)l[t]&&l[t].length&&-1!==(n=l[t].trim().indexOf("="))&&n!==l[t].trim().length-1&&(r=l[t].trim().substring(0,n),i=l[t].trim().substring(n+1),r&&i&&(f[r]=i));o=a[e]}}!function(){for(var e in(s=document.createElement("meta")).name="viewport",f["initial-scale"]&&(A+=(A.length?", ":"")+"initial-scale=1.0"),f["maximum-scale"]&&(A+=(A.length?", ":"")+"maximum-scale=1.0"),s.content=A,u=document.createElement("div"),v)Object.prototype.hasOwnProperty.call(v,e)&&u.style.setProperty(e,v[e],"position"===e?"important":"")}()}(),{change:function(){N()},entered:I,enabled:function(){return!0},destroy:function(){I()&&N(),d=null}}}}}.apply(t,r))||(e.exports=i)},4103:function(e,t,n){var r,i;r=[n(5533),n(6444)],void 0===(i=function(e,t){"use strict";return{create:function(n,r,i){return document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen?e.create(n,r,i):t.create(n,r,i)}}}.apply(t,r))||(e.exports=i)},5533:function(e,t,n){var r,i;r=[n(8718),n(5833),n(9087)],void 0===(i=function(e,t,n){"use strict";return{create:function(r,i,a){var o=document.getElementById(r);o.requestFullScreen=o.requestFullscreen||o.mozRequestFullScreen||o.webkitRequestFullscreen||o.msRequestFullscreen,document.exitFullScreen=document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen;var s=o.style.cssText,u="",d=a.style.backgroundColor,l=a.style.fullScreenBackgroundColor,c="",f="",E="",p="",m="",h=!1;function g(){_()?(h=!0,S(),i.emit(n.FULLSCREEN_CHANGE,{entered:!0})):h&&!T()&&(S(!0),i.emit(n.FULLSCREEN_CHANGE,{entered:!1})),y()&&!T()||(h=!1)}function b(){i.emit(n.FULLSCREEN_ERROR,{reason:"denied"})}function _(){return y()===o}function T(){var e=y();return!!e&&e!==o}function y(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement}function S(e){e||o.dataset.fullscreenCSS?(o.style.cssText=s,o.style.setProperty("border-width",u),o.style.setProperty("background-color",d,"important"),o.style.setProperty("padding",c),o.style.setProperty("padding-top",f),o.style.setProperty("padding-bottom",E),o.style.setProperty("padding-left",p),o.style.setProperty("padding-right",m),o.dataset.fullscreenCSS=!1):(s=o.style.cssText,o.style.width="100vw",o.style.height="100vh",u=o.style.borderWidth,o.style.setProperty("border-width","0px","important"),o.style.setProperty("background-color",l,"important"),c=o.style.padding,f=o.style.paddingTop,E=o.style.paddingBottom,p=o.style.paddingLeft,m=o.style.paddingRight,o.style.setProperty("padding","0","important"),o.dataset.fullscreenCSS=!0)}function A(){}function v(){}return function(){var n;for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&document.addEventListener(e[n],g);for(n in t)Object.prototype.hasOwnProperty.call(t,n)&&document.addEventListener(t[n],b)}(),{change:function(){!function(){try{_()?document.exitFullScreen&&document.exitFullScreen():o.requestFullScreen&&o.requestFullScreen().then(A).catch(v)}catch(e){}}()},entered:_,enabled:function(){return document.fullscreenEnabled||document.mozFullScreenEnabled||document.webkitFullscreenEnabled||document.msFullscreenEnabled},destroy:function(){!function(){var n;for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&document.removeEventListener(e[n],g);for(n in t)Object.prototype.hasOwnProperty.call(t,n)&&document.removeEventListener(t[n],b)}(),_()&&(document.exitFullScreen&&document.exitFullScreen(),i.emit(n.FULLSCREEN_EXIT)),o=null}}}}}.apply(t,r))||(e.exports=i)},6471:function(e,t,n){var r,i;r=[n(1933),n(6627),n(1117),n(2833)],void 0===(i=function(e,t,n,r){"use strict";return{create:function(e,i){var a,o=document.getElementById(e),s={timeout:0,touchInProgress:!1},u=0;function d(){var e,n;if(o)if(o.addEventListener(r.CLICK,c),t.isIOS11||!t.isIOS&&t.mustUseHLS)a.addEventListener(r.CLICK,c),a.addEventListener("contextmenu",f);else{var i=o.querySelectorAll("video");for(n=0;n<i.length;n+=1)(e=i[n]).addEventListener(r.CLICK,c),e.addEventListener("contextmenu",f)}}function l(){var e,n;if(clearTimeout(u),u=0,o)if(o.removeEventListener(r.CLICK,c),t.isIOS11||!t.isIOS&&t.mustUseHLS)a.removeEventListener(r.CLICK,c),a.removeEventListener("contextmenu",f);else{var i=o.querySelectorAll("video");for(n=0;n<i.length;n+=1)(e=i[n]).removeEventListener(r.CLICK,c),e.removeEventListener("contextmenu",f)}}function c(e){e.preventDefault(),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0,e.currentTarget.id&&-1!==e.currentTarget.id.indexOf("controls-")||e.button&&0!==e.button||s.touchInProgress||(s.touchInProgress=!0,e.currentTarget.id&&-1!==e.currentTarget.id.indexOf("playButton-")?i.emit(n.PLAYBUTTON_SINGLE_CLICK):e.currentTarget.id&&-1!==e.currentTarget.id.indexOf("audio-")?i.emit(n.AUDIO_SINGLE_CLICK):i.emit(n.PLAYER_SINGLE_CLICK),s.timeout=setTimeout((function(){s.touchInProgress=!1,clearTimeout(s.timeout),s.timeout=0}),300))}function f(e){return e.preventDefault(),!1}return(t.isIOS11||!t.isIOS&&t.mustUseHLS)&&((a=document.createElement("div")).style.width="100%",a.style.height="100%",a.style.position="absolute",a.style.left=0,a.style.top=0,a.style.backgroundColor="transparent",a.style.border=0,a.style.display="block",a.style.visibility="visible",a.style.zIndex=2,a.id="clickWrapper-"+e,o.appendChild(a)),{update:function(){var t=document.getElementById("middleView-"+e);t&&t.firstChild&&t.firstChild.addEventListener(r.CLICK,c),l(),u=setTimeout(d,200)},destroy:function(){l(),s.timeout&&clearTimeout(s.timeout),s.timeout=0,a&&o.removeChild(a),a=null,o=null}}}}}.apply(t,r))||(e.exports=i)},1890:function(e,t,n){var r,i;r=[n(7176),n(6627),n(505)],void 0===(i=function(e,t,n){"use strict";return{create:function(r,i,a){a=a||n.LETTERBOX;var o,s,u=document.getElementById(r),d=[],l=i.width/i.height;function c(n){n=void 0===n?0:n;var c=((u=document.getElementById(r))&&(o=u.clientWidth,s=u.clientHeight),{width:o,height:s}),f=["100%","100%"],E=(c.width/c.height-l).toFixed(2),p=1,m=1;if(u){switch(a){case"letterbox":n%180&&(p=(p/l).toFixed(2),m=(m/l).toFixed(2));break;case"crop":E<0?p=m=(c.height/(c.width/l)).toFixed(2):E>=0&&(m=p=(c.width/(c.height*l)).toFixed(2)),n%180&&(p=(p*E).toFixed(2),m=(m*E).toFixed(2));break;case"fill":E<0?m=(c.height/(c.width/l)).toFixed(2):E>=0&&(p=(c.width/(c.height*l)).toFixed(2));break;case"original":f[0]=i.width+"px",f[1]=i.height+"px";break;case"resize":"fixed"!==u.style.position&&(f[0]=i.width+"px",f[1]=i.height+"px",u.style.width=f[0],u.style.height=f[1])}for(;d.length;)d.pop();var h;if(t.isIOS11||!t.isIOS&&t.mustUseHLS){var g=u.querySelectorAll("iframe");for(h=0;h<g.length;h+=1)g[h].contentDocument&&g[h].contentDocument.body&&g[h].contentDocument.body.firstChild&&d.push(g[h].contentDocument.body.firstChild)}else{var b=u.querySelectorAll("video");for(h=0;h<b.length;h+=1)d.push(b[h])}for(h=0;h<d.length;h+=1){var _=d[h];e.normalize(_,"transform","translate(-50%, -50%) scaleX("+p+") scaleY("+m+") rotate("+n+"deg)"),_.style.width=f[0],_.style.height=f[1]}}}return c(),{update:function(e,t){a=e||a,c(t)},destroy:function(){for(;d.length;)d.pop();u=null}}}}}.apply(t,r))||(e.exports=i)},383:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(8812)],void 0===(i=function(e,t,n,r,i,a){"use strict";return{create:function(o,s,u,d,l,c){var f,E,p,m="http://www.w3.org/2000/svg",h=t.create(l.symbolColor),g=h.get(),b=document.createElementNS(m,"path"),_=a.centerView.audio.paths,T=a.centerView.audio.viewBox,y=d(),S=c[0],A=c[1];l.buttonHighlighting&&(p="rect",E=1,f=document.createElementNS(m,p));var v=0;return S?n.setAttribute(b,"d",_.off):A>=.5?n.setAttribute(b,"d",_.up):A>0?n.setAttribute(b,"d",_.down):n.setAttribute(b,"d",_.muted),l.buttonHighlighting&&("rect"===p?(n.setAttribute(f,"x",E),n.setAttribute(f,"y",E),n.setAttribute(f,"width",T[2]-2*E),n.setAttribute(f,"height",T[3]-2*E),n.setAttribute(f,"rx",4),n.setAttribute(f,"ry",4),n.setAttribute(f,"fill",h.getGradientUrl("vertical"))):(n.setAttribute(f,"cx",T[2]/2),n.setAttribute(f,"cy",T[3]/2),n.setAttribute(f,"r",Math.min(T[2],T[3])-E),n.setAttribute(f,"fill",h.getGradientUrl("radial"))),n.setAttribute(f,"opacity","0"),g.appendChild(f)),n.setAttribute(g,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(g,"transform","scale(0.9)"),n.setAttribute(g,"viewBox",T.join(" ")),n.setAttribute(g,"fill",l.symbolColor),g.style.zIndex=1001,g.id="audio-"+o,g.addEventListener(r.MOUSE_ENTER,(function(){l.buttonHighlighting&&n.setAttribute(f,"opacity","0.15"),l.buttonAnimation&&n.setAttribute(g,"transform","scale(1.0)")})),g.addEventListener(r.MOUSE_LEAVE,(function(){l.buttonHighlighting&&n.setAttribute(f,"opacity","0"),l.buttonAnimation&&n.setAttribute(g,"transform","scale(0.9)")})),g.appendChild(b),g.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){l.buttonAnimation&&n.setAttribute(g,"transform","scale(0.8)")})),g.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(v&&clearTimeout(v)&&(v=0),v=setTimeout((function(){g&&g&&-1!==n.getAttribute(g,"transform").indexOf("0.8")&&n.setAttribute(g,"transform","scale(1.0)"),S?s.emit(i.UNMUTE):s.emit(i.MUTE)}),50))})),u.appendChild(g),u.style.width=y.width+"px",u.style.height=y.height+"px",u.style.cursor=l.buttonCursor,{destroy:function(){v&&clearTimeout(v)&&(v=0),null!==u&&null!==g&&null!==g.parentNode&&g.parentNode===u&&u.removeChild(g),g=null}}}}}.apply(t,r))||(e.exports=i)},2911:function(e,t,n){var r,i;r=[n(7251),n(3740),n(8812)],void 0===(i=function(e,t,n){"use strict";return{create:function(r,i,a,o,s){var u=e.create(s.symbolColor).get(),d=document.createElementNS("http://www.w3.org/2000/svg","path"),l=n.centerView.error.paths,c=n.centerView.error.viewBox,f=o();return t.setAttribute(d,"d",l.error),t.setAttribute(u,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),t.setAttribute(u,"transform","scale(0.9)"),t.setAttribute(u,"viewBox",c.join(" ")),t.setAttribute(u,"fill",s.symbolColor),u.style.zIndex=1001,u.id="error-"+r,u.appendChild(d),a.appendChild(u),a.style.width=f.width+"px",a.style.height=f.height+"px",a.style.cursor="default",{destroy:function(){null!==a&&null!==u&&null!==u.parentNode&&u.parentNode===a&&a.removeChild(u),u=null}}}}}.apply(t,r))||(e.exports=i)},79:function(e,t,n){var r,i;r=[n(6627),n(7251),n(3740),n(8812)],void 0===(i=function(e,t,n,r){"use strict";return{create:function(i,a,o,s,u){var d,l="http://www.w3.org/2000/svg",c=t.create(u.symbolColor).get(),f=document.createElementNS(l,"path"),E=document.createElementNS(l,"path"),p=document.createElementNS(l,"path"),m=document.createElementNS(l,"animate"),h=document.createElementNS(l,"animateTransform"),g=document.createElementNS(l,"animateTransform"),b=r.centerView.loading.paths,_=r.centerView.loading.viewBox,T=s(),y=e.isTridentBrowser,S="quarter";function A(){clearInterval(d),d=null}return y||(m.setAttribute("attributeName","opacity"),m.setAttribute("dur","1s"),m.setAttribute("values","0;0.5;0"),m.setAttribute("repeatCount","indefinite"),f.appendChild(m)),f.setAttribute("opacity","0"),f.setAttribute("d",b.circleFull),c.appendChild(f),-1!==S.indexOf("half")&&(y||(h.setAttribute("id","first"),h.setAttribute("attributeName","transform"),h.setAttribute("attributeType","XML"),h.setAttribute("type","rotate"),h.setAttribute("dur",.66),h.setAttribute("from","0 12 12"),h.setAttribute("to","360 12 12"),h.setAttribute("begin","0s"),h.setAttribute("repeatCount","indefinite"),E.appendChild(h)),E.setAttribute("opacity","1.0"),E.setAttribute("d",b.circleHalf),c.appendChild(E)),-1!==S.indexOf("quarter")&&(y||(h.setAttribute("id","first"),h.setAttribute("attributeName","transform"),h.setAttribute("attributeType","XML"),h.setAttribute("type","rotate"),h.setAttribute("dur",.66),h.setAttribute("from","0 12 12"),h.setAttribute("to","360 12 12"),h.setAttribute("begin","0s;second.end"),p.appendChild(h),g.setAttribute("id","second"),g.setAttribute("attributeName","transform"),g.setAttribute("attributeType","XML"),g.setAttribute("type","rotate"),g.setAttribute("dur",.33),g.setAttribute("from","360 12 12"),g.setAttribute("to","720 12 12"),g.setAttribute("begin","first.end"),p.appendChild(g)),p.setAttribute("opacity","1.0"),p.setAttribute("d",b.circleQuarter),c.appendChild(p)),n.setAttribute(c,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(c,"transform","scale(0.9)"),n.setAttribute(c,"viewBox",_.join(" ")),n.setAttribute(c,"fill",u.symbolColor),c.style.zIndex=1001,c.id="loading-"+i,o.appendChild(c),o.style.width=T.width+"px",o.style.height=T.height+"px",o.style.cursor="default",y&&function(){A();var e=0,t=0,r=1,i=0,a=function(){(e+=9.090909090909092*(i%2+1))>=360&&(i++,e=0),t>=.5&&1===r?r=-1:t<=0&&-1===r&&(r=1),t+=.016666666666666666*r,n.setAttribute(c,"style","transform: rotate("+e+"deg)"),f.setAttribute("opacity",t)};d=setInterval(a,1e3/60),a()}(),{destroy:function(){y&&A(),c.parentNode===o&&o.removeChild(c),c=null}}}}}.apply(t,r))||(e.exports=i)},729:function(e,t,n){var r,i;r=[n(7176),n(4869),n(79),n(862),n(2911),n(383)],void 0===(i=function(e,t,n,r,i,a){"use strict";return{create:function(o,s,u){var d={loading:n,playbutton:r,error:i,audio:a},l=null,c=document.getElementById(o),f="middleView-"+o,E=document.createElement("div"),p={symbolColor:u.style.symbolColor,buttonAnimation:u.style.buttonAnimation,buttonHighlighting:u.style.buttonHighlighting,buttonCursor:u.style.buttonCursor},m=0;function h(){E&&E.parentNode===c&&c.removeChild(E)}function g(){return(c=document.getElementById(o))&&(m=Math.round(.2*Math.min(c.clientWidth,c.clientHeight))),{width:m,height:m}}return{update:function(n,r){l&&l.destroy&&(l.destroy(),l=null),n&&n!==t.NONE?(function(){if(!document.getElementById(f)){var t=g();E.id=f,E.style.width=t.width+"px",E.style.height=t.height+"px",E.style.position="absolute",E.style.left="50%",E.style.top="50%",E.style.marginRight="-50%",e.normalize(E,"transform","translate(-50%, -50%)"),E.style.zIndex=1e3,c&&c.appendChild(E)}}(),l=d[n].create(o,s,E,g,p,r)):h()},destroy:function(){l&&l.destroy&&(l.destroy(),l=null),h(),E=null,c=null}}}}}.apply(t,r))||(e.exports=i)},862:function(e,t,n){var r,i;r=[n(1933),n(7251),n(3740),n(2833),n(5403),n(8812)],void 0===(i=function(e,t,n,r,i,a){"use strict";return{create:function(o,s,u,d,l){var c,f,E,p="http://www.w3.org/2000/svg",m=t.create(l.symbolColor),h=m.get(),g=document.createElementNS(p,"path"),b=a.centerView.playButton.paths,_=a.centerView.playButton.viewBox,T=d();l.buttonHighlighting&&(E="rect",f=1,c=document.createElementNS(p,E));var y=0;return n.setAttribute(g,"d",b.play),l.buttonHighlighting&&("rect"===E?(n.setAttribute(c,"x",f),n.setAttribute(c,"y",f),n.setAttribute(c,"width",_[2]-2*f),n.setAttribute(c,"height",_[3]-2*f),n.setAttribute(c,"rx",4),n.setAttribute(c,"ry",4),n.setAttribute(c,"fill",m.getGradientUrl("vertical"))):(n.setAttribute(c,"cx",_[2]/2),n.setAttribute(c,"cy",_[3]/2),n.setAttribute(c,"r",Math.min(_[2],_[3])-f),n.setAttribute(c,"fill",m.getGradientUrl("radial"))),n.setAttribute(c,"opacity","0"),h.appendChild(c)),n.setAttribute(h,"style","-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;"),n.setAttribute(h,"transform","scale(0.9)"),n.setAttribute(h,"viewBox",_.join(" ")),n.setAttribute(h,"fill",l.symbolColor),h.style.zIndex=1001,h.id="playButton-"+o,h.addEventListener(r.MOUSE_ENTER,(function(){l.buttonHighlighting&&n.setAttribute(c,"opacity","0.15"),l.buttonAnimation&&n.setAttribute(h,"transform","scale(1.0)")})),h.addEventListener(r.MOUSE_LEAVE,(function(){l.buttonHighlighting&&n.setAttribute(c,"opacity","0"),l.buttonAnimation&&n.setAttribute(h,"transform","scale(0.9)")})),h.appendChild(g),h.addEventListener(e.mobile?r.TOUCH_START:r.MOUSE_DOWN,(function(){l.buttonAnimation&&n.setAttribute(h,"transform","scale(0.8)")})),h.addEventListener(e.mobile?r.TOUCH_END:r.MOUSE_UP,(function(e){e&&void 0!==e.button&&0!==e.button||(y&&clearTimeout(y)&&(y=0),y=setTimeout((function(){h&&-1!==n.getAttribute(h,"transform").indexOf("0.8")&&n.setAttribute(h,"transform","scale(1.0)"),s.emit(i.PLAY)}),50))})),u.appendChild(h),u.style.width=T.width+"px",u.style.height=T.height+"px",u.style.cursor=l.buttonCursor,{destroy:function(){y&&clearTimeout(y)&&(y=0),null!==u&&null!==h&&null!==h.parentNode&&h.parentNode===u&&u.removeChild(h),h=null}}}}}.apply(t,r))||(e.exports=i)},995:function(e,t,n){var r,i;r=[n(1933),n(6627),n(7176),n(9583),n(492),n(6637)],void 0===(i=function(e,t,n,r,i,a){"use strict";return{create:function(e,t,o){var s,u,d,l=[{type:r.STATE_CHANGE,listener:function(e){switch(e.state){case i.BUFFERING:case i.PLAYING:d&&(d.style.visibility="hidden");break;case i.READY:d&&(d.style.visibility="visible");break;case i.LOADING:s===i.READY&&d&&(d.style.visibility="visible");break;case i.PAUSED:!p&&d&&(d.style.visibility="visible")}s=e.state}}],c=o.style,f=c.poster,E="string"==typeof f&&f.length,p=c.keepFrame,m=o.playback.crossOrigin,h=16,g=9,b=I(),_=["100%","100%"],T=b.width/b.height,y=h/g,S=(T-y).toFixed(2),A=1,v=1,R=0,O=0;function I(){return t&&(R=t.clientWidth,O=t.clientHeight),{width:R,height:O}}function N(){E&&u&&(b=I(),T=b.width/b.height,S=Math.round(100*(T-y))/100,v=A=1,S<0?v=Math.round(T/y*100)/100:S>=0&&(A=Math.round(y/T*100)/100),n.normalize(u,"transform","translate(-50%, -50%) scaleX("+A+") scaleY("+v+") rotate(0deg)"))}function C(){u&&(u.parentNode.removeChild(u),u=null),d&&(d.parentNode.removeChild(d),d=null),E=!1}function w(){C()}function D(){u&&u.naturalWidth&&u.naturalHeight&&(h=u.naturalWidth,g=u.naturalHeight,y=h/g,N())}return a.add({target:e,listeners:l}),function(){if(E){switch((d=document.createElement("div")).style.width=_[0],d.style.height=_[1],d.style.top=0,d.style.left=0,d.style.position="absolute",d.style.zIndex=999,d.style.visibility="visible",d.style.backgroundColor=c.backgroundColor,(u=document.createElement("img")).style.width=_[0],u.style.height=_[1],u.style.position="absolute",u.style.left="50%",u.style.top="50%",u.style.marginRight="-50%",u.style.backgroundColor=c.backgroundColor,n.normalize(u,"transform","translate(-50%, -50%)"),u.style.zIndex=999,m){case"anonymous":case"use-credentials":u.setAttribute("crossOrigin",m)}u.onerror=w,u.onload=D,u.src=f,d.appendChild(u),t.appendChild(d),N()}}(),{update:function(){E&&N()},destroy:function(){C(),a.remove({target:e,listeners:l})}}}}}.apply(t,r))||(e.exports=i)},7661:function(e,t,n){var r,i;r=[n(1933),n(6627),n(7176),n(9583),n(492),n(6637),n(6182),n(995)],void 0===(i=function(e,t,n,r,i,a,o,s){"use strict";return{create:function(u,d,l){var c,f,E,p=[],m=l.style,h=!1,g=[{type:r.STATE_CHANGE,listener:function(e){f=e.state;for(var t=0;t<p.length;t+=1){var n=p[t];m.audioPlayer?n.style.visibility="hidden":f===i.READY||f===i.PAUSED?!m.keepFrame&&(n.style.visibility="hidden"):f===i.PLAYING&&!m.keepFrame&&(n.style.visibility="visible")}}},{type:r.STREAM_INFO,listener:function(){for(var e=0;e<p.length;e+=1){var t=p[e];m.audioPlayer&&(t.style.visibility="hidden")}}},{type:o.FULLSCREEN_CHANGE,listener:function(e){h=e.data.entered;for(var t=0;t<p.length;t+=1){var n=p[t];h?n.style.setProperty("background-color",m.fullScreenBackgroundColor):n.style.setProperty("background-color",m.backgroundColor)}}}],b=!1;return function(t){var n,r,i,a,o,f=["width","height"],p=0;(c=document.getElementById(d))&&("auto"!==t.width&&"auto"!==t.height?t.audioPlayer?t.width&&t.height?(c.style.width=t.width,c.style.height=t.height):t.width?(c.style.width=t.width,c.style.height=Math.round(e.mobile?100/9:100/12)/100*c.clientWidth+"px"):c.clientWidth||c.clientHeight?c.clientWidth&&(c.style.height=Math.round(e.mobile?100/9:100/12)/100*c.clientWidth+"px"):(c.style.width="640px",c.style.height=Math.round(e.mobile?100/9:100/12)/100*c.clientWidth+"px"):t.width&&t.height?(c.style.width=t.width,c.style.height=t.height):(t.width||t.height)&&t.aspectratio?(i=-1!==t[f[p=+!!t.height]].indexOf("%"),a=-1!==t[f[p]].indexOf("px"),c.style[f[p]]=t[f[p]],(o=t.aspectratio.split("/"))[0]=parseInt(o[0],10),o[1]=parseInt(o[1],10),(i||a)&&(n=parseInt(t[f[p]].replace(i?"%":"px",""),10),r=Math.round(n*o[+!p]/o[p]).toString()+(i?"%":"px"),c.style[f[+!p]]=r)):(t.width||t.height)&&(t.width||t.aspectratio)&&(t.aspectratio||t.height)||c.clientWidth&&c.clientHeight||(c.style.width="640px",c.style.height="360px"):c.clientWidth||c.clientHeight?c.clientWidth||"auto"!==t.width?c.clientHeight||(t.aspectratio&&!t.audioPlayer?((o=t.aspectratio.split("/"))[0]=parseInt(o[0],10),o[1]=parseInt(o[1],10),c.style.height=Math.round(c.clientWidth/o[0]*o[1]).toString()+"px"):c.style.height=t.audioPlayer?Math.round(e.mobile?100/6:100/12)/100*c.clientWidth+"px":Math.round(c.clientWidth/16*9).toString()+"px"):t.aspectratio?((o=t.aspectratio.split("/"))[0]=parseInt(o[0],10),o[1]=parseInt(o[1],10),c.style.width=Math.round(c.clientHeight*o[0]/o[1]).toString()+"px"):c.style.width=Math.round(16*c.clientHeight/9).toString()+"px":(c.style.width="640px",c.style.height=t.audioPlayer?Math.round(e.mobile?100/6:100/12)/100*c.clientWidth+"px":"360px"),c.style.overflow="hidden",c.style.position="relative",c.style.backgroundColor=t.audioPlayer?"transparent":t.backgroundColor?t.backgroundColor:"black",E=s.create(u,c,l))}(m),{update:function(){for(b||(a.add({target:u,listeners:g}),b=!0);p.length;)p.pop();var e;if(c){if(t.isIOS11||!t.isIOS&&t.mustUseHLS){var r=c.querySelectorAll("iframe");for(e=0;e<r.length;e+=1)r[e].contentDocument&&r[e].contentDocument.body&&r[e].contentDocument.body&&p.push(r[e].contentDocument.body.firstChild)}else{var i=c.querySelectorAll("video");for(e=0;e<i.length;e+=1)p.push(i[e])}for(e=0;e<p.length;e+=1){var o=p[e];o.style.width="100%",o.style.height="100%",o.style.position="absolute",o.style.left="50%",o.style.top="50%",o.style.marginRight="-50%",o.style.backgroundColor=m.audioPlayer?"transparent":h?m.fullScreenBackgroundColor:m.backgroundColor,n.normalize(o,"transform","translate(-50%, -50%)")}E.update()}},destroy:function(){for(E.destroy(),E=null,a.remove({target:u,listeners:g}),c=null;p.length;)p.pop()}}}}}.apply(t,r))||(e.exports=i)},3740:function(e,t,n){var r,i;r=[n(6627),n(8333)],void 0===(i=function(e,t){"use strict";var n=t.create("Attribute");function r(e,t){var r=!!(e&&1===e.nodeType&&t&&"string"==typeof t&&t.length);return!r&&n.debugEnabled()&&n.debug("element not existing or invalid property"),r}return{setAttribute:function(t,n,i){if(r(t,n))try{e.isTridentBrowser&&"transform"===n?t.style.transform=i:t.setAttribute(n,i)}catch(e){}},getAttribute:function(t,n){var i=void 0;if(r(t,n))try{i=e.isTridentBrowser&&"transform"===n?t.style.transform:t.getAttribute(n)}catch(e){}return i}}}.apply(t,r))||(e.exports=i)},7251:function(e,t,n){var r,i;r=[n(3740)],void 0===(i=function(e){"use strict";return{create:function(t){var n="http://www.w3.org/2000/svg",r=document.createElementNS(n,"svg"),i=document.createElementNS(n,"defs"),a=document.createElementNS(n,"radialGradient"),o=document.createElementNS(n,"linearGradient"),s=document.createElementNS(n,"linearGradient"),u=document.createElementNS(n,"stop"),d=document.createElementNS(n,"stop"),l=document.createElementNS(n,"stop"),c=document.createElementNS(n,"stop"),f=document.createElementNS(n,"stop"),E=document.createElementNS(n,"stop"),p=document.createElementNS(n,"stop"),m=document.createElementNS(n,"stop"),h=document.createElementNS(n,"stop"),g=document.createElementNS(n,"stop"),b=document.createElementNS(n,"stop"),_=document.createElementNS(n,"stop");return u.setAttribute("offset","0%"),u.setAttribute("stop-color",t),d.setAttribute("offset","30%"),d.setAttribute("stop-color",t),l.setAttribute("stop-opacity","1"),l.setAttribute("offset","70%"),l.setAttribute("stop-color",t),l.setAttribute("stop-opacity","1"),c.setAttribute("offset","100%"),c.setAttribute("stop-color",t),c.setAttribute("stop-opacity","0"),f.setAttribute("offset","0%"),f.setAttribute("stop-color",t),E.setAttribute("offset","80%"),E.setAttribute("stop-color",t),p.setAttribute("offset","90%"),p.setAttribute("stop-color",t),p.setAttribute("stop-opacity","0.1"),m.setAttribute("offset","100%"),m.setAttribute("stop-color",t),m.setAttribute("stop-opacity","0"),h.setAttribute("offset","0%"),h.setAttribute("stop-color",t),b.setAttribute("stop-opacity","0"),g.setAttribute("offset","10%"),g.setAttribute("stop-color",t),b.setAttribute("stop-opacity","0.15"),b.setAttribute("offset","90%"),b.setAttribute("stop-color",t),b.setAttribute("stop-opacity","0.15"),_.setAttribute("offset","100%"),_.setAttribute("stop-color",t),_.setAttribute("stop-opacity","0"),a.setAttribute("id","hoverTransparencyRadial"),o.setAttribute("id","hoverTransparencyHorizontal"),s.setAttribute("id","hoverTransparencyVertical"),s.setAttribute("x1","0"),s.setAttribute("y1","0"),s.setAttribute("x2","0"),s.setAttribute("y2","1"),a.appendChild(u),a.appendChild(d),a.appendChild(l),a.appendChild(c),o.appendChild(f),o.appendChild(E),o.appendChild(p),o.appendChild(m),s.appendChild(h),s.appendChild(g),s.appendChild(b),s.appendChild(_),i.appendChild(a),i.appendChild(o),i.appendChild(s),e.setAttribute(r,"version","1.1"),e.setAttribute(r,"xmlns",n),e.setAttribute(r,"xmlns:xlink","http://www.w3.org/1999/xlink"),e.setAttribute(r,"enable-background","new 0 0 0 0"),e.setAttribute(r,"xml:space","preserve"),e.setAttribute(r,"x","0px"),e.setAttribute(r,"y","0px"),r.appendChild(i),{get:function(){return r},getGradientUrl:function(e){var t="";switch(e=e||"radial"){case"horizontal":t=o.getAttribute("id");break;case"vertical":t=s.getAttribute("id");break;case"radial":t=a.getAttribute("id")}return"url(#"+t+")"}}}}}.apply(t,r))||(e.exports=i)},8143:function(e,t){var n;void 0===(n=function(){"use strict";return{format:function(e){var t=Math.floor(e/3600),n=Math.floor((e-3600*t)/60);e=Math.floor(e-3600*t-60*n);var r="";return 0!==t&&(r=t+":"),(r+=(n=n<10&&""!==r?"0"+n:String(n))+":")+(e<10?"0"+e:String(e))}}}.apply(t,[]))||(e.exports=n)},5623:function(e,t){var n;void 0===(n=function(){"use strict";return{isColor:function(e){var t=/^[a-zA-Z]*$/.test(e)&&1===["AliceBlue","AntiqueWhite","Aqua","Aquamarine","Azure","Beige","Bisque","Black","BlanchedAlmond","Blue","BlueViolet","Brown","BurlyWood","CadetBlue","Chartreuse","Chocolate","Coral","CornflowerBlue","Cornsilk","Crimson","Cyan","DarkBlue","DarkCyan","DarkGoldenRod","DarkGray","DarkGreen","DarkKhaki","DarkMagenta","DarkOliveGreen","Darkorange","DarkOrchid","DarkRed","DarkSalmon","DarkSeaGreen","DarkSlateBlue","DarkSlateGray","DarkTurquoise","DarkViolet","DeepPink","DeepSkyBlue","DimGray","DodgerBlue","FireBrick","FloralWhite","ForestGreen","Fuchsia","Gainsboro","GhostWhite","Gold","GoldenRod","Gray","Green","GreenYellow","HoneyDew","HotPink","IndianRed","Indigo","Ivory","Khaki","Lavender","LavenderBlush","LawnGreen","LemonChiffon","LightBlue","LightCoral","LightCyan","LightGoldenRodYellow","LightGrey","LightGreen","LightPink","LightSalmon","LightSeaGreen","LightSkyBlue","LightSlateGray","LightSteelBlue","LightYellow","Lime","LimeGreen","Linen","Magenta","Maroon","MediumAquaMarine","MediumBlue","MediumOrchid","MediumPurple","MediumSeaGreen","MediumSlateBlue","MediumSpringGreen","MediumTurquoise","MediumVioletRed","MidnightBlue","MintCream","MistyRose","Moccasin","NavajoWhite","Navy","OldLace","Olive","OliveDrab","Orange","OrangeRed","Orchid","PaleGoldenRod","PaleGreen","PaleTurquoise","PaleVioletRed","PapayaWhip","PeachPuff","Peru","Pink","Plum","PowderBlue","Purple","Red","RosyBrown","RoyalBlue","SaddleBrown","Salmon","SandyBrown","SeaGreen","SeaShell","Sienna","Silver","SkyBlue","SlateBlue","SlateGray","Snow","SpringGreen","SteelBlue","Tan","Teal","Thistle","Tomato","Turquoise","Violet","Wheat","White","WhiteSmoke","Yellow","YellowGreen"].filter((function(t){return t.toLowerCase()===e.toLowerCase()})).length,n=/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3}|[a-fA-F0-9]{8}|[a-fA-F0-9]{4})$/.test(e),r=/^[Rr][Gg][Bb]\(\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*,\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*,\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*\)$/.test(e),i=/^[Rr][Gg][Bb][Aa]\(\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*,\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*,\s*(0|[1-9]\d?|1\d\d?|2[0-4]\d|25[0-5])%?\s*,\s*((0.[1-9])|[01])\s*\)$/.test(e),a=/^[Hh][Ss][Ll]\(\s*(0|[1-9]\d?|[12]\d\d|3[0-5]\d)\s*,\s*((0|[1-9]\d?|100)%)\s*,\s*((0|[1-9]\d?|100)%)\s*\)$/.test(e),o=/^[Hh][Ss][Ll][Aa]\(\s*(0|[1-9]\d?|[12]\d\d|3[0-5]\d)\s*,\s*((0|[1-9]\d?|100)%)\s*,\s*((0|[1-9]\d?|100)%)\s*,\s*((0.[1-9])|[01])\s*\)$/.test(e);return t||n||r||i||a||o}}}.apply(t,[]))||(e.exports=n)},1583:function(e,t,n){var r,i;r=[n(1933),n(1473),n(9088),n(9658),n(6627),n(9583),n(5623),n(2058),n(5109),n(4355),n(5459),n(2833),n(6182),n(8741),n(9087),n(1117),n(5403),n(4869),n(6471),n(7661),n(729),n(1890),n(4103),n(6643)],void 0===(i=function(e,t,n,r,i,a,o,s,u,d,l,c,f,E,p,m,h,g,b,_,T,y,S,A){"use strict";function v(){this.version=""}var R=v.prototype=Object.create(r.prototype);return R.setBaseValues=function(e){console.debug("NanoView api version: "+this.version),this.config=s.create(),this._playerDivId=e,this._emitter=new r,this._muted=!1,this._volume=100,this._handleAutoplay=!1,this._isRestarting=!1,this.state=l.IDLE},R.baseSetup=function(e){this._validate(e.style),t.extend(e,this.config),t.merge(e,this.config),t.clean(this.config),this.registerExternalEventHandler(),(i.mustUseHLS||i.canUseHLS&&this.config.playback.allowSafariHlsFallback)&&(this.config.style.keepFrame=!1),this.config.style.view?(this._style=_.create(this._emitter,this._playerDivId,this.config),this.config.style.audioPlayer||(this.config.style.centerView&&(this._middleView=T.create(this._playerDivId,this._emitter,this.config)),this._resizeListener=d.create(this._playerDivId,this._emitter)),this.config.style.controls&&(this._controls=A.create(this._playerDivId,this._emitter,this.config)),this.config.style.audioPlayer||(this._fullscreen=S.create(this._playerDivId,this._emitter,this.config))):this._fullscreen=S.create(this._playerDivId,this._emitter,this.config)},R.postSetup=function(){this.config.style.view&&(this._style.update(),this.config.style.interactive&&!this.config.style.audioPlayer?(this._interactive=b.create(this._playerDivId,this._emitter),this._interactive.update()):this.config.style.controls&&this._controls.show()),this.registerInternalEventHandler()},R.registerExternalEventHandler=function(){for(var e in f)Object.prototype.hasOwnProperty.call(f,e)&&this._emitter.on(f[e],this._emit.bind(this,f[e]))},R.registerInternalEventHandler=function(){var e,t;if(this._interactive&&(this._emitter.on(m.PLAYBUTTON_SINGLE_CLICK,function(){this.state!==l.READY&&this.state!==l.PAUSED?this._emitter.emit(f.PAUSE):this._emitter.emit(f.PLAY)}.bind(this)),this._emitter.on(m.AUDIO_SINGLE_CLICK,function(){this._muted?this._emitter.emit(f.UNMUTE):this._emitter.emit(f.MUTE)}.bind(this)),this._emitter.on(m.PLAYER_SINGLE_CLICK,function(){this._controls&&this._controls.appear()}.bind(this))),this._controls)for(e in t=[h.FULLSCREEN_CHANGE],h)Object.prototype.hasOwnProperty.call(h,e)&&Object.prototype.hasOwnProperty.call(f,e)&&(-1===t.indexOf(h[e])?this._emitter.on(h[e],this._emitter.emit.bind(this._emitter,f[e])):this._fullscreen&&this._emitter.on(h[e],function(){this._fullscreen&&this._fullscreen.change()}.bind(this)));if(this._fullscreen)for(e in t=[p.FULLSCREEN_ERROR],p)Object.prototype.hasOwnProperty.call(p,e)&&-1===t.indexOf(p[e])&&(this._emitter.on(p[e],this._emitter.emit.bind(this._emitter,f[e])),this._emitter.on(p[e],this.handleFullscreen.bind(this)));this._resizeListener&&(this._emitter.on(f.PLAYER_RESIZE,this._onPlayerResize.bind(this)),this._onPlayerResize(null))},R.removeExternalEventHandler=function(){if(this._emitter)for(var e in f)Object.prototype.hasOwnProperty.call(f,e)&&this._emitter.removeAllListeners(f[e])},R.removeInternalEventHandler=function(){if(this._emitter){var e;if(this._interactive&&(this._emitter.removeAllListeners(m.PLAYER_SINGLE_CLICK),this._emitter.removeAllListeners(m.PLAYBUTTON_SINGLE_CLICK),this._emitter.removeAllListeners(m.AUDIO_SINGLE_CLICK)),this._controls)for(e in h)Object.prototype.hasOwnProperty.call(h,e)&&Object.prototype.hasOwnProperty.call(f,e)&&this._emitter.removeAllListeners(h[e]);if(this._fullscreen)for(e in p)Object.prototype.hasOwnProperty.call(p,e)&&this._emitter.removeAllListeners(p[e]);this._resizeListener&&this._emitter.removeAllListeners(f.PLAYER_RESIZE)}},R.destroy=function(){this.removeExternalEventHandler(),this.removeInternalEventHandler(),this._emitter=null,this._resizeListener&&this._resizeListener.destroy(),this._resizeListener=null,this._style&&this._style.destroy(),this._style=null,this._interactive&&this._interactive.destroy(),this._interactive=null,this._scaling&&this._scaling.destroy(),this._scaling=null,this._middleView&&this._middleView.destroy(),this._middleView=null,this._controls&&this._controls.destroy(),this._controls=null,this._fullscreen&&this._fullscreen.destroy(),this._fullscreen=null},R.update=function(e){this.state=e.state,this._emitter.emit(a.STATE_CHANGE,e),this.handleView(e.state),this._interactive&&this._interactive.update()},R.handleView=function(e){switch(this._viewTimeout&&clearTimeout(this._viewTimeout),e){case l.UNINITIALIZED:this._middleView&&this._middleView.update(g.ERROR),this._controls&&this._controls.destroy();break;case l.IDLE:this._middleView&&this._middleView.update(g.NONE);break;case l.READY:this._middleView&&this._middleView.update(g.PLAYBUTTON),this._controls&&this._controls.play();break;case l.LOADING:this._middleView&&this._middleView.update(g.LOADING),this._controls&&this._controls.pause();break;case l.PLAYING:this.handleAudio(),this._controls&&this._controls.pause();break;case l.PAUSED:this._isRestarting||(this._middleView&&this._middleView.update(g.PLAYBUTTON),this._controls&&this._controls.play()),this._isRestarting=!1;break;case l.BUFFERING:this._viewTimeout=setTimeout(function(){this._middleView&&this._middleView.update(g.LOADING),this._controls&&this._controls.pause()}.bind(this),1e3);break;case l.UNKNOWN:this._middleView&&this._middleView.update(g.NONE);break;case l.PLAYBACK_RESTARTING:this._isRestarting=!0,this._middleView&&this._middleView.update(g.NONE)}this.state=e},R.handleConfig=function(e){this._handleAutoplay=e.playback.autoplay},R.handleStreamInfo=function(e){this._emitter.emit(a.STREAM_INFO,e),this.progressStreamInfo(e)},R.handleStreamInfoUpdate=function(e){this._emitter.emit(a.STREAM_INFO_UPDATE,e),this.progressStreamInfo(e)},R.progressStreamInfo=function(e){this.streamInfo=e.data.streamInfo,this._style&&this._style.update(),this._interactive&&this._interactive.update(),this._scaling&&this._scaling.destroy()&&(this._scaling=null),this.streamInfo.haveVideo&&(this._scaling=y.create(this._playerDivId,this.streamInfo.videoInfo,this.config.style.scaling))},R.handleAudio=function(){var e=this.state===l.PLAYING,t=this.streamInfo&&this.streamInfo.haveAudio;e&&!this._muted&&t&&(this._handleAutoplay=!1);var n=this.config.style.displayMutedAutoplay&&this._muted&&this._handleAutoplay,r=this.config.style.displayAudioOnly&&this.streamInfo&&!this.streamInfo.haveVideo;e&&(t&&n||r?this._middleView&&this._middleView.update(g.AUDIO,[this._muted,this._volume]):this._middleView&&this._middleView.update(g.NONE))},R.handleMute=function(e,t){this._controls&&(e?this._controls.mute(t):this._controls.unmute(t)),this._muted=e,this.handleAudio()},R.handleVolume=function(e){this._controls&&this._controls.volume(e),this._volume=e,this.handleMute(this._muted,this._volume)},R.handleFullscreen=function(e){this._controls&&this._controls.fullscreen(e.data.entered)},R.handleStats=function(e){this._controls&&this._controls.time(e.currentTime)},R.handleMetaData=function(e){if("object"==typeof e.data.message&&Object.prototype.hasOwnProperty.call(e.data.message,"nanoStreamStatus")){var t="object"==typeof e.data.message.nanoStreamStatus&&Object.prototype.hasOwnProperty.call(e.data.message.nanoStreamStatus,"VideoRotation")?e.data.message.nanoStreamStatus.VideoRotation:0;this._scaling&&this._scaling.update(this.config.style.scaling,t)}},R.requestFullscreen=function(){return new Promise(function(e,t){function n(){this._emitter.off(p.FULLSCREEN_ERROR,r),e()}function r(e){this._emitter.off(p.FULLSCREEN_CHANGE,n),t({reason:e.data.reason})}this._fullscreen&&this._fullscreen.enabled()?this._fullscreen.entered()?n.call(this):(this._emitter.once(p.FULLSCREEN_CHANGE,n.bind(this)),this._emitter.once(p.FULLSCREEN_ERROR,r.bind(this)),this._fullscreen.change()):r.call(this,{data:{reason:"disabled"}})}.bind(this))},R.exitFullscreen=function(){return new Promise(function(e,t){function n(){this._emitter.off(p.FULLSCREEN_ERROR,r),e()}function r(e){this._emitter.off(p.FULLSCREEN_CHANGE,n),t({reason:e.data.reason})}this._fullscreen&&this._fullscreen.enabled()?this._fullscreen.entered()?(this._emitter.once(p.FULLSCREEN_CHANGE,n.bind(this)),this._emitter.once(p.FULLSCREEN_ERROR,r.bind(this)),this._fullscreen.change()):n.call(this):r.call(this,{data:{reason:"disabled"}})}.bind(this))},R._emit=function(e,t){var n={};t&&t.name&&t.data?n=t:t?(n.data=t,n.name=e||"unknown"):(n.data={},n.name=e||"unknown"),n.player=this._playerDivId,n.state=this.state,this.emit(n.name,n)},R._validate=function(e){e.symbolColor&&!o.isColor(e.symbolColor)&&delete e.symbolColor,e.controlBarColor&&!o.isColor(e.controlBarColor)&&delete e.controlBarColor,"inherit"===e.fullScreenBackgroundColor&&(e.fullScreenBackgroundColor=e.backgroundColor)},R._onPlayerResize=function(){this.handleView(this.state),this._interactive&&this._interactive.update(),this._style&&this._style.update(),this._scaling&&this._scaling.update(),this._controls&&this._controls.size()},v.events=f,v.publicEvents=E,v.emptyConfig=s,v.validConfig=u,v}.apply(t,r))||(e.exports=i)}},t={};!function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}(9921)}();