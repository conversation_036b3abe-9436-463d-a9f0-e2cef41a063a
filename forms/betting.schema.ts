import { object, string } from 'yup'
import { BetType } from '~/interfaces/event'

export const betFreeForm = (t: any, betType: number) => {
    const createNumberValidation = () => 
        string()
            .test('all-validations', '', (value) => {
                if (!value) return true
                if (!/^\d+$/.test(value)) return false
                if (betType === BetType.LUCKY_NUMBER_1 && value.length !== 2) return false
                if (betType === BetType.LUCKY_NUMBER_2 && value.length !== 3) return false
                const num = parseInt(value)
                if (betType === BetType.LUCKY_NUMBER_1 && (num < 0 || num > 99)) return false
                if (betType === BetType.LUCKY_NUMBER_2 && (num < 0 || num > 999)) return false
                return true
            })

    const schema = betType === BetType.LUCKY_NUMBER_1 
        ? {
            numberOne: createNumberValidation(),
            numberTwo: createNumberValidation()

                .test('unique', '<PERSON><PERSON> chọn không được trùng nhau.', function(value) {
                    if (!value || !this.parent.numberOne) return true
                    return value !== this.parent.numberOne
                })
        }
        : {
            numberThree: createNumberValidation(),
            numberFour: createNumberValidation()
                .test('unique1', 'Số chọn không được trùng nhau.', function(value) {
                    if (!value || !this.parent.numberThree) return true
                    return value !== this.parent.numberThree
                }),
            numberFive: createNumberValidation()
                .test('unique2', 'Số chọn không được trùng nhau.', function(value) {
                    if (!value) return true
                    const { numberThree, numberFour } = this.parent
                    if (numberThree && value === numberThree) return false
                    if (numberFour && value === numberFour) return false
                    return true
                }),
        }

    return object().shape(schema)
}
