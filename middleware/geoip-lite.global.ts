export default defineNuxtRouteMiddleware(async (to, from) => {
    if (import.meta.server) {
        const headers = useRequestHeaders()
        let country = headers['cf-ipcountry']
        const rayId = headers['x-ray-id'] || headers['cf-ray'] || ''
        const ip =
            headers['x-forwarded-for'] ||
            headers['cf-connecting-ip'] ||
            useRequestHeaders()['x-real-ip'] ||
            'unknown'
        if (!country) {
            try {
                const { data } = await useFetch(
                    `https://api.country.is/${ip}`,
                    {
                        method: 'GET',
                    }
                )
                if (data.value) {
                    country = data.value?.country
                }
            } catch (error) {
                console.error('error', error)
            }
        }

        const blockedCountries = useRuntimeConfig().public.GEO_BLOCK_COUNTRIES
            ? useRuntimeConfig().public.GEO_BLOCK_COUNTRIES?.split(',')
            : ['AE', 'KH', 'TH', 'LA', 'PH']
        if (blockedCountries.includes(country)) {
            const error = new Error('Access Denied')
            error.statusCode = 403
            error.message = ip
            error.stack = rayId
            throw error
        }
    }
})
